/*! This file is auto-generated */
(()=>{"use strict";var e={7734:e=>{e.exports=function e(r,t){if(r===t)return!0;if(r&&t&&"object"==typeof r&&"object"==typeof t){if(r.constructor!==t.constructor)return!1;var n,o,s;if(Array.isArray(r)){if((n=r.length)!=t.length)return!1;for(o=n;0!=o--;)if(!e(r[o],t[o]))return!1;return!0}if(r instanceof Map&&t instanceof Map){if(r.size!==t.size)return!1;for(o of r.entries())if(!t.has(o[0]))return!1;for(o of r.entries())if(!e(o[1],t.get(o[0])))return!1;return!0}if(r instanceof Set&&t instanceof Set){if(r.size!==t.size)return!1;for(o of r.entries())if(!t.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(r)&&ArrayBuffer.isView(t)){if((n=r.length)!=t.length)return!1;for(o=n;0!=o--;)if(r[o]!==t[o])return!1;return!0}if(r.constructor===RegExp)return r.source===t.source&&r.flags===t.flags;if(r.valueOf!==Object.prototype.valueOf)return r.valueOf()===t.valueOf();if(r.toString!==Object.prototype.toString)return r.toString()===t.toString();if((n=(s=Object.keys(r)).length)!==Object.keys(t).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(t,s[o]))return!1;for(o=n;0!=o--;){var i=s[o];if(!e(r[i],t[i]))return!1}return!0}return r!=r&&t!=t}}},r={};function t(n){var o=r[n];if(void 0!==o)return o.exports;var s=r[n]={exports:{}};return e[n](s,s.exports,t),s.exports}t.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},t.d=(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r);var n={};(()=>{t.d(n,{default:()=>b});const e=window.wp.element,r=window.wp.data;var o=t(7734),s=t.n(o);const i=window.wp.compose,u=window.wp.i18n,c=window.wp.apiFetch;var l=t.n(c);const a=window.wp.url,f=window.wp.components,d=window.wp.blocks,p=window.ReactJSXRuntime,w={};function h({className:e}){return(0,p.jsx)(f.Placeholder,{className:e,children:(0,u.__)("Block rendered as empty.")})}function y({response:e,className:r}){const t=(0,u.sprintf)((0,u.__)("Error loading block: %s"),e.errorMsg);return(0,p.jsx)(f.Placeholder,{className:r,children:t})}function g({children:e,showLoader:r}){return(0,p.jsxs)("div",{style:{position:"relative"},children:[r&&(0,p.jsx)("div",{style:{position:"absolute",top:"50%",left:"50%",marginTop:"-9px",marginLeft:"-9px"},children:(0,p.jsx)(f.Spinner,{})}),(0,p.jsx)("div",{style:{opacity:r?"0.3":1},children:e})]})}function m(r){const{attributes:t,block:n,className:o,httpMethod:u="GET",urlQueryArgs:c,skipBlockSupportAttributes:f=!1,EmptyResponsePlaceholder:m=h,ErrorResponsePlaceholder:v=y,LoadingResponsePlaceholder:b=g}=r,x=(0,e.useRef)(!1),[j,S]=(0,e.useState)(!1),O=(0,e.useRef)(),[P,k]=(0,e.useState)(null),R=(0,i.usePrevious)(r),[A,M]=(0,e.useState)(!1);function T(){var e,r;if(!x.current)return;M(!0);const o=setTimeout((()=>{S(!0)}),1e3);let s=t&&(0,d.__experimentalSanitizeBlockAttributes)(n,t);f&&(s=function(e){const{backgroundColor:r,borderColor:t,fontFamily:n,fontSize:o,gradient:s,textColor:i,className:u,...c}=e,{border:l,color:a,elements:f,spacing:d,typography:p,...h}=e?.style||w;return{...c,style:h}}(s));const i="POST"===u,p=i?null:null!==(e=s)&&void 0!==e?e:null,h=function(e,r=null,t={}){return(0,a.addQueryArgs)(`/wp/v2/block-renderer/${e}`,{context:"edit",...null!==r?{attributes:r}:{},...t})}(n,p,c),y=i?{attributes:null!==(r=s)&&void 0!==r?r:null}:null,g=O.current=l()({path:h,data:y,method:i?"POST":"GET"}).then((e=>{x.current&&g===O.current&&e&&k(e.rendered)})).catch((e=>{x.current&&g===O.current&&k({error:!0,errorMsg:e.message})})).finally((()=>{x.current&&g===O.current&&(M(!1),S(!1),clearTimeout(o))}));return g}const _=(0,i.useDebounce)(T,500);(0,e.useEffect)((()=>(x.current=!0,()=>{x.current=!1})),[]),(0,e.useEffect)((()=>{void 0===R?T():s()(R,r)||_()}));const E=!!P,N=""===P,z=P?.error;return A?(0,p.jsx)(b,{...r,showLoader:j,children:E&&(0,p.jsx)(e.RawHTML,{className:o,children:P})}):N||!E?(0,p.jsx)(m,{...r}):z?(0,p.jsx)(v,{response:P,...r}):(0,p.jsx)(e.RawHTML,{className:o,children:P})}const v={},b=(0,r.withSelect)((e=>{const r=e("core/editor");if(r){const e=r.getCurrentPostId();if(e&&"number"==typeof e)return{currentPostId:e}}return v}))((({urlQueryArgs:r=v,currentPostId:t,...n})=>{const o=(0,e.useMemo)((()=>t?{post_id:t,...r}:r),[t,r]);return(0,p.jsx)(m,{urlQueryArgs:o,...n})}))})(),(window.wp=window.wp||{}).serverSideRender=n.default})();