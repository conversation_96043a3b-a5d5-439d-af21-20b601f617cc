/*! This file is auto-generated */
(()=>{"use strict";var e={923:e=>{e.exports=window.wp.isShallowEqual}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};(()=>{n.r(o),n.d(o,{createUndoManager:()=>a});var e=n(923),t=n.n(e);function r(e,t){const n={...e};return Object.entries(t).forEach((([e,t])=>{n[e]?n[e]={...n[e],to:t.to}:n[e]=t})),n}const i=(e,n)=>{const o=e?.findIndex((({id:e})=>"string"==typeof e?e===n.id:t()(e,n.id))),i=[...e];return-1!==o?i[o]={id:n.id,changes:r(i[o].changes,n.changes)}:i.push(n),i};function a(){let e=[],n=[],o=0;const r=()=>{e=e.slice(0,o||void 0),o=0},a=()=>{var t;const o=0===e.length?0:e.length-1;let r=null!==(t=e[o])&&void 0!==t?t:[];n.forEach((e=>{r=i(r,e)})),n=[],e[o]=r};return{addRecord(o,d=!1){const s=!o||(e=>!e.filter((({changes:e})=>Object.values(e).some((({from:e,to:n})=>"function"!=typeof e&&"function"!=typeof n&&!t()(e,n))))).length)(o);if(d){if(s)return;o.forEach((e=>{n=i(n,e)}))}else{if(r(),n.length&&a(),s)return;e.push(o)}},undo(){n.length&&(r(),a());const t=e[e.length-1+o];if(t)return o-=1,t},redo(){const t=e[e.length+o];if(t)return o+=1,t},hasUndo:()=>!!e[e.length-1+o],hasRedo:()=>!!e[e.length+o]}}})(),(window.wp=window.wp||{}).undoManager=o})();