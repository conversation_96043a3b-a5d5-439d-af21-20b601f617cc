/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,o)=>{for(var s in o)e.o(o,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:o[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{privateApis:()=>R});const o=window.wp.commands,s=window.wp.i18n,a=window.wp.primitives,n=window.ReactJSXRuntime,r=(0,n.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(a.<PERSON>,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})}),i=window.wp.url,c=window.wp.coreData,p=window.wp.data,d=window.wp.element,l=window.wp.notices,m=window.wp.router,h=window.wp.privateApis,{lock:u,unlock:w}=(0,h.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/core-commands"),{useHistory:g}=w(m.privateApis);function _(){const e=(0,i.getPath)(window.location.href)?.includes("site-editor.php"),t=g(),o=(0,p.useSelect)((e=>e(c.store).getCurrentTheme()?.is_block_theme),[]),{saveEntityRecord:a}=(0,p.useDispatch)(c.store),{createErrorNotice:n}=(0,p.useDispatch)(l.store),m=(0,d.useCallback)((async({close:e})=>{try{const e=await a("postType","page",{status:"draft"},{throwOnError:!0});e?.id&&t.push({postId:e.id,postType:"page",canvas:"edit"})}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,s.__)("An error occurred while creating the item.");n(t,{type:"snackbar"})}finally{e()}}),[n,t,a]);return{isLoading:!1,commands:(0,d.useMemo)((()=>{const t=e&&o?m:()=>document.location.href="post-new.php?post_type=page";return[{name:"core/add-new-page",label:(0,s.__)("Add new page"),icon:r,callback:t}]}),[m,e,o])}}const v=(0,n.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(a.Path,{d:"m7.3 9.7 1.4 1.4c.2-.2.3-.3.4-.5 0 0 0-.1.1-.1.3-.5.4-1.1.3-1.6L12 7 9 4 7.2 6.5c-.6-.1-1.1 0-1.6.3 0 0-.1 0-.1.1-.3.1-.4.2-.6.4l1.4 1.4L4 11v1h1l2.3-2.3zM4 20h9v-1.5H4V20zm0-5.5V16h16v-1.5H4z"})}),y=(0,n.jsxs)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,n.jsx)(a.Path,{d:"M15.5 7.5h-7V9h7V7.5Zm-7 3.5h7v1.5h-7V11Zm7 3.5h-7V16h7v-1.5Z"}),(0,n.jsx)(a.Path,{d:"M17 4H7a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM7 5.5h10a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5Z"})]}),b=(0,n.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(a.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),k=(0,n.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(a.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.******* 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-17.6 1L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})}),f=(0,n.jsx)(a.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(a.Path,{d:"M12 4c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14.5c-3.6 0-6.5-2.9-6.5-6.5S8.4 5.5 12 5.5s6.5 2.9 6.5 6.5-2.9 6.5-6.5 6.5zM9 16l4.5-3L15 8.4l-4.5 3L9 16z"})}),T=(0,n.jsx)(a.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(a.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M20 12a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-1.5 0a6.5 6.5 0 0 1-6.5 6.5v-13a6.5 6.5 0 0 1 6.5 6.5Z"})}),x=(0,n.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(a.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.******* 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.******* 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})}),L=window.wp.compose,V=window.wp.htmlEntities;const{useHistory:C}=w(m.privateApis),S={post:v,page:y,wp_template:b,wp_template_part:k};const j=e=>function({search:t}){const o=C(),{isBlockBasedTheme:a,canCreateTemplate:n}=(0,p.useSelect)((e=>({isBlockBasedTheme:e(c.store).getCurrentTheme()?.is_block_theme,canCreateTemplate:e(c.store).canUser("create",{kind:"postType",name:"wp_template"})})),[]),r=function(e){const[t,o]=(0,d.useState)(""),s=(0,L.useDebounce)(o,250);return(0,d.useEffect)((()=>(s(e),()=>s.cancel())),[s,e]),t}(t),{records:l,isLoading:m}=(0,p.useSelect)((t=>{if(!r)return{isLoading:!1};const o={search:r,per_page:10,orderby:"relevance",status:["publish","future","draft","pending","private"]};return{records:t(c.store).getEntityRecords("postType",e,o),isLoading:!t(c.store).hasFinishedResolution("getEntityRecords",["postType",e,o])}}),[r]);return{commands:(0,d.useMemo)((()=>(null!=l?l:[]).map((t=>{const r={name:e+"-"+t.id,searchLabel:t.title?.rendered+" "+t.id,label:t.title?.rendered?(0,V.decodeEntities)(t.title?.rendered):(0,s.__)("(no title)"),icon:S[e]};if(!n||"post"===e||"page"===e&&!a)return{...r,callback:({close:e})=>{const o={post:t.id,action:"edit"},s=(0,i.addQueryArgs)("post.php",o);document.location=s,e()}};const c=(0,i.getPath)(window.location.href)?.includes("site-editor.php");return{...r,callback:({close:s})=>{const a={postType:e,postId:t.id,canvas:"edit"},n=(0,i.addQueryArgs)("site-editor.php",a);c?o.push(a):document.location=n,s()}}}))),[n,l,a,o]),isLoading:m}},P=e=>function({search:t}){const o=C(),{isBlockBasedTheme:a,canCreateTemplate:n}=(0,p.useSelect)((t=>({isBlockBasedTheme:t(c.store).getCurrentTheme()?.is_block_theme,canCreateTemplate:t(c.store).canUser("create",{kind:"postType",name:e})})),[]),{records:r,isLoading:l}=(0,p.useSelect)((t=>{const{getEntityRecords:o}=t(c.store),s={per_page:-1};return{records:o("postType",e,s),isLoading:!t(c.store).hasFinishedResolution("getEntityRecords",["postType",e,s])}}),[]),m=(0,d.useMemo)((()=>function(e=[],t=""){if(!Array.isArray(e)||!e.length)return[];if(!t)return e;const o=[],s=[];for(let a=0;a<e.length;a++){const n=e[a];n?.title?.raw?.toLowerCase()?.includes(t?.toLowerCase())?o.push(n):s.push(n)}return o.concat(s)}(r,t).slice(0,10)),[r,t]);return{commands:(0,d.useMemo)((()=>{if(!n||!a&&"wp_template_part"===!e)return[];const t=(0,i.getPath)(window.location.href)?.includes("site-editor.php"),r=[];return r.push(...m.map((a=>({name:e+"-"+a.id,searchLabel:a.title?.rendered+" "+a.id,label:a.title?.rendered?a.title?.rendered:(0,s.__)("(no title)"),icon:S[e],callback:({close:s})=>{const n={postType:e,postId:a.id,canvas:"edit"},r=(0,i.addQueryArgs)("site-editor.php",n);t?o.push(n):document.location=r,s()}})))),m?.length>0&&"wp_template_part"===e&&r.push({name:"core/edit-site/open-template-parts",label:(0,s.__)("Template parts"),icon:k,callback:({close:e})=>{const s={postType:"wp_template_part",categoryId:"all-parts"},a=(0,i.addQueryArgs)("site-editor.php",s);t?o.push(s):document.location=a,e()}}),r}),[n,a,m,o]),isLoading:l}},B=j("page"),M=j("post"),A=P("wp_template"),z=P("wp_template_part");function H(){const e=C(),t=(0,i.getPath)(window.location.href)?.includes("site-editor.php"),{isBlockBasedTheme:o,canCreateTemplate:a}=(0,p.useSelect)((e=>({isBlockBasedTheme:e(c.store).getCurrentTheme()?.is_block_theme,canCreateTemplate:e(c.store).canUser("create",{kind:"postType",name:"wp_template"})})),[]);return{commands:(0,d.useMemo)((()=>{const n=[];return a&&o&&(n.push({name:"core/edit-site/open-navigation",label:(0,s.__)("Navigation"),icon:f,callback:({close:o})=>{const s={postType:"wp_navigation"},a=(0,i.addQueryArgs)("site-editor.php",s);t?e.push(s):document.location=a,o()}}),n.push({name:"core/edit-site/open-styles",label:(0,s.__)("Styles"),icon:T,callback:({close:o})=>{const s={path:"/wp_global_styles"},a=(0,i.addQueryArgs)("site-editor.php",s);t?e.push(s):document.location=a,o()}}),n.push({name:"core/edit-site/open-pages",label:(0,s.__)("Pages"),icon:y,callback:({close:o})=>{const s={postType:"page"},a=(0,i.addQueryArgs)("site-editor.php",s);t?e.push(s):document.location=a,o()}}),n.push({name:"core/edit-site/open-templates",label:(0,s.__)("Templates"),icon:b,callback:({close:o})=>{const s={postType:"wp_template"},a=(0,i.addQueryArgs)("site-editor.php",s);t?e.push(s):document.location=a,o()}})),n.push({name:"core/edit-site/open-patterns",label:(0,s.__)("Patterns"),icon:x,callback:({close:o})=>{if(a){const s={postType:"wp_block"},a=(0,i.addQueryArgs)("site-editor.php",s);t?e.push(s):document.location=a,o()}else document.location.href="edit.php?post_type=wp_block"}}),n}),[e,t,a,o]),isLoading:!1}}const R={};u(R,{useCommands:function(){(0,o.useCommand)({name:"core/add-new-post",label:(0,s.__)("Add new post"),icon:r,callback:()=>{document.location.href="post-new.php"}}),(0,o.useCommandLoader)({name:"core/add-new-page",hook:_}),(0,o.useCommandLoader)({name:"core/edit-site/navigate-pages",hook:B}),(0,o.useCommandLoader)({name:"core/edit-site/navigate-posts",hook:M}),(0,o.useCommandLoader)({name:"core/edit-site/navigate-templates",hook:A}),(0,o.useCommandLoader)({name:"core/edit-site/navigate-template-parts",hook:z}),(0,o.useCommandLoader)({name:"core/edit-site/basic-navigation",hook:H,context:"site-editor"})}}),(window.wp=window.wp||{}).coreCommands=t})();