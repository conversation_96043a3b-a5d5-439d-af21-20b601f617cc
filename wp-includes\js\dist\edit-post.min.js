/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},d:(t,o)=>{for(var s in o)e.o(o,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:o[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{PluginBlockSettingsMenuItem:()=>qt,PluginDocumentSettingPanel:()=>Wt,PluginMoreMenuItem:()=>Qt,PluginPostPublishPanel:()=>Xt,PluginPostStatusInfo:()=>Zt,PluginPrePublishPanel:()=>$t,PluginSidebar:()=>Yt,PluginSidebarMoreMenuItem:()=>Kt,__experimentalFullscreenModeClose:()=>A,__experimentalMainDashboardButton:()=>eo,__experimentalPluginPostExcerpt:()=>Jt,initializeEditor:()=>oo,reinitializeEditor:()=>so,store:()=>tt});var o={};e.r(o),e.d(o,{__experimentalSetPreviewDeviceType:()=>he,__unstableCreateTemplate:()=>fe,closeGeneralSidebar:()=>Z,closeModal:()=>K,closePublishSidebar:()=>ee,hideBlockTypes:()=>de,initializeMetaBoxes:()=>xe,metaBoxUpdatesFailure:()=>me,metaBoxUpdatesSuccess:()=>ge,openGeneralSidebar:()=>X,openModal:()=>Y,openPublishSidebar:()=>J,removeEditorPanel:()=>ie,requestMetaBoxUpdates:()=>ue,setAvailableMetaBoxesPerLocation:()=>pe,setIsEditingTemplate:()=>ye,setIsInserterOpened:()=>we,setIsListViewOpened:()=>_e,showBlockTypes:()=>le,switchEditorMode:()=>ne,toggleDistractionFree:()=>ve,toggleEditorPanelEnabled:()=>oe,toggleEditorPanelOpened:()=>se,toggleFeature:()=>re,togglePinnedPluginItem:()=>ae,togglePublishSidebar:()=>te,updatePreferredStyleVariations:()=>ce});var s={};e.r(s),e.d(s,{getEditedPostTemplateId:()=>Se});var i={};e.r(i),e.d(i,{__experimentalGetInsertionPoint:()=>Ze,__experimentalGetPreviewDeviceType:()=>$e,areMetaBoxesInitialized:()=>Je,getActiveGeneralSidebarName:()=>Be,getActiveMetaBoxLocations:()=>Ge,getAllMetaBoxes:()=>qe,getEditedPostTemplate:()=>et,getEditorMode:()=>Te,getHiddenBlockTypes:()=>Re,getMetaBoxesPerLocation:()=>He,getPreference:()=>Ae,getPreferences:()=>Ie,hasMetaBoxes:()=>We,isEditingTemplate:()=>Ke,isEditorPanelEnabled:()=>Oe,isEditorPanelOpened:()=>Ne,isEditorPanelRemoved:()=>De,isEditorSidebarOpened:()=>je,isFeatureActive:()=>Fe,isInserterOpened:()=>Xe,isListViewOpened:()=>Ye,isMetaBoxLocationActive:()=>Ue,isMetaBoxLocationVisible:()=>ze,isModalActive:()=>Le,isPluginItemPinned:()=>Ve,isPluginSidebarOpened:()=>ke,isPublishSidebarOpened:()=>Ce,isSavingMetaBoxes:()=>Qe});const r=window.wp.blocks,n=window.wp.blockLibrary,a=window.wp.deprecated;var c=e.n(a);const l=window.wp.element,d=window.wp.data,p=window.wp.preferences,u=window.wp.widgets,g=window.wp.editor;function m(e){var t,o,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(o=m(e[t]))&&(s&&(s+=" "),s+=o)}else for(o in e)e[o]&&(s&&(s+=" "),s+=o);return s}const h=function(){for(var e,t,o=0,s="",i=arguments.length;o<i;o++)(e=arguments[o])&&(t=m(e))&&(s&&(s+=" "),s+=t);return s},w=window.wp.blockEditor,_=window.wp.plugins,y=window.wp.i18n,f=window.wp.primitives,b=window.ReactJSXRuntime,x=(0,b.jsx)(f.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,b.jsx)(f.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})}),v=(0,b.jsx)(f.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,b.jsx)(f.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})}),S=window.wp.notices,P=window.wp.commands,E=window.wp.coreCommands,M=window.wp.url,T=window.wp.htmlEntities,j=window.wp.coreData,k=window.wp.components,B=window.wp.compose,I=(0,b.jsx)(f.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,b.jsx)(f.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"})});const A=function({showTooltip:e,icon:t,href:o,initialPost:s}){var i;const{isRequestingSiteIcon:r,postType:n,siteIconUrl:a}=(0,d.useSelect)((e=>{const{getCurrentPostType:t}=e(g.store),{getEntityRecord:o,getPostType:i,isResolving:r}=e(j.store),n=o("root","__unstableBase",void 0)||{},a=s?.type||t();return{isRequestingSiteIcon:r("getEntityRecord",["root","__unstableBase",void 0]),postType:i(a),siteIconUrl:n.site_icon_url}}),[]),c=(0,B.useReducedMotion)();if(!n)return null;let l=(0,b.jsx)(k.Icon,{size:"36px",icon:I});const p={expand:{scale:1.25,transition:{type:"tween",duration:"0.3"}}};a&&(l=(0,b.jsx)(k.__unstableMotion.img,{variants:!c&&p,alt:(0,y.__)("Site Icon"),className:"edit-post-fullscreen-mode-close_site-icon",src:a})),r&&(l=null),t&&(l=(0,b.jsx)(k.Icon,{size:"36px",icon:t}));const u=h("edit-post-fullscreen-mode-close",{"has-icon":a}),m=null!=o?o:(0,M.addQueryArgs)("edit.php",{post_type:n.slug}),w=null!==(i=n?.labels?.view_items)&&void 0!==i?i:(0,y.__)("Back");return(0,b.jsx)(k.__unstableMotion.div,{whileHover:"expand",children:(0,b.jsx)(k.Button,{__next40pxDefaultSize:!1,className:u,href:m,label:w,showTooltip:e,children:l})})},R=window.wp.privateApis,{lock:C,unlock:D}=(0,R.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-post"),{BackButton:O}=D(g.privateApis),N={hidden:{x:"-100%"},distractionFreeInactive:{x:0},hover:{x:0,transition:{type:"tween",delay:.2}}};const L=function({initialPost:e}){return(0,b.jsx)(O,{children:({length:t})=>t<=1&&(0,b.jsx)(k.__unstableMotion.div,{variants:N,transition:{type:"tween",delay:.8},children:(0,b.jsx)(A,{showTooltip:!0,initialPost:e})})})},F=()=>{const{newPermalink:e}=(0,d.useSelect)((e=>({newPermalink:e(g.store).getCurrentPost().link})),[]),t=(0,l.useRef)();(0,l.useEffect)((()=>{t.current=document.querySelector("#wp-admin-bar-preview a")||document.querySelector("#wp-admin-bar-view a")}),[]),(0,l.useEffect)((()=>{e&&t.current&&t.current.setAttribute("href",e)}),[e])};function V(){return F(),null}const G=window.wp.keyboardShortcuts;function z(e=[],t){const o=[...e];for(const e of t){const t=o.findIndex((t=>t.id===e.id));-1!==t?o[t]=e:o.push(e)}return o}const U=(0,d.combineReducers)({isSaving:function(e=!1,t){switch(t.type){case"REQUEST_META_BOX_UPDATES":return!0;case"META_BOX_UPDATES_SUCCESS":case"META_BOX_UPDATES_FAILURE":return!1;default:return e}},locations:function(e={},t){if("SET_META_BOXES_PER_LOCATIONS"===t.type){const o={...e};for(const[e,s]of Object.entries(t.metaBoxesPerLocation))o[e]=z(o[e],s);return o}return e},initialized:function(e=!1,t){return"META_BOXES_INITIALIZED"===t.type||e}}),H=(0,d.combineReducers)({metaBoxes:U}),q=window.wp.apiFetch;var W=e.n(q);const Q=window.wp.hooks,{interfaceStore:$}=D(g.privateApis),X=e=>({registry:t})=>{t.dispatch($).enableComplementaryArea("core",e)},Z=()=>({registry:e})=>e.dispatch($).disableComplementaryArea("core"),Y=e=>({registry:t})=>(c()("select( 'core/edit-post' ).openModal( name )",{since:"6.3",alternative:"select( 'core/interface').openModal( name )"}),t.dispatch($).openModal(e)),K=()=>({registry:e})=>(c()("select( 'core/edit-post' ).closeModal()",{since:"6.3",alternative:"select( 'core/interface').closeModal()"}),e.dispatch($).closeModal()),J=()=>({registry:e})=>{c()("dispatch( 'core/edit-post' ).openPublishSidebar",{since:"6.6",alternative:"dispatch( 'core/editor').openPublishSidebar"}),e.dispatch(g.store).openPublishSidebar()},ee=()=>({registry:e})=>{c()("dispatch( 'core/edit-post' ).closePublishSidebar",{since:"6.6",alternative:"dispatch( 'core/editor').closePublishSidebar"}),e.dispatch(g.store).closePublishSidebar()},te=()=>({registry:e})=>{c()("dispatch( 'core/edit-post' ).togglePublishSidebar",{since:"6.6",alternative:"dispatch( 'core/editor').togglePublishSidebar"}),e.dispatch(g.store).togglePublishSidebar()},oe=e=>({registry:t})=>{c()("dispatch( 'core/edit-post' ).toggleEditorPanelEnabled",{since:"6.5",alternative:"dispatch( 'core/editor').toggleEditorPanelEnabled"}),t.dispatch(g.store).toggleEditorPanelEnabled(e)},se=e=>({registry:t})=>{c()("dispatch( 'core/edit-post' ).toggleEditorPanelOpened",{since:"6.5",alternative:"dispatch( 'core/editor').toggleEditorPanelOpened"}),t.dispatch(g.store).toggleEditorPanelOpened(e)},ie=e=>({registry:t})=>{c()("dispatch( 'core/edit-post' ).removeEditorPanel",{since:"6.5",alternative:"dispatch( 'core/editor').removeEditorPanel"}),t.dispatch(g.store).removeEditorPanel(e)},re=e=>({registry:t})=>t.dispatch(p.store).toggle("core/edit-post",e),ne=e=>({registry:t})=>{c()("dispatch( 'core/edit-post' ).switchEditorMode",{since:"6.6",alternative:"dispatch( 'core/editor').switchEditorMode"}),t.dispatch(g.store).switchEditorMode(e)},ae=e=>({registry:t})=>{const o=t.select($).isItemPinned("core",e);t.dispatch($)[o?"unpinItem":"pinItem"]("core",e)};function ce(){return c()("dispatch( 'core/edit-post' ).updatePreferredStyleVariations",{since:"6.6",hint:"Preferred Style Variations are not supported anymore."}),{type:"NOTHING"}}const le=e=>({registry:t})=>{D(t.dispatch(g.store)).showBlockTypes(e)},de=e=>({registry:t})=>{D(t.dispatch(g.store)).hideBlockTypes(e)};function pe(e){return{type:"SET_META_BOXES_PER_LOCATIONS",metaBoxesPerLocation:e}}const ue=()=>async({registry:e,select:t,dispatch:o})=>{o({type:"REQUEST_META_BOX_UPDATES"}),window.tinyMCE&&window.tinyMCE.triggerSave();const s=new window.FormData(document.querySelector(".metabox-base-form")),i=s.get("post_ID"),r=s.get("post_type"),n=e.select(j.store).getEditedEntityRecord("postType",r,i),a=[!!n.comment_status&&["comment_status",n.comment_status],!!n.ping_status&&["ping_status",n.ping_status],!!n.sticky&&["sticky",n.sticky],!!n.author&&["post_author",n.author]].filter(Boolean),c=[s,...t.getActiveMetaBoxLocations().map((e=>new window.FormData((e=>{const t=document.querySelector(`.edit-post-meta-boxes-area.is-${e} .metabox-location-${e}`);return t||document.querySelector("#metaboxes .metabox-location-"+e)})(e))))].reduce(((e,t)=>{for(const[o,s]of t)e.append(o,s);return e}),new window.FormData);a.forEach((([e,t])=>c.append(e,t)));try{await W()({url:window._wpMetaBoxUrl,method:"POST",body:c,parse:!1}),o.metaBoxUpdatesSuccess()}catch{o.metaBoxUpdatesFailure()}};function ge(){return{type:"META_BOX_UPDATES_SUCCESS"}}function me(){return{type:"META_BOX_UPDATES_FAILURE"}}const he=e=>({registry:t})=>{c()("dispatch( 'core/edit-post' ).__experimentalSetPreviewDeviceType",{since:"6.5",version:"6.7",hint:"registry.dispatch( editorStore ).setDeviceType"}),t.dispatch(g.store).setDeviceType(e)},we=e=>({registry:t})=>{c()("dispatch( 'core/edit-post' ).setIsInserterOpened",{since:"6.5",alternative:"dispatch( 'core/editor').setIsInserterOpened"}),t.dispatch(g.store).setIsInserterOpened(e)},_e=e=>({registry:t})=>{c()("dispatch( 'core/edit-post' ).setIsListViewOpened",{since:"6.5",alternative:"dispatch( 'core/editor').setIsListViewOpened"}),t.dispatch(g.store).setIsListViewOpened(e)};function ye(){return c()("dispatch( 'core/edit-post' ).setIsEditingTemplate",{since:"6.5",alternative:"dispatch( 'core/editor').setRenderingMode"}),{type:"NOTHING"}}function fe(){return c()("dispatch( 'core/edit-post' ).__unstableCreateTemplate",{since:"6.5"}),{type:"NOTHING"}}let be=!1;const xe=()=>({registry:e,select:t,dispatch:o})=>{if(!e.select(g.store).__unstableIsEditorReady())return;if(be)return;const s=e.select(g.store).getCurrentPostType();window.postboxes.page!==s&&window.postboxes.add_postbox_toggles(s),be=!0,(0,Q.addAction)("editor.savePost","core/edit-post/save-metaboxes",(async(e,s)=>{!s.isAutosave&&t.hasMetaBoxes()&&await o.requestMetaBoxUpdates()})),o({type:"META_BOXES_INITIALIZED"})},ve=()=>({registry:e})=>{c()("dispatch( 'core/edit-post' ).toggleDistractionFree",{since:"6.6",alternative:"dispatch( 'core/editor').toggleDistractionFree"}),e.dispatch(g.store).toggleDistractionFree()},Se=(0,d.createRegistrySelector)((e=>()=>{const{id:t,type:o,slug:s}=e(g.store).getCurrentPost(),{getEntityRecord:i,getEntityRecords:r,canUser:n}=e(j.store),a=n("read",{kind:"root",name:"site"})?i("root","site"):void 0;if(+t===a?.page_for_posts)return e(j.store).getDefaultTemplateId({slug:"home"});const c=e(g.store).getEditedPostAttribute("template");if(c){const e=r("postType","wp_template",{per_page:-1})?.find((e=>e.slug===c));return e?e.id:e}let l;return l=s?"page"===o?`${o}-${s}`:`single-${o}-${s}`:"page"===o?"page":`single-${o}`,o?e(j.store).getDefaultTemplateId({slug:l}):void 0})),{interfaceStore:Pe}=D(g.privateApis),Ee=[],Me={},Te=(0,d.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(p.store).get("core","editorMode"))&&void 0!==t?t:"visual"})),je=(0,d.createRegistrySelector)((e=>()=>{const t=e(Pe).getActiveComplementaryArea("core");return["edit-post/document","edit-post/block"].includes(t)})),ke=(0,d.createRegistrySelector)((e=>()=>{const t=e(Pe).getActiveComplementaryArea("core");return!!t&&!["edit-post/document","edit-post/block"].includes(t)})),Be=(0,d.createRegistrySelector)((e=>()=>e(Pe).getActiveComplementaryArea("core")));const Ie=(0,d.createRegistrySelector)((e=>()=>{c()("select( 'core/edit-post' ).getPreferences",{since:"6.0",alternative:"select( 'core/preferences' ).get"});const t=["editorMode","hiddenBlockTypes"].reduce(((t,o)=>({...t,[o]:e(p.store).get("core",o)})),{}),o=function(e,t){var o;const s=e?.reduce(((e,t)=>({...e,[t]:{enabled:!1}})),{}),i=t?.reduce(((e,t)=>{const o=e?.[t];return{...e,[t]:{...o,opened:!0}}}),null!=s?s:{});return null!==(o=null!=i?i:s)&&void 0!==o?o:Me}(e(p.store).get("core","inactivePanels"),e(p.store).get("core","openPanels"));return{...t,panels:o}}));function Ae(e,t,o){c()("select( 'core/edit-post' ).getPreference",{since:"6.0",alternative:"select( 'core/preferences' ).get"});const s=Ie(e)[t];return void 0===s?o:s}const Re=(0,d.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(p.store).get("core","hiddenBlockTypes"))&&void 0!==t?t:Ee})),Ce=(0,d.createRegistrySelector)((e=>()=>(c()("select( 'core/edit-post' ).isPublishSidebarOpened",{since:"6.6",alternative:"select( 'core/editor' ).isPublishSidebarOpened"}),e(g.store).isPublishSidebarOpened()))),De=(0,d.createRegistrySelector)((e=>(t,o)=>(c()("select( 'core/edit-post' ).isEditorPanelRemoved",{since:"6.5",alternative:"select( 'core/editor' ).isEditorPanelRemoved"}),e(g.store).isEditorPanelRemoved(o)))),Oe=(0,d.createRegistrySelector)((e=>(t,o)=>(c()("select( 'core/edit-post' ).isEditorPanelEnabled",{since:"6.5",alternative:"select( 'core/editor' ).isEditorPanelEnabled"}),e(g.store).isEditorPanelEnabled(o)))),Ne=(0,d.createRegistrySelector)((e=>(t,o)=>(c()("select( 'core/edit-post' ).isEditorPanelOpened",{since:"6.5",alternative:"select( 'core/editor' ).isEditorPanelOpened"}),e(g.store).isEditorPanelOpened(o)))),Le=(0,d.createRegistrySelector)((e=>(t,o)=>(c()("select( 'core/edit-post' ).isModalActive",{since:"6.3",alternative:"select( 'core/interface' ).isModalActive"}),!!e(Pe).isModalActive(o)))),Fe=(0,d.createRegistrySelector)((e=>(t,o)=>!!e(p.store).get("core/edit-post",o))),Ve=(0,d.createRegistrySelector)((e=>(t,o)=>e(Pe).isItemPinned("core",o))),Ge=(0,d.createSelector)((e=>Object.keys(e.metaBoxes.locations).filter((t=>Ue(e,t)))),(e=>[e.metaBoxes.locations])),ze=(0,d.createRegistrySelector)((e=>(t,o)=>Ue(t,o)&&He(t,o)?.some((({id:t})=>e(g.store).isEditorPanelEnabled(`meta-box-${t}`)))));function Ue(e,t){const o=He(e,t);return!!o&&0!==o.length}function He(e,t){return e.metaBoxes.locations[t]}const qe=(0,d.createSelector)((e=>Object.values(e.metaBoxes.locations).flat()),(e=>[e.metaBoxes.locations]));function We(e){return Ge(e).length>0}function Qe(e){return e.metaBoxes.isSaving}const $e=(0,d.createRegistrySelector)((e=>()=>(c()("select( 'core/edit-site' ).__experimentalGetPreviewDeviceType",{since:"6.5",version:"6.7",alternative:"select( 'core/editor' ).getDeviceType"}),e(g.store).getDeviceType()))),Xe=(0,d.createRegistrySelector)((e=>()=>(c()("select( 'core/edit-post' ).isInserterOpened",{since:"6.5",alternative:"select( 'core/editor' ).isInserterOpened"}),e(g.store).isInserterOpened()))),Ze=(0,d.createRegistrySelector)((e=>()=>(c()("select( 'core/edit-post' ).__experimentalGetInsertionPoint",{since:"6.5",version:"6.7"}),D(e(g.store)).getInsertionPoint()))),Ye=(0,d.createRegistrySelector)((e=>()=>(c()("select( 'core/edit-post' ).isListViewOpened",{since:"6.5",alternative:"select( 'core/editor' ).isListViewOpened"}),e(g.store).isListViewOpened()))),Ke=(0,d.createRegistrySelector)((e=>()=>(c()("select( 'core/edit-post' ).isEditingTemplate",{since:"6.5",alternative:"select( 'core/editor' ).getRenderingMode"}),"wp_template"===e(g.store).getCurrentPostType())));function Je(e){return e.metaBoxes.initialized}const et=(0,d.createRegistrySelector)((e=>t=>{const o=Se(t);if(o)return e(j.store).getEditedEntityRecord("postType","wp_template",o)})),tt=(0,d.createReduxStore)("core/edit-post",{reducer:H,actions:o,selectors:i});(0,d.register)(tt),D(tt).registerPrivateSelectors(s);const ot=function(){const{toggleFeature:e}=(0,d.useDispatch)(tt),{registerShortcut:t}=(0,d.useDispatch)(G.store);return(0,l.useEffect)((()=>{t({name:"core/edit-post/toggle-fullscreen",category:"global",description:(0,y.__)("Toggle fullscreen mode."),keyCombination:{modifier:"secondary",character:"f"}})}),[]),(0,G.useShortcut)("core/edit-post/toggle-fullscreen",(()=>{e("fullscreenMode")})),null};function st(){const{editPost:e}=(0,d.useDispatch)(g.store),[t,o]=(0,l.useState)(!1),[s,i]=(0,l.useState)(void 0),[r,n]=(0,l.useState)(""),{postType:a,isNewPost:c}=(0,d.useSelect)((e=>{const{getEditedPostAttribute:t,isCleanNewPost:o}=e(g.store);return{postType:t("type"),isNewPost:o()}}),[]);return(0,l.useEffect)((()=>{c&&"wp_block"===a&&o(!0)}),[]),"wp_block"===a&&c?(0,b.jsx)(b.Fragment,{children:t&&(0,b.jsx)(k.Modal,{title:(0,y.__)("Create pattern"),onRequestClose:()=>{o(!1)},overlayClassName:"reusable-blocks-menu-items__convert-modal",children:(0,b.jsx)("form",{onSubmit:t=>{t.preventDefault(),o(!1),e({title:r,meta:{wp_pattern_sync_status:s}})},children:(0,b.jsxs)(k.__experimentalVStack,{spacing:"5",children:[(0,b.jsx)(k.TextControl,{label:(0,y.__)("Name"),value:r,onChange:n,placeholder:(0,y.__)("My pattern"),className:"patterns-create-modal__name-input",__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,b.jsx)(k.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,y._x)("Synced","pattern (singular)"),help:(0,y.__)("Sync this pattern across multiple locations."),checked:!s,onChange:()=>{i(s?void 0:"unsynced")}}),(0,b.jsx)(k.__experimentalHStack,{justify:"right",children:(0,b.jsx)(k.Button,{__next40pxDefaultSize:!1,variant:"primary",type:"submit",disabled:!r,accessibleWhenDisabled:!0,children:(0,y.__)("Create")})})]})})})}):null}class it extends l.Component{constructor(){super(...arguments),this.state={historyId:null}}componentDidUpdate(e){const{postId:t,postStatus:o,hasHistory:s}=this.props,{historyId:i}=this.state;t===e.postId&&t===i||"auto-draft"===o||!t||s||this.setBrowserURL(t)}setBrowserURL(e){window.history.replaceState({id:e},"Post "+e,function(e){return(0,M.addQueryArgs)("post.php",{post:e,action:"edit"})}(e)),this.setState((()=>({historyId:e})))}render(){return null}}const rt=(0,d.withSelect)((e=>{const{getCurrentPost:t}=e(g.store),o=t();let{id:s,status:i,type:r}=o;return["wp_template","wp_template_part"].includes(r)&&(s=o.wp_id),{postId:s,postStatus:i}}))(it);const nt=function({location:e}){const t=(0,l.useRef)(null),o=(0,l.useRef)(null);(0,l.useEffect)((()=>(o.current=document.querySelector(".metabox-location-"+e),o.current&&t.current.appendChild(o.current),()=>{o.current&&document.querySelector("#metaboxes").appendChild(o.current)})),[e]);const s=(0,d.useSelect)((e=>e(tt).isSavingMetaBoxes()),[]),i=h("edit-post-meta-boxes-area",`is-${e}`,{"is-loading":s});return(0,b.jsxs)("div",{className:i,children:[s&&(0,b.jsx)(k.Spinner,{}),(0,b.jsx)("div",{className:"edit-post-meta-boxes-area__container",ref:t}),(0,b.jsx)("div",{className:"edit-post-meta-boxes-area__clear"})]})};class at extends l.Component{componentDidMount(){this.updateDOM()}componentDidUpdate(e){this.props.isVisible!==e.isVisible&&this.updateDOM()}updateDOM(){const{id:e,isVisible:t}=this.props,o=document.getElementById(e);o&&(t?o.classList.remove("is-hidden"):o.classList.add("is-hidden"))}render(){return null}}const ct=(0,d.withSelect)(((e,{id:t})=>({isVisible:e(g.store).isEditorPanelEnabled(`meta-box-${t}`)})))(at);function lt({location:e}){const t=(0,d.useSelect)((t=>t(tt).getMetaBoxesPerLocation(e)),[e]);return(0,b.jsxs)(b.Fragment,{children:[(null!=t?t:[]).map((({id:e})=>(0,b.jsx)(ct,{id:e},e))),(0,b.jsx)(nt,{location:e})]})}const dt=window.wp.keycodes;const pt=function(){const e=(0,d.useSelect)((e=>{const{canUser:t}=e(j.store),o=(0,M.addQueryArgs)("edit.php",{post_type:"wp_block"}),s=(0,M.addQueryArgs)("site-editor.php",{path:"/patterns"});return t("create",{kind:"postType",name:"wp_template"})?s:o}),[]);return(0,b.jsx)(k.MenuItem,{role:"menuitem",href:e,children:(0,y.__)("Manage patterns")})};function ut(){const e=(0,d.useSelect)((e=>"wp_template"===e(g.store).getCurrentPostType()),[]);return(0,b.jsx)(p.PreferenceToggleMenuItem,{scope:"core/edit-post",name:e?"welcomeGuideTemplate":"welcomeGuide",label:(0,y.__)("Welcome Guide")})}const{PreferenceBaseOption:gt}=D(p.privateApis);function mt({willEnable:e}){const[t,o]=(0,l.useState)(!1);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("p",{className:"edit-post-preferences-modal__custom-fields-confirmation-message",children:(0,y.__)("A page reload is required for this change. Make sure your content is saved before reloading.")}),(0,b.jsx)(k.Button,{__next40pxDefaultSize:!1,className:"edit-post-preferences-modal__custom-fields-confirmation-button",variant:"secondary",isBusy:t,accessibleWhenDisabled:!0,disabled:t,onClick:()=>{o(!0),function(){const e=document.getElementById("toggle-custom-fields-form");e.querySelector('[name="_wp_http_referer"]').setAttribute("value",(0,M.getPathAndQueryString)(window.location.href)),e.submit()}()},children:e?(0,y.__)("Show & Reload Page"):(0,y.__)("Hide & Reload Page")})]})}const ht=(0,d.withSelect)((e=>({areCustomFieldsEnabled:!!e(g.store).getEditorSettings().enableCustomFields})))((function({label:e,areCustomFieldsEnabled:t}){const[o,s]=(0,l.useState)(t);return(0,b.jsx)(gt,{label:e,isChecked:o,onChange:s,children:o!==t&&(0,b.jsx)(mt,{willEnable:o})})})),{PreferenceBaseOption:wt}=D(p.privateApis),_t=(0,B.compose)((0,d.withSelect)(((e,{panelName:t})=>{const{isEditorPanelEnabled:o,isEditorPanelRemoved:s}=e(g.store);return{isRemoved:s(t),isChecked:o(t)}})),(0,B.ifCondition)((({isRemoved:e})=>!e)),(0,d.withDispatch)(((e,{panelName:t})=>({onChange:()=>e(g.store).toggleEditorPanelEnabled(t)}))))(wt),{PreferencesModalSection:yt}=D(p.privateApis);const ft=(0,d.withSelect)((e=>{const{getEditorSettings:t}=e(g.store),{getAllMetaBoxes:o}=e(tt);return{areCustomFieldsRegistered:void 0!==t().enableCustomFields,metaBoxes:o()}}))((function({areCustomFieldsRegistered:e,metaBoxes:t,...o}){const s=t.filter((({id:e})=>"postcustom"!==e));return e||0!==s.length?(0,b.jsxs)(yt,{...o,children:[e&&(0,b.jsx)(ht,{label:(0,y.__)("Custom fields")}),s.map((({id:e,title:t})=>(0,b.jsx)(_t,{label:t,panelName:`meta-box-${e}`},e)))]}):null})),{PreferenceToggleControl:bt}=D(p.privateApis),{PreferencesModal:xt}=D(g.privateApis);function vt(){const e={general:(0,b.jsx)(ft,{title:(0,y.__)("Advanced")}),appearance:(0,b.jsx)(bt,{scope:"core/edit-post",featureName:"themeStyles",help:(0,y.__)("Make the editor look like your theme."),label:(0,y.__)("Use theme styles")})};return(0,b.jsx)(xt,{extraSections:e})}const{ToolsMoreMenuGroup:St,ViewMoreMenuGroup:Pt}=D(g.privateApis),Et=()=>{const e=(0,B.useViewportMatch)("large");return(0,b.jsxs)(b.Fragment,{children:[e&&(0,b.jsx)(Pt,{children:(0,b.jsx)(p.PreferenceToggleMenuItem,{scope:"core/edit-post",name:"fullscreenMode",label:(0,y.__)("Fullscreen mode"),info:(0,y.__)("Show and hide the admin user interface"),messageActivated:(0,y.__)("Fullscreen mode activated"),messageDeactivated:(0,y.__)("Fullscreen mode deactivated"),shortcut:dt.displayShortcut.secondary("f")})}),(0,b.jsxs)(St,{children:[(0,b.jsx)(pt,{}),(0,b.jsx)(ut,{})]}),(0,b.jsx)(vt,{})]})};function Mt({nonAnimatedSrc:e,animatedSrc:t}){return(0,b.jsxs)("picture",{className:"edit-post-welcome-guide__image",children:[(0,b.jsx)("source",{srcSet:e,media:"(prefers-reduced-motion: reduce)"}),(0,b.jsx)("img",{src:t,width:"312",height:"240",alt:""})]})}function Tt(){const{toggleFeature:e}=(0,d.useDispatch)(tt);return(0,b.jsx)(k.Guide,{className:"edit-post-welcome-guide",contentLabel:(0,y.__)("Welcome to the block editor"),finishButtonText:(0,y.__)("Get started"),onFinish:()=>e("welcomeGuide"),pages:[{image:(0,b.jsx)(Mt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.gif"}),content:(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,y.__)("Welcome to the block editor")}),(0,b.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,y.__)("In the WordPress editor, each paragraph, image, or video is presented as a distinct “block” of content.")})]})},{image:(0,b.jsx)(Mt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-editor.gif"}),content:(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,y.__)("Make each block your own")}),(0,b.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,y.__)("Each block comes with its own set of controls for changing things like color, width, and alignment. These will show and hide automatically when you have a block selected.")})]})},{image:(0,b.jsx)(Mt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-library.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-library.gif"}),content:(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,y.__)("Get to know the block library")}),(0,b.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,l.createInterpolateElement)((0,y.__)("All of the blocks available to you live in the block library. You’ll find it wherever you see the <InserterIconImage /> icon."),{InserterIconImage:(0,b.jsx)("img",{alt:(0,y.__)("inserter"),src:"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' rx='2' fill='%231E1E1E'/%3E%3Cpath d='M9.22727 4V14M4 8.77273H14' stroke='white' stroke-width='1.5'/%3E%3C/svg%3E%0A"})})})]})},{image:(0,b.jsx)(Mt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.gif"}),content:(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,y.__)("Learn how to use the block editor")}),(0,b.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,l.createInterpolateElement)((0,y.__)("New to the block editor? Want to learn more about using it? <a>Here's a detailed guide.</a>"),{a:(0,b.jsx)(k.ExternalLink,{href:(0,y.__)("https://wordpress.org/documentation/article/wordpress-block-editor/")})})})]})}]})}function jt(){const{toggleFeature:e}=(0,d.useDispatch)(tt);return(0,b.jsx)(k.Guide,{className:"edit-template-welcome-guide",contentLabel:(0,y.__)("Welcome to the template editor"),finishButtonText:(0,y.__)("Get started"),onFinish:()=>e("welcomeGuideTemplate"),pages:[{image:(0,b.jsx)(Mt,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-template-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-template-editor.gif"}),content:(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,y.__)("Welcome to the template editor")}),(0,b.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,y.__)("Templates help define the layout of the site. You can customize all aspects of your posts and pages using blocks and patterns in this editor.")})]})}]})}function kt({postType:e}){const{isActive:t,isEditingTemplate:o}=(0,d.useSelect)((t=>{const{isFeatureActive:o}=t(tt),s="wp_template"===e;return{isActive:o(s?"welcomeGuideTemplate":"welcomeGuide"),isEditingTemplate:s}}),[e]);return t?o?(0,b.jsx)(jt,{}):(0,b.jsx)(Tt,{}):null}const Bt=(0,b.jsx)(f.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,b.jsx)(f.Path,{d:"M6 4a2 2 0 0 0-2 2v3h1.5V6a.5.5 0 0 1 .5-.5h3V4H6Zm3 14.5H6a.5.5 0 0 1-.5-.5v-3H4v3a2 2 0 0 0 2 2h3v-1.5Zm6 1.5v-1.5h3a.5.5 0 0 0 .5-.5v-3H20v3a2 2 0 0 1-2 2h-3Zm3-16a2 2 0 0 1 2 2v3h-1.5V6a.5.5 0 0 0-.5-.5h-3V4h3Z"})});const It=!1;const{getLayoutStyles:At}=D(w.privateApis),{useCommands:Rt}=D(E.privateApis),{useCommandContext:Ct}=D(P.privateApis),{Editor:Dt,FullscreenMode:Ot,NavigableRegion:Nt}=D(g.privateApis),{BlockKeyboardShortcuts:Lt}=D(n.privateApis),Ft=["wp_template","wp_template_part","wp_block","wp_navigation"];function Vt({isLegacy:e}){const[t,o,s]=(0,d.useSelect)((e=>{const{get:t}=e(p.store),{isMetaBoxLocationVisible:o}=e(tt);return[t("core/edit-post","metaBoxesMainIsOpen"),t("core/edit-post","metaBoxesMainOpenHeight"),o("normal")||o("advanced")||o("side")]}),[]),{set:i}=(0,d.useDispatch)(p.store),r=(0,l.useRef)(),n=(0,B.useMediaQuery)("(max-height: 549px)"),[{min:a,max:c},u]=(0,l.useState)((()=>({}))),g=(0,B.useRefEffect)((e=>{const t=e.closest(".interface-interface-skeleton__content"),o=t.querySelectorAll(":scope > .components-notice-list"),s=t.querySelector(".edit-post-meta-boxes-main__presenter"),i=new window.ResizeObserver((()=>{let e=t.offsetHeight;for(const t of o)e-=t.offsetHeight;const i=s.offsetHeight;u({min:i,max:e})}));i.observe(t);for(const e of o)i.observe(e);return()=>i.disconnect()}),[]),m=(0,l.useRef)(),w=(0,l.useId)(),[_,f]=(0,l.useState)(!0),S=(e,t,o)=>{const s=Math.min(c,Math.max(a,e));t?i("core/edit-post","metaBoxesMainOpenHeight",s):m.current.ariaValueNow=T(s),o&&r.current.updateSize({height:s,width:"auto"})};if(!s)return;const P=(0,b.jsxs)("div",{className:h("edit-post-layout__metaboxes",!e&&"edit-post-meta-boxes-main__liner"),hidden:!e&&n&&!t,children:[(0,b.jsx)(lt,{location:"normal"}),(0,b.jsx)(lt,{location:"advanced"})]});if(e)return P;const E=void 0===o;let M="50%";void 0!==c&&(M=E&&_?c/2:c);const T=e=>Math.round((e-a)/(c-a)*100),j=void 0===c||E?50:T(o),I=e=>{const t={ArrowUp:20,ArrowDown:-20}[e.key];if(t){const s=r.current.resizable,i=E?s.offsetHeight:o;S(t+i,!0,!0),e.preventDefault()}},A="edit-post-meta-boxes-main",R=(0,y.__)("Meta Boxes");let C,D;return n?(C=Nt,D={className:h(A,"is-toggle-only")}):(C=k.ResizableBox,D={as:Nt,ref:r,className:h(A,"is-resizable"),defaultSize:{height:o},minHeight:a,maxHeight:M,enable:{top:!0,right:!1,bottom:!1,left:!1,topLeft:!1,topRight:!1,bottomRight:!1,bottomLeft:!1},handleClasses:{top:"edit-post-meta-boxes-main__presenter"},handleComponent:{top:(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(k.Tooltip,{text:(0,y.__)("Drag to resize"),children:(0,b.jsx)("button",{ref:m,role:"separator","aria-valuenow":j,"aria-label":(0,y.__)("Drag to resize"),"aria-describedby":w,onKeyDown:I})}),(0,b.jsx)(k.VisuallyHidden,{id:w,children:(0,y.__)("Use up and down arrow keys to resize the meta box panel.")})]})},onPointerDown:({pointerId:e,target:t})=>{t.setPointerCapture(e)},onResizeStart:(e,t,o)=>{E&&(S(o.offsetHeight,!1,!0),f(!1))},onResize:()=>S(r.current.state.height),onResizeStop:()=>S(r.current.state.height,!0)}),(0,b.jsxs)(C,{"aria-label":R,...D,children:[n?(0,b.jsxs)("button",{"aria-expanded":t,className:"edit-post-meta-boxes-main__presenter",onClick:()=>i("core/edit-post","metaBoxesMainIsOpen",!t),children:[R,(0,b.jsx)(k.Icon,{icon:t?x:v})]}):(0,b.jsx)("meta",{ref:g}),P]})}const Gt=function({postId:e,postType:t,settings:o,initialEdits:s}){Rt(),function(){const{isFullscreen:e}=(0,d.useSelect)((e=>{const{get:t}=e(p.store);return{isFullscreen:t("core/edit-post","fullscreenMode")}}),[]),{toggle:t}=(0,d.useDispatch)(p.store),{createInfoNotice:o}=(0,d.useDispatch)(S.store);(0,P.useCommand)({name:"core/toggle-fullscreen-mode",label:e?(0,y.__)("Exit fullscreen"):(0,y.__)("Enter fullscreen"),icon:Bt,callback:({close:s})=>{t("core/edit-post","fullscreenMode"),s(),o(e?(0,y.__)("Fullscreen off."):(0,y.__)("Fullscreen on."),{id:"core/edit-post/toggle-fullscreen-mode/notice",type:"snackbar",actions:[{label:(0,y.__)("Undo"),onClick:()=>{t("core/edit-post","fullscreenMode")}}]})}})}();const i=function(){const e=(0,d.useRegistry)();return(0,B.useRefEffect)((t=>{function o(o){if(o.target!==t&&o.target!==t.parentElement)return;const{ownerDocument:s}=t,{defaultView:i}=s;if(!i.parseInt(i.getComputedStyle(t,":after").height,10))return;const n=t.lastElementChild;if(!n)return;const a=n.getBoundingClientRect();if(o.clientY<a.bottom)return;o.preventDefault();const c=e.select(w.store).getBlockOrder(""),l=c[c.length-1],d=e.select(w.store).getBlock(l),{selectBlock:p,insertDefaultBlock:u}=e.dispatch(w.store);d&&(0,r.isUnmodifiedDefaultBlock)(d)?p(l):u()}const{ownerDocument:s}=t;return s.addEventListener("mousedown",o),()=>{s.removeEventListener("mousedown",o)}}),[e])}(),n=function(){const{isBlockBasedTheme:e,hasV3BlocksOnly:t,isEditingTemplate:o,isZoomOutMode:s}=(0,d.useSelect)((e=>{const{getEditorSettings:t,getCurrentPostType:o}=e(g.store),{__unstableGetEditorMode:s}=e(w.store),{getBlockTypes:i}=e(r.store);return{isBlockBasedTheme:t().__unstableIsBlockBasedTheme,hasV3BlocksOnly:i().every((e=>e.apiVersion>=3)),isEditingTemplate:"wp_template"===o(),isZoomOutMode:"zoom-out"===s()}}),[]);return t||It&&e||o||s}(),{createErrorNotice:a}=(0,d.useDispatch)(S.store),{currentPost:{postId:c,postType:u},onNavigateToEntityRecord:m,onNavigateToPreviousEntityRecord:f}=function(e,t,o){const[s,i]=(0,l.useReducer)(((e,{type:t,post:o,previousRenderingMode:s})=>"push"===t?[...e,{post:o,previousRenderingMode:s}]:"pop"===t&&e.length>1?e.slice(0,-1):e),[{post:{postId:e,postType:t}}]),{post:r,previousRenderingMode:n}=s[s.length-1],{getRenderingMode:a}=(0,d.useSelect)(g.store),{setRenderingMode:c}=(0,d.useDispatch)(g.store),p=(0,l.useCallback)((e=>{i({type:"push",post:{postId:e.postId,postType:e.postType},previousRenderingMode:a()}),c(o)}),[a,c,o]),u=(0,l.useCallback)((()=>{i({type:"pop"}),n&&c(n)}),[c,n]);return{currentPost:r,onNavigateToEntityRecord:p,onNavigateToPreviousEntityRecord:s.length>1?u:void 0}}(e,t,"post-only"),x="wp_template"===u,{mode:v,isFullscreenActive:E,hasActiveMetaboxes:I,hasBlockSelected:A,showIconLabels:R,isDistractionFree:C,showMetaBoxes:O,hasHistory:N,isWelcomeGuideVisible:F,templateId:G,isDevicePreview:z}=(0,d.useSelect)((e=>{var t;const{get:s}=e(p.store),{isFeatureActive:i,getEditedPostTemplateId:r}=D(e(tt)),{canUser:n,getPostType:a}=e(j.store),{getDeviceType:c,getEditorMode:l}=e(g.store),{__unstableGetEditorMode:d}=D(e(w.store)),m=o.supportsTemplateMode,h=null!==(t=a(u)?.viewable)&&void 0!==t&&t,_=n("read",{kind:"postType",name:"wp_template"}),y="zoom-out"===d();return{mode:l(),isFullscreenActive:e(tt).isFeatureActive("fullscreenMode"),hasActiveMetaboxes:e(tt).hasMetaBoxes(),hasBlockSelected:!!e(w.store).getBlockSelectionStart(),showIconLabels:s("core","showIconLabels"),isDistractionFree:s("core","distractionFree"),showMetaBoxes:!Ft.includes(u)&&!y,isWelcomeGuideVisible:i("welcomeGuide"),templateId:m&&h&&_&&!x?r():null,isDevicePreview:"Desktop"!==c()}}),[u,x,o.supportsTemplateMode]);(e=>{const t=(0,d.useSelect)((t=>e&&t(g.store).__unstableIsEditorReady()),[e]),{initializeMetaBoxes:o}=(0,d.useDispatch)(tt);(0,l.useEffect)((()=>{t&&o()}),[t,o])})(I),Ct(A?"block-selection-edit":"entity-edit");const U=(0,l.useMemo)((()=>({...o,onNavigateToEntityRecord:m,onNavigateToPreviousEntityRecord:f,defaultRenderingMode:"post-only"})),[o,m,f]),H=function(){const{hasThemeStyleSupport:e,editorSettings:t,isZoomedOutView:o,renderingMode:s,postType:i}=(0,d.useSelect)((e=>{const{__unstableGetEditorMode:t}=e(w.store),{getCurrentPostType:o,getRenderingMode:s}=e(g.store),i=o();return{hasThemeStyleSupport:e(tt).isFeatureActive("themeStyles"),editorSettings:e(g.store).getEditorSettings(),isZoomedOutView:"zoom-out"===t(),renderingMode:s(),postType:i}}),[]);return(0,l.useMemo)((()=>{var r,n,a,c;const l=null!==(r=t.styles?.filter((e=>e.__unstableType&&"theme"!==e.__unstableType)))&&void 0!==r?r:[],d=[...null!==(n=t?.defaultEditorStyles)&&void 0!==n?n:[],...l],p=e&&l.length!==(null!==(a=t.styles?.length)&&void 0!==a?a:0);t.disableLayoutStyles||p||d.push({css:At({style:{},selector:"body",hasBlockGapSupport:!1,hasFallbackGapSupport:!0,fallbackGapValue:"0.5em"})});const u=p?null!==(c=t.styles)&&void 0!==c?c:[]:d;return o||"post-only"!==s||Ft.includes(i)?u:[...u,{css:':root :where(.editor-styles-wrapper)::after {content: ""; display: block; height: 40vh;}'}]}),[t.defaultEditorStyles,t.disableLayoutStyles,t.styles,e,i])}();R?document.body.classList.add("show-icon-labels"):document.body.classList.remove("show-icon-labels");const q=(0,k.__unstableUseNavigateRegions)(),W=h("edit-post-layout","is-mode-"+v,{"has-metaboxes":I}),{createSuccessNotice:Q}=(0,d.useDispatch)(S.store),$=(0,l.useCallback)(((e,t)=>{switch(e){case"move-to-trash":document.location.href=(0,M.addQueryArgs)("edit.php",{trashed:1,post_type:t[0].type,ids:t[0].id});break;case"duplicate-post":{const e=t[0],o="string"==typeof e.title?e.title:e.title?.rendered;Q((0,y.sprintf)((0,y.__)('"%s" successfully created.'),(0,T.decodeEntities)(o)),{type:"snackbar",id:"duplicate-post-action",actions:[{label:(0,y.__)("Edit"),onClick:()=>{const t=e.id;document.location.href=(0,M.addQueryArgs)("post.php",{post:t,action:"edit"})}}]})}}}),[Q]),X=(0,l.useMemo)((()=>({type:t,id:e})),[t,e]),Z=(0,B.useViewportMatch)("medium")&&E?(0,b.jsx)(L,{initialPost:X}):null;return(0,b.jsx)(k.SlotFillProvider,{children:(0,b.jsxs)(g.ErrorBoundary,{children:[(0,b.jsx)(P.CommandMenu,{}),(0,b.jsx)(kt,{postType:u}),(0,b.jsx)("div",{className:q.className,...q,ref:q.ref,children:(0,b.jsxs)(Dt,{settings:U,initialEdits:s,postType:u,postId:c,templateId:G,className:W,styles:H,forceIsDirty:I,contentRef:i,disableIframe:!n,autoFocus:!F,onActionPerformed:$,extraSidebarPanels:O&&(0,b.jsx)(lt,{location:"side"}),extraContent:!C&&O&&(0,b.jsx)(Vt,{isLegacy:!n||z}),children:[(0,b.jsx)(g.PostLockedModal,{}),(0,b.jsx)(V,{}),(0,b.jsx)(Ot,{isActive:E}),(0,b.jsx)(rt,{hasHistory:N}),(0,b.jsx)(g.UnsavedChangesWarning,{}),(0,b.jsx)(g.AutosaveMonitor,{}),(0,b.jsx)(g.LocalAutosaveMonitor,{}),(0,b.jsx)(ot,{}),(0,b.jsx)(g.EditorKeyboardShortcutsRegister,{}),(0,b.jsx)(Lt,{}),(0,b.jsx)(st,{}),(0,b.jsx)(_.PluginArea,{onError:function(e){a((0,y.sprintf)((0,y.__)('The "%s" plugin has encountered an error and cannot be rendered.'),e))}}),(0,b.jsx)(Et,{}),Z,(0,b.jsx)(g.EditorSnackbars,{})]})})]})})},{PluginPostExcerpt:zt}=D(g.privateApis),Ut=(0,M.getPath)(window.location.href)?.includes("site-editor.php"),Ht=e=>{c()(`wp.editPost.${e}`,{since:"6.6",alternative:`wp.editor.${e}`})};function qt(e){return Ut?null:(Ht("PluginBlockSettingsMenuItem"),(0,b.jsx)(g.PluginBlockSettingsMenuItem,{...e}))}function Wt(e){return Ut?null:(Ht("PluginDocumentSettingPanel"),(0,b.jsx)(g.PluginDocumentSettingPanel,{...e}))}function Qt(e){return Ut?null:(Ht("PluginMoreMenuItem"),(0,b.jsx)(g.PluginMoreMenuItem,{...e}))}function $t(e){return Ut?null:(Ht("PluginPrePublishPanel"),(0,b.jsx)(g.PluginPrePublishPanel,{...e}))}function Xt(e){return Ut?null:(Ht("PluginPostPublishPanel"),(0,b.jsx)(g.PluginPostPublishPanel,{...e}))}function Zt(e){return Ut?null:(Ht("PluginPostStatusInfo"),(0,b.jsx)(g.PluginPostStatusInfo,{...e}))}function Yt(e){return Ut?null:(Ht("PluginSidebar"),(0,b.jsx)(g.PluginSidebar,{...e}))}function Kt(e){return Ut?null:(Ht("PluginSidebarMoreMenuItem"),(0,b.jsx)(g.PluginSidebarMoreMenuItem,{...e}))}function Jt(){return Ut?null:(c()("wp.editPost.__experimentalPluginPostExcerpt",{since:"6.6",hint:"Core and custom panels can be access programmatically using their panel name.",link:"https://developer.wordpress.org/block-editor/reference-guides/slotfills/plugin-document-setting-panel/#accessing-a-panel-programmatically"}),zt)}const{BackButton:eo,registerCoreBlockBindingsSources:to}=D(g.privateApis);function oo(e,t,o,s,i){const a=window.matchMedia("(min-width: 782px)").matches,c=document.getElementById(e),m=(0,l.createRoot)(c);(0,d.dispatch)(p.store).setDefaults("core/edit-post",{fullscreenMode:!0,themeStyles:!0,welcomeGuide:!0,welcomeGuideTemplate:!0}),(0,d.dispatch)(p.store).setDefaults("core",{allowRightClickOverrides:!0,editorMode:"visual",fixedToolbar:!1,hiddenBlockTypes:[],inactivePanels:[],openPanels:["post-status"],showBlockBreadcrumbs:!0,showIconLabels:!1,showListViewByDefault:!1,enableChoosePatternModal:!0,isPublishSidebarEnabled:!0}),window.__experimentalMediaProcessing&&(0,d.dispatch)(p.store).setDefaults("core/media",{requireApproval:!0,optimizeOnUpload:!0}),(0,d.dispatch)(r.store).reapplyBlockTypeFilters(),a&&(0,d.select)(p.store).get("core","showListViewByDefault")&&!(0,d.select)(p.store).get("core","distractionFree")&&(0,d.dispatch)(g.store).setIsListViewOpened(!0),(0,n.registerCoreBlocks)(),to(),(0,u.registerLegacyWidgetBlock)({inserter:!1}),(0,u.registerWidgetGroupBlock)({inserter:!1});"Standards"!==("CSS1Compat"===document.compatMode?"Standards":"Quirks")&&console.warn("Your browser is using Quirks Mode. \nThis can cause rendering issues such as blocks overlaying meta boxes in the editor. Quirks Mode can be triggered by PHP errors or HTML code appearing before the opening <!DOCTYPE html>. Try checking the raw page source or your site's PHP error log and resolving errors there, removing any HTML before the doctype, or disabling plugins.");return-1!==window.navigator.userAgent.indexOf("iPhone")&&window.addEventListener("scroll",(e=>{const t=document.getElementsByClassName("interface-interface-skeleton__body")[0];e.target===document&&(window.scrollY>100&&(t.scrollTop=t.scrollTop+window.scrollY),document.getElementsByClassName("is-mode-visual")[0]&&window.scrollTo(0,0))})),window.addEventListener("dragover",(e=>e.preventDefault()),!1),window.addEventListener("drop",(e=>e.preventDefault()),!1),m.render((0,b.jsx)(l.StrictMode,{children:(0,b.jsx)(Gt,{settings:s,postId:o,postType:t,initialEdits:i})})),m}function so(){c()("wp.editPost.reinitializeEditor",{since:"6.2",version:"6.3"})}(window.wp=window.wp||{}).editPost=t})();