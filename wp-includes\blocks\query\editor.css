.block-library-query-toolbar__popover .components-popover__content{
  min-width:230px;
}
.block-library-query-toolbar__popover .components-popover__content .block-library-query-toolbar__popover-number-control{
  margin-bottom:8px;
}

.wp-block-query__create-new-link{
  padding:0 16px 16px 52px;
}

.block-library-query__pattern-selection-content .block-editor-block-patterns-list{
  display:grid;
  grid-template-columns:1fr 1fr 1fr;
  grid-gap:8px;
}
.block-library-query__pattern-selection-content .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item{
  margin-bottom:0;
}
.block-library-query__pattern-selection-content .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item .block-editor-block-preview__container{
  max-height:250px;
}

.block-library-query-pattern__selection-modal .block-editor-block-patterns-list{
  column-count:2;
  column-gap:24px;
}
@media (min-width:1280px){
  .block-library-query-pattern__selection-modal .block-editor-block-patterns-list{
    column-count:3;
  }
}
.block-library-query-pattern__selection-modal .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item{
  break-inside:avoid-column;
}
.block-library-query-pattern__selection-modal .block-library-query-pattern__selection-search{
  background:#fff;
  margin-bottom:-4px;
  padding:16px 0;
  position:sticky;
  top:0;
  transform:translateY(-4px);
  z-index:2;
}

@media (min-width:600px){
  .wp-block-query__enhanced-pagination-modal{
    max-width:480px;
  }
}

.wp-block-query__enhanced-pagination-notice{
  margin:0;
}