<?php
/**
 * Features Taxonomy Template
 */

get_header();

$current_term = get_queried_object();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        
        <!-- Page Header -->
        <div class="taxonomy-header">
            <div class="container">
                <div class="breadcrumbs">
                    <a href="<?php echo home_url(); ?>">Home</a>
                    <span class="separator">›</span>
                    <a href="<?php echo home_url('/features/'); ?>">Features</a>
                    <span class="separator">›</span>
                    <span class="current"><?php echo esc_html($current_term->name); ?></span>
                </div>
                
                <h1 class="page-title">
                    Petting Zoos with <?php echo esc_html($current_term->name); ?>
                </h1>
                
                <?php if ($current_term->description) : ?>
                    <div class="taxonomy-description">
                        <?php echo wpautop($current_term->description); ?>
                    </div>
                <?php else : ?>
                    <div class="taxonomy-description">
                        <p>Find petting zoos that offer <?php echo esc_html(strtolower($current_term->name)); ?>. 
                        Perfect for families looking for specific amenities and features during their visit.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="container">
            
            <!-- Filters Section -->
            <div class="zoo-filters-section">
                <h3>Filter Results</h3>
                <div class="zoo-filters">
                    <select name="location" onchange="applyFilters()">
                        <option value="">All Locations</option>
                        <?php
                        // Get states first
                        $states = get_terms(array(
                            'taxonomy' => 'location',
                            'hide_empty' => true,
                            'parent' => 0
                        ));
                        
                        $selected_location = isset($_GET['location']) ? $_GET['location'] : '';
                        
                        foreach ($states as $state) {
                            $cities = get_terms(array(
                                'taxonomy' => 'location',
                                'hide_empty' => true,
                                'parent' => $state->term_id
                            ));
                            
                            if (!empty($cities)) {
                                echo '<optgroup label="' . esc_attr($state->name) . '">';
                                foreach ($cities as $city) {
                                    $selected = ($selected_location === $city->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($city->slug) . '" ' . $selected . '>' . esc_html($city->name) . '</option>';
                                }
                                echo '</optgroup>';
                            }
                        }
                        ?>
                    </select>
                    
                    <select name="animal_type" onchange="applyFilters()">
                        <option value="">All Animal Types</option>
                        <?php
                        $animals = get_terms(array(
                            'taxonomy' => 'animal_type',
                            'hide_empty' => true
                        ));
                        
                        $selected_animal = isset($_GET['animal_type']) ? $_GET['animal_type'] : '';
                        
                        foreach ($animals as $animal) {
                            $selected = ($selected_animal === $animal->slug) ? 'selected' : '';
                            echo '<option value="' . esc_attr($animal->slug) . '" ' . $selected . '>' . esc_html($animal->name) . '</option>';
                        }
                        ?>
                    </select>
                    
                    <select name="zoo_type" onchange="applyFilters()">
                        <option value="">All Zoo Types</option>
                        <?php
                        $zoo_types = get_terms(array(
                            'taxonomy' => 'zoo_type',
                            'hide_empty' => true
                        ));
                        
                        $selected_type = isset($_GET['zoo_type']) ? $_GET['zoo_type'] : '';
                        
                        foreach ($zoo_types as $type) {
                            $selected = ($selected_type === $type->slug) ? 'selected' : '';
                            echo '<option value="' . esc_attr($type->slug) . '" ' . $selected . '>' . esc_html($type->name) . '</option>';
                        }
                        ?>
                    </select>
                </div>
            </div>

            <!-- Results Count -->
            <?php
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
            
            // Build query args
            $args = array(
                'post_type' => 'petting_zoo',
                'posts_per_page' => 12,
                'paged' => $paged,
                'tax_query' => array(
                    'relation' => 'AND',
                    array(
                        'taxonomy' => 'features',
                        'field' => 'term_id',
                        'terms' => $current_term->term_id,
                    )
                )
            );
            
            // Add additional filters
            if (isset($_GET['location']) && !empty($_GET['location'])) {
                $args['tax_query'][] = array(
                    'taxonomy' => 'location',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['location']),
                );
            }
            
            if (isset($_GET['animal_type']) && !empty($_GET['animal_type'])) {
                $args['tax_query'][] = array(
                    'taxonomy' => 'animal_type',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['animal_type']),
                );
            }
            
            if (isset($_GET['zoo_type']) && !empty($_GET['zoo_type'])) {
                $args['tax_query'][] = array(
                    'taxonomy' => 'zoo_type',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['zoo_type']),
                );
            }
            
            $zoo_query = new WP_Query($args);
            ?>
            
            <div class="results-info">
                <p>Found <?php echo $zoo_query->found_posts; ?> petting zoo<?php echo $zoo_query->found_posts !== 1 ? 's' : ''; ?> 
                with <?php echo esc_html(strtolower($current_term->name)); ?></p>
            </div>

            <!-- Petting Zoos Grid -->
            <?php if ($zoo_query->have_posts()) : ?>
                <div class="zoo-grid">
                    <?php while ($zoo_query->have_posts()) : $zoo_query->the_post(); ?>
                        <div class="zoo-card">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium'); ?>
                                </a>
                            <?php endif; ?>
                            
                            <div class="zoo-card-content">
                                <h3><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                
                                <?php
                                $address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                                if ($address) : ?>
                                    <div class="location"><?php echo esc_html($address); ?></div>
                                <?php endif; ?>
                                
                                <div class="excerpt">
                                    <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                </div>
                                
                                <!-- Location Tags -->
                                <?php
                                $locations = get_the_terms(get_the_ID(), 'location');
                                if ($locations && !is_wp_error($locations)) : ?>
                                    <div class="location-tags">
                                        <?php foreach ($locations as $location) : ?>
                                            <span class="location-tag">📍 <?php echo esc_html($location->name); ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Features Tags -->
                                <?php
                                $zoo_features = get_the_terms(get_the_ID(), 'features');
                                if ($zoo_features && !is_wp_error($zoo_features)) : ?>
                                    <div class="features">
                                        <?php 
                                        $feature_count = 0;
                                        foreach ($zoo_features as $feature) : 
                                            if ($feature_count >= 3) break;
                                            $is_current = ($feature->term_id === $current_term->term_id);
                                            ?>
                                            <span class="feature-tag <?php echo $is_current ? 'current-feature' : ''; ?>">
                                                <?php echo esc_html($feature->name); ?>
                                            </span>
                                            <?php 
                                            $feature_count++;
                                        endforeach; 
                                        
                                        if (count($zoo_features) > 3) : ?>
                                            <span class="feature-tag">+<?php echo count($zoo_features) - 3; ?> more</span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <a href="<?php the_permalink(); ?>" class="btn">Learn More</a>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <?php
                $pagination = paginate_links(array(
                    'total' => $zoo_query->max_num_pages,
                    'current' => $paged,
                    'format' => '?paged=%#%',
                    'prev_text' => '← Previous',
                    'next_text' => 'Next →',
                ));
                
                if ($pagination) : ?>
                    <div class="pagination-wrapper">
                        <?php echo $pagination; ?>
                    </div>
                <?php endif; ?>

            <?php else : ?>
                <div class="no-results">
                    <h3>No petting zoos found</h3>
                    <p>Sorry, we couldn't find any petting zoos with <?php echo esc_html(strtolower($current_term->name)); ?> matching your criteria.</p>
                    <a href="<?php echo home_url(); ?>" class="btn">Browse All Petting Zoos</a>
                </div>
            <?php endif; ?>
            
            <?php wp_reset_postdata(); ?>

            <!-- Related Features -->
            <section class="related-features section">
                <h2>Other Popular Features</h2>
                <div class="features-grid">
                    <?php
                    $related_features = get_terms(array(
                        'taxonomy' => 'features',
                        'hide_empty' => true,
                        'exclude' => array($current_term->term_id),
                        'number' => 8,
                        'orderby' => 'count',
                        'order' => 'DESC'
                    ));
                    
                    foreach ($related_features as $feature) : ?>
                        <a href="<?php echo get_term_link($feature); ?>" class="feature-card">
                            <h4><?php echo esc_html($feature->name); ?></h4>
                            <div class="zoo-count"><?php echo $feature->count; ?> Location<?php echo $feature->count !== 1 ? 's' : ''; ?></div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </section>

        </div>
    </main>
</div>

<script>
function applyFilters() {
    const params = new URLSearchParams();
    
    document.querySelectorAll('.zoo-filters select').forEach(select => {
        if (select.value) {
            params.set(select.name, select.value);
        }
    });
    
    const currentUrl = window.location.pathname;
    const queryString = params.toString();
    
    if (queryString) {
        window.location.href = currentUrl + '?' + queryString;
    } else {
        window.location.href = currentUrl;
    }
}
</script>

<style>
.current-feature {
    background: #4CAF50 !important;
    color: white !important;
}

.feature-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.feature-card:hover {
    transform: translateY(-3px);
    text-decoration: none;
    color: inherit;
}

.feature-card h4 {
    color: #4CAF50;
    margin-bottom: 0.5rem;
}

.feature-card .zoo-count {
    color: #666;
    font-size: 0.9rem;
}

.location-tags {
    margin-bottom: 1rem;
}

.location-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}
</style>

<?php get_footer(); ?>
