!function(){function e(e){return void 0===e?0:Number(e)}function n(e,n){return!(e===n||isNaN(e)&&isNaN(n))}self.DOMRect=function(t,i,u,r){var o,f,c,a,m=e(t),b=e(i),d=e(u),g=e(r);Object.defineProperties(this,{x:{get:function(){return m},set:function(e){n(m,e)&&(m=e,o=f=void 0)},enumerable:!0},y:{get:function(){return b},set:function(e){n(b,e)&&(b=e,c=a=void 0)},enumerable:!0},width:{get:function(){return d},set:function(e){n(d,e)&&(d=e,o=f=void 0)},enumerable:!0},height:{get:function(){return g},set:function(e){n(g,e)&&(g=e,c=a=void 0)},enumerable:!0},left:{get:function(){return o=void 0===o?m+Math.min(0,d):o},enumerable:!0},right:{get:function(){return f=void 0===f?m+Math.max(0,d):f},enumerable:!0},top:{get:function(){return c=void 0===c?b+Math.min(0,g):c},enumerable:!0},bottom:{get:function(){return a=void 0===a?b+Math.max(0,g):a},enumerable:!0}})}}();