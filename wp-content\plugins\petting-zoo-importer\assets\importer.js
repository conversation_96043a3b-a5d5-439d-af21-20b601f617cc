/**
 * Petting Zoo Importer JavaScript
 */

jQuery(document).ready(function($) {
    let selectedFile = null;
    let jsonData = null;
    
    // File selection handler
    $('#json-file').on('change', function(e) {
        const file = e.target.files[0];
        
        if (!file) {
            resetInterface();
            return;
        }
        
        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            alert('Please select a valid JSON file.');
            resetInterface();
            return;
        }
        
        selectedFile = file;
        
        // Read file content
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                jsonData = JSON.parse(e.target.result);
                enableButtons();
                logMessage('File loaded successfully: ' + file.name);
                logMessage('Found ' + (jsonData.petting_zoos ? jsonData.petting_zoos.length : 0) + ' petting zoos in file.');
            } catch (error) {
                alert('Invalid JSON file: ' + error.message);
                resetInterface();
            }
        };
        
        reader.onerror = function() {
            alert('Error reading file.');
            resetInterface();
        };
        
        reader.readAsText(file);
    });
    
    // Simulate import button
    $('#simulate-btn').on('click', function() {
        if (!jsonData) {
            alert('Please select a JSON file first.');
            return;
        }
        
        simulateImport();
    });
    
    // Import button
    $('#import-btn').on('click', function() {
        if (!jsonData) {
            alert('Please select a JSON file first.');
            return;
        }
        
        if (!confirm('Are you sure you want to import this data? This will create/update petting zoo entries in your database.')) {
            return;
        }
        
        processImport();
    });
    
    function resetInterface() {
        selectedFile = null;
        jsonData = null;
        disableButtons();
        clearLog();
        hideProgress();
    }
    
    function enableButtons() {
        $('#simulate-btn, #import-btn').prop('disabled', false);
    }
    
    function disableButtons() {
        $('#simulate-btn, #import-btn').prop('disabled', true);
    }
    
    function showProgress(text = 'Processing...') {
        $('.import-progress').show();
        $('.progress-text').text(text);
        $('.progress-fill').css('width', '0%');
    }
    
    function updateProgress(percentage, text) {
        $('.progress-fill').css('width', percentage + '%');
        if (text) {
            $('.progress-text').text(text);
        }
    }
    
    function hideProgress() {
        $('.import-progress').hide();
    }
    
    function clearLog() {
        $('#log-content').html('<p>No import activity yet.</p>');
    }
    
    function logMessage(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = $('<div class="log-entry log-' + type + '"></div>');
        logEntry.html('<span class="timestamp">[' + timestamp + ']</span> ' + escapeHtml(message));
        
        if ($('#log-content p').length && $('#log-content p').text() === 'No import activity yet.') {
            $('#log-content').empty();
        }
        
        $('#log-content').append(logEntry);
        $('#log-content').scrollTop($('#log-content')[0].scrollHeight);
    }
    
    function logMessages(messages, type = 'info') {
        messages.forEach(function(message) {
            logMessage(message, type);
        });
    }
    
    function simulateImport() {
        showProgress('Simulating import...');
        disableButtons();
        
        logMessage('Starting import simulation...', 'info');
        
        $.ajax({
            url: importerAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'simulate_import',
                json_data: JSON.stringify(jsonData),
                nonce: importerAjax.nonce
            },
            success: function(response) {
                hideProgress();
                enableButtons();
                
                if (response.success) {
                    logMessages(response.data.log, 'info');
                    
                    if (response.data.warnings.length > 0) {
                        logMessage('Warnings found:', 'warning');
                        logMessages(response.data.warnings, 'warning');
                    }
                    
                    if (response.data.errors.length > 0) {
                        logMessage('Errors found:', 'error');
                        logMessages(response.data.errors, 'error');
                        logMessage('Import cannot proceed due to errors. Please fix the issues and try again.', 'error');
                    } else {
                        logMessage('Simulation completed successfully! Ready to import.', 'success');
                    }
                } else {
                    logMessage('Simulation failed: ' + response.data, 'error');
                }
            },
            error: function(xhr, status, error) {
                hideProgress();
                enableButtons();
                logMessage('AJAX error during simulation: ' + error, 'error');
            }
        });
    }
    
    function processImport() {
        showProgress('Importing data...');
        disableButtons();
        
        logMessage('Starting data import...', 'info');
        
        $.ajax({
            url: importerAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'process_import',
                json_data: JSON.stringify(jsonData),
                nonce: importerAjax.nonce
            },
            success: function(response) {
                hideProgress();
                enableButtons();
                
                if (response.success) {
                    logMessages(response.data.log, 'info');
                    
                    if (response.data.errors.length > 0) {
                        logMessage('Some errors occurred during import:', 'warning');
                        logMessages(response.data.errors, 'error');
                    }
                    
                    logMessage('Import completed!', 'success');
                    logMessage('Created: ' + response.data.imported + ' new petting zoos', 'success');
                    logMessage('Updated: ' + response.data.updated + ' existing petting zoos', 'success');
                    
                    // Show success message
                    const successMsg = 'Import completed successfully!\n\n' +
                                     'Created: ' + response.data.imported + ' new petting zoos\n' +
                                     'Updated: ' + response.data.updated + ' existing petting zoos';
                    
                    if (response.data.errors.length > 0) {
                        alert(successMsg + '\n\nNote: Some errors occurred. Check the log for details.');
                    } else {
                        alert(successMsg);
                    }
                    
                } else {
                    logMessage('Import failed: ' + response.data, 'error');
                    alert('Import failed. Check the log for details.');
                }
            },
            error: function(xhr, status, error) {
                hideProgress();
                enableButtons();
                logMessage('AJAX error during import: ' + error, 'error');
                alert('Import failed due to a server error. Check the log for details.');
            }
        });
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Initialize interface
    resetInterface();
});
