.wp-block-navigation .block-list-appender{
  position:relative;
}
.wp-block-navigation .has-child{
  cursor:pointer;
}
.wp-block-navigation .has-child .wp-block-navigation__submenu-container{
  z-index:28;
}
.wp-block-navigation .has-child:hover .wp-block-navigation__submenu-container{
  z-index:29;
}
.wp-block-navigation .has-child.has-child-selected>.wp-block-navigation__submenu-container,.wp-block-navigation .has-child.is-selected>.wp-block-navigation__submenu-container{
  height:auto !important;
  min-width:200px !important;
  opacity:1 !important;
  overflow:visible !important;
  visibility:visible !important;
  width:auto !important;
}
.wp-block-navigation-item .wp-block-navigation-item__content{
  cursor:text;
}
.wp-block-navigation-item.is-editing,.wp-block-navigation-item.is-selected{
  min-width:20px;
}
.wp-block-navigation-item .block-list-appender{
  margin:16px auto 16px 16px;
}

.wp-block-navigation-link__invalid-item{
  color:#000;
}
.wp-block-navigation-link__placeholder{
  background-image:none !important;
  box-shadow:none !important;
  position:relative;
  text-decoration:none !important;
}
.wp-block-navigation-link__placeholder .wp-block-navigation-link__placeholder-text span{
  --wp-underline-color:var(--wp-admin-theme-color);
  background-image:linear-gradient(45deg, #0000 20%, var(--wp-underline-color) 30%, var(--wp-underline-color) 36%, #0000 46%), linear-gradient(135deg, #0000 54%, var(--wp-underline-color) 64%, var(--wp-underline-color) 70%, #0000 80%);
  background-position:0 100%;
  background-repeat:repeat-x;
  background-size:6px 3px;
  padding-bottom:.1em;
}
.wp-block-navigation-link__placeholder.wp-block-navigation-item__content{
  cursor:pointer;
}
.link-control-transform{
  border-top:1px solid #ccc;
  padding:0 16px 8px;
}

.link-control-transform__subheading{
  color:#1e1e1e;
  font-size:11px;
  font-weight:500;
  margin-bottom:1.5em;
  text-transform:uppercase;
}

.link-control-transform__items{
  display:flex;
  justify-content:space-between;
}

.link-control-transform__item{
  flex-basis:33%;
  flex-direction:column;
  gap:8px;
  height:auto;
}