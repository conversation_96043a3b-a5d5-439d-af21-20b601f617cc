:root{
  --wp-admin-theme-color:#007cba;
  --wp-admin-theme-color--rgb:0, 124, 186;
  --wp-admin-theme-color-darker-10:#006ba1;
  --wp-admin-theme-color-darker-10--rgb:0, 107, 161;
  --wp-admin-theme-color-darker-20:#005a87;
  --wp-admin-theme-color-darker-20--rgb:0, 90, 135;
  --wp-admin-border-width-focus:2px;
  --wp-block-synced-color:#7a00df;
  --wp-block-synced-color--rgb:122, 0, 223;
  --wp-bound-block-color:var(--wp-block-synced-color);
}
@media (min-resolution:192dpi){
  :root{
    --wp-admin-border-width-focus:1.5px;
  }
}

.nux-dot-tip:after,.nux-dot-tip:before{
  border-radius:100%;
  content:" ";
  pointer-events:none;
  position:absolute;
}
.nux-dot-tip:before{
  animation:nux-pulse 1.6s cubic-bezier(.17, .67, .92, .62) infinite;
  background:#00739ce6;
  height:24px;
  left:-12px;
  opacity:.9;
  top:-12px;
  transform:scale(.3333333333);
  width:24px;
}
.nux-dot-tip:after{
  background:#00739c;
  height:8px;
  left:-4px;
  top:-4px;
  width:8px;
}
@keyframes nux-pulse{
  to{
    background:#00739c00;
    transform:scale(1);
  }
}
.nux-dot-tip .components-popover__content{
  padding:20px 18px;
  width:350px;
}
@media (min-width:600px){
  .nux-dot-tip .components-popover__content{
    width:450px;
  }
}
.nux-dot-tip .components-popover__content .nux-dot-tip__disable{
  position:absolute;
  right:0;
  top:0;
}
.nux-dot-tip[data-y-axis=top]{
  margin-top:-4px;
}
.nux-dot-tip[data-y-axis=bottom]{
  margin-top:4px;
}
.nux-dot-tip[data-y-axis=middle][data-y-axis=left]{
  margin-left:-4px;
}
.nux-dot-tip[data-y-axis=middle][data-y-axis=right]{
  margin-left:4px;
}
.nux-dot-tip[data-y-axis=top] .components-popover__content{
  margin-bottom:20px;
}
.nux-dot-tip[data-y-axis=bottom] .components-popover__content{
  margin-top:20px;
}
.nux-dot-tip[data-y-axis=middle][data-y-axis=left] .components-popover__content{
  margin-right:20px;
}
.nux-dot-tip[data-y-axis=middle][data-y-axis=right] .components-popover__content{
  margin-left:20px;
}
.nux-dot-tip[data-y-axis=center],.nux-dot-tip[data-y-axis=left],.nux-dot-tip[data-y-axis=right]{
  z-index:1000001;
}
@media (max-width:600px){
  .nux-dot-tip[data-y-axis=center] .components-popover__content,.nux-dot-tip[data-y-axis=left] .components-popover__content,.nux-dot-tip[data-y-axis=right] .components-popover__content{
    align-self:end;
    left:5px;
    margin:20px 0 0;
    max-width:none !important;
    position:fixed;
    right:5px;
    width:auto;
  }
}
.nux-dot-tip.components-popover:not([data-y-axis=middle])[data-y-axis=right] .components-popover__content{
  margin-left:0;
}
.nux-dot-tip.components-popover:not([data-y-axis=middle])[data-y-axis=left] .components-popover__content{
  margin-right:0;
}
.nux-dot-tip.components-popover.interface-more-menu-dropdown__content:not([data-y-axis=middle])[data-y-axis=right] .components-popover__content{
  margin-left:-12px;
}
.nux-dot-tip.components-popover.interface-more-menu-dropdown__content:not([data-y-axis=middle])[data-y-axis=left] .components-popover__content{
  margin-right:-12px;
}