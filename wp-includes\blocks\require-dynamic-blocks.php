<?php

// This file was autogenerated by tools/release/sync-stable-blocks.js, do not change manually!
// Requires files for dynamic blocks necessary for core blocks registration.
require_once ABSPATH . WPINC . '/blocks/archives.php';
require_once ABSPATH . WPINC . '/blocks/avatar.php';
require_once ABSPATH . WPINC . '/blocks/block.php';
require_once ABSPATH . WPINC . '/blocks/button.php';
require_once ABSPATH . WPINC . '/blocks/calendar.php';
require_once ABSPATH . WPINC . '/blocks/categories.php';
require_once ABSPATH . WPINC . '/blocks/comment-author-name.php';
require_once ABSPATH . WPINC . '/blocks/comment-content.php';
require_once ABSPATH . WPINC . '/blocks/comment-date.php';
require_once ABSPATH . WPINC . '/blocks/comment-edit-link.php';
require_once ABSPATH . WPINC . '/blocks/comment-reply-link.php';
require_once ABSPATH . WPINC . '/blocks/comment-template.php';
require_once ABSPATH . WPINC . '/blocks/comments.php';
require_once ABSPATH . WPINC . '/blocks/comments-pagination.php';
require_once ABSPATH . WPINC . '/blocks/comments-pagination-next.php';
require_once ABSPATH . WPINC . '/blocks/comments-pagination-numbers.php';
require_once ABSPATH . WPINC . '/blocks/comments-pagination-previous.php';
require_once ABSPATH . WPINC . '/blocks/comments-title.php';
require_once ABSPATH . WPINC . '/blocks/cover.php';
require_once ABSPATH . WPINC . '/blocks/file.php';
require_once ABSPATH . WPINC . '/blocks/footnotes.php';
require_once ABSPATH . WPINC . '/blocks/gallery.php';
require_once ABSPATH . WPINC . '/blocks/heading.php';
require_once ABSPATH . WPINC . '/blocks/home-link.php';
require_once ABSPATH . WPINC . '/blocks/image.php';
require_once ABSPATH . WPINC . '/blocks/latest-comments.php';
require_once ABSPATH . WPINC . '/blocks/latest-posts.php';
require_once ABSPATH . WPINC . '/blocks/list.php';
require_once ABSPATH . WPINC . '/blocks/loginout.php';
require_once ABSPATH . WPINC . '/blocks/media-text.php';
require_once ABSPATH . WPINC . '/blocks/navigation.php';
require_once ABSPATH . WPINC . '/blocks/navigation-link.php';
require_once ABSPATH . WPINC . '/blocks/navigation-submenu.php';
require_once ABSPATH . WPINC . '/blocks/page-list.php';
require_once ABSPATH . WPINC . '/blocks/page-list-item.php';
require_once ABSPATH . WPINC . '/blocks/pattern.php';
require_once ABSPATH . WPINC . '/blocks/post-author.php';
require_once ABSPATH . WPINC . '/blocks/post-author-biography.php';
require_once ABSPATH . WPINC . '/blocks/post-author-name.php';
require_once ABSPATH . WPINC . '/blocks/post-comments-form.php';
require_once ABSPATH . WPINC . '/blocks/post-content.php';
require_once ABSPATH . WPINC . '/blocks/post-date.php';
require_once ABSPATH . WPINC . '/blocks/post-excerpt.php';
require_once ABSPATH . WPINC . '/blocks/post-featured-image.php';
require_once ABSPATH . WPINC . '/blocks/post-navigation-link.php';
require_once ABSPATH . WPINC . '/blocks/post-template.php';
require_once ABSPATH . WPINC . '/blocks/post-terms.php';
require_once ABSPATH . WPINC . '/blocks/post-title.php';
require_once ABSPATH . WPINC . '/blocks/query.php';
require_once ABSPATH . WPINC . '/blocks/query-no-results.php';
require_once ABSPATH . WPINC . '/blocks/query-pagination.php';
require_once ABSPATH . WPINC . '/blocks/query-pagination-next.php';
require_once ABSPATH . WPINC . '/blocks/query-pagination-numbers.php';
require_once ABSPATH . WPINC . '/blocks/query-pagination-previous.php';
require_once ABSPATH . WPINC . '/blocks/query-title.php';
require_once ABSPATH . WPINC . '/blocks/read-more.php';
require_once ABSPATH . WPINC . '/blocks/rss.php';
require_once ABSPATH . WPINC . '/blocks/search.php';
require_once ABSPATH . WPINC . '/blocks/shortcode.php';
require_once ABSPATH . WPINC . '/blocks/site-logo.php';
require_once ABSPATH . WPINC . '/blocks/site-tagline.php';
require_once ABSPATH . WPINC . '/blocks/site-title.php';
require_once ABSPATH . WPINC . '/blocks/social-link.php';
require_once ABSPATH . WPINC . '/blocks/tag-cloud.php';
require_once ABSPATH . WPINC . '/blocks/template-part.php';
require_once ABSPATH . WPINC . '/blocks/term-description.php';
