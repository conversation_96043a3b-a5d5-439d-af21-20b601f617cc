/*! This file is auto-generated */
(()=>{"use strict";var e,t,n={},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={exports:{}};return n[e](a,a.exports,o),a.exports}t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var a=Object.create(null);o.r(a);var c={};e=e||[null,t({}),t([]),t(t)];for(var u=2&r&&n;"object"==typeof u&&!~e.indexOf(u);u=t(u))Object.getOwnPropertyNames(u).forEach((e=>c[e]=()=>n[e]));return c.default=()=>n,o.d(a,c),a},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nc=void 0;var a={};o.r(a),o.d(a,{CommandMenu:()=>Zn,privateApis:()=>Jn,store:()=>qn,useCommand:()=>Qn,useCommandLoader:()=>er});var c={};o.r(c),o.d(c,{close:()=>In,open:()=>Pn,registerCommand:()=>Mn,registerCommandLoader:()=>Tn,unregisterCommand:()=>_n,unregisterCommandLoader:()=>Dn});var u={};o.r(u),o.d(u,{getCommandLoaders:()=>Fn,getCommands:()=>jn,getContext:()=>Un,isOpen:()=>Wn});var i={};o.r(i),o.d(i,{setContext:()=>$n});var l=1,s=.9,d=.8,f=.17,m=.1,v=.999,p=.9999,h=.99,g=/[\\\/_+.#"@\[\(\{&]/,b=/[\\\/_+.#"@\[\(\{&]/g,E=/[\s-]/,y=/[\s-]/g;function w(e,t,n,r,o,a,c){if(a===t.length)return o===e.length?l:h;var u=`${o},${a}`;if(void 0!==c[u])return c[u];for(var i,C,S,x,O=r.charAt(a),R=n.indexOf(O,o),k=0;R>=0;)(i=w(e,t,n,r,R+1,a+1,c))>k&&(R===o?i*=l:g.test(e.charAt(R-1))?(i*=d,(S=e.slice(o,R-1).match(b))&&o>0&&(i*=Math.pow(v,S.length))):E.test(e.charAt(R-1))?(i*=s,(x=e.slice(o,R-1).match(y))&&o>0&&(i*=Math.pow(v,x.length))):(i*=f,o>0&&(i*=Math.pow(v,R-o))),e.charAt(R)!==t.charAt(a)&&(i*=p)),(i<m&&n.charAt(R-1)===r.charAt(a+1)||r.charAt(a+1)===r.charAt(a)&&n.charAt(R-1)!==r.charAt(a))&&((C=w(e,t,n,r,R+1,a+2,c))*m>i&&(i=C*m)),i>k&&(k=i),R=n.indexOf(O,R+1);return c[u]=k,k}function C(e){return e.toLowerCase().replace(y," ")}function S(e,t,n){return w(e=n&&n.length>0?""+(e+" "+n.join(" ")):e,t,C(e),C(t),0,0,{})}function x(){return x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},x.apply(this,arguments)}const O=window.React;var R=o.t(O,2);function k(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function A(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function N(...e){return(0,O.useCallback)(A(...e),e)}function L(...e){const t=e[0];if(1===e.length)return t;const n=()=>{const n=e.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(e){const r=n.reduce(((t,{useScope:n,scopeName:r})=>({...t,...n(e)[`__scope${r}`]})),{});return(0,O.useMemo)((()=>({[`__scope${t.scopeName}`]:r})),[r])}};return n.scopeName=t.scopeName,n}const M=Boolean(null===globalThis||void 0===globalThis?void 0:globalThis.document)?O.useLayoutEffect:()=>{},_=R["useId".toString()]||(()=>{});let T=0;function D(e){const[t,n]=O.useState(_());return M((()=>{e||n((e=>null!=e?e:String(T++)))}),[e]),e||(t?`radix-${t}`:"")}function P(e){const t=(0,O.useRef)(e);return(0,O.useEffect)((()=>{t.current=e})),(0,O.useMemo)((()=>(...e)=>{var n;return null===(n=t.current)||void 0===n?void 0:n.call(t,...e)}),[])}function I({prop:e,defaultProp:t,onChange:n=(()=>{})}){const[r,o]=function({defaultProp:e,onChange:t}){const n=(0,O.useState)(e),[r]=n,o=(0,O.useRef)(r),a=P(t);return(0,O.useEffect)((()=>{o.current!==r&&(a(r),o.current=r)}),[r,o,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,c=a?e:r,u=P(n);return[c,(0,O.useCallback)((t=>{if(a){const n="function"==typeof t?t(e):t;n!==e&&u(n)}else o(t)}),[a,e,o,u])]}const j=window.ReactDOM,F=(0,O.forwardRef)(((e,t)=>{const{children:n,...r}=e,o=O.Children.toArray(n),a=o.find($);if(a){const e=a.props.children,n=o.map((t=>t===a?O.Children.count(e)>1?O.Children.only(null):(0,O.isValidElement)(e)?e.props.children:null:t));return(0,O.createElement)(W,x({},r,{ref:t}),(0,O.isValidElement)(e)?(0,O.cloneElement)(e,void 0,n):null)}return(0,O.createElement)(W,x({},r,{ref:t}),n)}));F.displayName="Slot";const W=(0,O.forwardRef)(((e,t)=>{const{children:n,...r}=e;return(0,O.isValidElement)(n)?(0,O.cloneElement)(n,{...B(r,n.props),ref:t?A(t,n.ref):n.ref}):O.Children.count(n)>1?O.Children.only(null):null}));W.displayName="SlotClone";const U=({children:e})=>(0,O.createElement)(O.Fragment,null,e);function $(e){return(0,O.isValidElement)(e)&&e.type===U}function B(e,t){const n={...t};for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}const K=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce(((e,t)=>{const n=(0,O.forwardRef)(((e,n)=>{const{asChild:r,...o}=e,a=r?F:t;return(0,O.useEffect)((()=>{window[Symbol.for("radix-ui")]=!0}),[]),(0,O.createElement)(a,x({},o,{ref:n}))}));return n.displayName=`Primitive.${t}`,{...e,[t]:n}}),{});const V="dismissableLayer.update",q="dismissableLayer.pointerDownOutside",z="dismissableLayer.focusOutside";let G;const H=(0,O.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),X=(0,O.forwardRef)(((e,t)=>{var n;const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:c,onInteractOutside:u,onDismiss:i,...l}=e,s=(0,O.useContext)(H),[d,f]=(0,O.useState)(null),m=null!==(n=null==d?void 0:d.ownerDocument)&&void 0!==n?n:null===globalThis||void 0===globalThis?void 0:globalThis.document,[,v]=(0,O.useState)({}),p=N(t,(e=>f(e))),h=Array.from(s.layers),[g]=[...s.layersWithOutsidePointerEventsDisabled].slice(-1),b=h.indexOf(g),E=d?h.indexOf(d):-1,y=s.layersWithOutsidePointerEventsDisabled.size>0,w=E>=b,C=function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const n=P(e),r=(0,O.useRef)(!1),o=(0,O.useRef)((()=>{}));return(0,O.useEffect)((()=>{const e=e=>{if(e.target&&!r.current){const a={originalEvent:e};function c(){Z(q,n,a,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=c,t.addEventListener("click",o.current,{once:!0})):c()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout((()=>{t.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}}),[t,n]),{onPointerDownCapture:()=>r.current=!0}}((e=>{const t=e.target,n=[...s.branches].some((e=>e.contains(t)));w&&!n&&(null==a||a(e),null==u||u(e),e.defaultPrevented||null==i||i())}),m),S=function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const n=P(e),r=(0,O.useRef)(!1);return(0,O.useEffect)((()=>{const e=e=>{if(e.target&&!r.current){Z(z,n,{originalEvent:e},{discrete:!1})}};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)}),[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}((e=>{const t=e.target;[...s.branches].some((e=>e.contains(t)))||(null==c||c(e),null==u||u(e),e.defaultPrevented||null==i||i())}),m);return function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const n=P(e);(0,O.useEffect)((()=>{const e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)}),[n,t])}((e=>{E===s.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&i&&(e.preventDefault(),i()))}),m),(0,O.useEffect)((()=>{if(d)return r&&(0===s.layersWithOutsidePointerEventsDisabled.size&&(G=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),s.layersWithOutsidePointerEventsDisabled.add(d)),s.layers.add(d),Y(),()=>{r&&1===s.layersWithOutsidePointerEventsDisabled.size&&(m.body.style.pointerEvents=G)}}),[d,m,r,s]),(0,O.useEffect)((()=>()=>{d&&(s.layers.delete(d),s.layersWithOutsidePointerEventsDisabled.delete(d),Y())}),[d,s]),(0,O.useEffect)((()=>{const e=()=>v({});return document.addEventListener(V,e),()=>document.removeEventListener(V,e)}),[]),(0,O.createElement)(K.div,x({},l,{ref:p,style:{pointerEvents:y?w?"auto":"none":void 0,...e.style},onFocusCapture:k(e.onFocusCapture,S.onFocusCapture),onBlurCapture:k(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:k(e.onPointerDownCapture,C.onPointerDownCapture)}))}));function Y(){const e=new CustomEvent(V);document.dispatchEvent(e)}function Z(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?function(e,t){e&&(0,j.flushSync)((()=>e.dispatchEvent(t)))}(o,a):o.dispatchEvent(a)}const J="focusScope.autoFocusOnMount",Q="focusScope.autoFocusOnUnmount",ee={bubbles:!1,cancelable:!0},te=(0,O.forwardRef)(((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[u,i]=(0,O.useState)(null),l=P(o),s=P(a),d=(0,O.useRef)(null),f=N(t,(e=>i(e))),m=(0,O.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,O.useEffect)((()=>{if(r){function e(e){if(m.paused||!u)return;const t=e.target;u.contains(t)?d.current=t:ae(d.current,{select:!0})}function t(e){if(m.paused||!u)return;const t=e.relatedTarget;null!==t&&(u.contains(t)||ae(d.current,{select:!0}))}function n(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&ae(u)}document.addEventListener("focusin",e),document.addEventListener("focusout",t);const o=new MutationObserver(n);return u&&o.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),o.disconnect()}}}),[r,u,m.paused]),(0,O.useEffect)((()=>{if(u){ce.add(m);const t=document.activeElement;if(!u.contains(t)){const n=new CustomEvent(J,ee);u.addEventListener(J,l),u.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ae(r,{select:t}),document.activeElement!==n)return}((e=ne(u),e.filter((e=>"A"!==e.tagName))),{select:!0}),document.activeElement===t&&ae(u))}return()=>{u.removeEventListener(J,l),setTimeout((()=>{const e=new CustomEvent(Q,ee);u.addEventListener(Q,s),u.dispatchEvent(e),e.defaultPrevented||ae(null!=t?t:document.body,{select:!0}),u.removeEventListener(Q,s),ce.remove(m)}),0)}}var e}),[u,l,s,m]);const v=(0,O.useCallback)((e=>{if(!n&&!r)return;if(m.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){const t=e.currentTarget,[r,a]=function(e){const t=ne(e),n=re(t,e),r=re(t.reverse(),e);return[n,r]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&ae(a,{select:!0})):(e.preventDefault(),n&&ae(r,{select:!0})):o===t&&e.preventDefault()}}),[n,r,m.paused]);return(0,O.createElement)(K.div,x({tabIndex:-1},c,{ref:f,onKeyDown:v}))}));function ne(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function re(e,t){for(const n of e)if(!oe(n,{upTo:t}))return n}function oe(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function ae(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}const ce=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null==n||n.pause()),e=ue(e,t),e.unshift(t)},remove(t){var n;e=ue(e,t),null===(n=e[0])||void 0===n||n.resume()}}}();function ue(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}const ie=(0,O.forwardRef)(((e,t)=>{var n;const{container:r=(null===globalThis||void 0===globalThis||null===(n=globalThis.document)||void 0===n?void 0:n.body),...o}=e;return r?j.createPortal((0,O.createElement)(K.div,x({},o,{ref:t})),r):null}));const le=e=>{const{present:t,children:n}=e,r=function(e){const[t,n]=(0,O.useState)(),r=(0,O.useRef)({}),o=(0,O.useRef)(e),a=(0,O.useRef)("none"),c=e?"mounted":"unmounted",[u,i]=function(e,t){return(0,O.useReducer)(((e,n)=>{const r=t[e][n];return null!=r?r:e}),e)}(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return(0,O.useEffect)((()=>{const e=se(r.current);a.current="mounted"===u?e:"none"}),[u]),M((()=>{const t=r.current,n=o.current;if(n!==e){const r=a.current,c=se(t);if(e)i("MOUNT");else if("none"===c||"none"===(null==t?void 0:t.display))i("UNMOUNT");else{i(n&&r!==c?"ANIMATION_OUT":"UNMOUNT")}o.current=e}}),[e,i]),M((()=>{if(t){const e=e=>{const n=se(r.current).includes(e.animationName);e.target===t&&n&&(0,j.flushSync)((()=>i("ANIMATION_END")))},n=e=>{e.target===t&&(a.current=se(r.current))};return t.addEventListener("animationstart",n),t.addEventListener("animationcancel",e),t.addEventListener("animationend",e),()=>{t.removeEventListener("animationstart",n),t.removeEventListener("animationcancel",e),t.removeEventListener("animationend",e)}}i("ANIMATION_END")}),[t,i]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:(0,O.useCallback)((e=>{e&&(r.current=getComputedStyle(e)),n(e)}),[])}}(t),o="function"==typeof n?n({present:r.isPresent}):O.Children.only(n),a=N(r.ref,o.ref);return"function"==typeof n||r.isPresent?(0,O.cloneElement)(o,{ref:a}):null};function se(e){return(null==e?void 0:e.animationName)||"none"}le.displayName="Presence";let de=0;function fe(){(0,O.useEffect)((()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:me()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:me()),de++,()=>{1===de&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),de--}}),[])}function me(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var ve=function(){return ve=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ve.apply(this,arguments)};function pe(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}Object.create;function he(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"==typeof SuppressedError&&SuppressedError;var ge="right-scroll-bar-position",be="width-before-scroll-bar";function Ee(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var ye="undefined"!=typeof window?O.useLayoutEffect:O.useEffect,we=new WeakMap;function Ce(e,t){var n,r,o,a=(n=t||null,r=function(t){return e.forEach((function(e){return Ee(e,t)}))},(o=(0,O.useState)((function(){return{value:n,callback:r,facade:{get current(){return o.value},set current(e){var t=o.value;t!==e&&(o.value=e,o.callback(e,t))}}}}))[0]).callback=r,o.facade);return ye((function(){var t=we.get(a);if(t){var n=new Set(t),r=new Set(e),o=a.current;n.forEach((function(e){r.has(e)||Ee(e,null)})),r.forEach((function(e){n.has(e)||Ee(e,o)}))}we.set(a,e)}),[e]),a}function Se(e){return e}function xe(e,t){void 0===t&&(t=Se);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},c=function(){return Promise.resolve().then(a)};c(),n={push:function(e){t.push(e),c()},filter:function(e){return t=t.filter(e),n}}}}}var Oe=function(e){void 0===e&&(e={});var t=xe(null);return t.options=ve({async:!0,ssr:!1},e),t}(),Re=function(){},ke=O.forwardRef((function(e,t){var n=O.useRef(null),r=O.useState({onScrollCapture:Re,onWheelCapture:Re,onTouchMoveCapture:Re}),o=r[0],a=r[1],c=e.forwardProps,u=e.children,i=e.className,l=e.removeScrollBar,s=e.enabled,d=e.shards,f=e.sideCar,m=e.noIsolation,v=e.inert,p=e.allowPinchZoom,h=e.as,g=void 0===h?"div":h,b=pe(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),E=f,y=Ce([n,t]),w=ve(ve({},b),o);return O.createElement(O.Fragment,null,s&&O.createElement(E,{sideCar:Oe,removeScrollBar:l,shards:d,noIsolation:m,inert:v,setCallbacks:a,allowPinchZoom:!!p,lockRef:n}),c?O.cloneElement(O.Children.only(u),ve(ve({},w),{ref:y})):O.createElement(g,ve({},w,{className:i,ref:y}),u))}));ke.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ke.classNames={fullWidth:be,zeroRight:ge};var Ae,Ne=function(e){var t=e.sideCar,n=pe(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return O.createElement(r,ve({},n))};Ne.isSideCarExport=!0;function Le(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Ae||o.nc;return t&&e.setAttribute("nonce",t),e}var Me=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=Le())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},_e=function(){var e,t=(e=Me(),function(t,n){O.useEffect((function(){return e.add(t),function(){e.remove()}}),[t&&n])});return function(e){var n=e.styles,r=e.dynamic;return t(n,r),null}},Te={left:0,top:0,right:0,gap:0},De=function(e){return parseInt(e||"",10)||0},Pe=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Te;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[De(n),De(r),De(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Ie=_e(),je="data-scroll-locked",Fe=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(je,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(ge," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(be," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(ge," .").concat(ge," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(be," .").concat(be," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(je,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},We=function(){var e=parseInt(document.body.getAttribute(je)||"0",10);return isFinite(e)?e:0},Ue=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;O.useEffect((function(){return document.body.setAttribute(je,(We()+1).toString()),function(){var e=We()-1;e<=0?document.body.removeAttribute(je):document.body.setAttribute(je,e.toString())}}),[]);var a=O.useMemo((function(){return Pe(o)}),[o]);return O.createElement(Ie,{styles:Fe(a,!t,o,n?"":"!important")})},$e=!1;if("undefined"!=typeof window)try{var Be=Object.defineProperty({},"passive",{get:function(){return $e=!0,!0}});window.addEventListener("test",Be,Be),window.removeEventListener("test",Be,Be)}catch(e){$e=!1}var Ke=!!$e&&{passive:!1},Ve=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},qe=function(e,t){var n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),ze(e,n)){var r=Ge(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},ze=function(e,t){return"v"===e?function(e){return Ve(e,"overflowY")}(t):function(e){return Ve(e,"overflowX")}(t)},Ge=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},He=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Xe=function(e){return[e.deltaX,e.deltaY]},Ye=function(e){return e&&"current"in e?e.current:e},Ze=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},Je=0,Qe=[];const et=(tt=function(e){var t=O.useRef([]),n=O.useRef([0,0]),r=O.useRef(),o=O.useState(Je++)[0],a=O.useState((function(){return _e()}))[0],c=O.useRef(e);O.useEffect((function(){c.current=e}),[e]),O.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=he([e.lockRef.current],(e.shards||[]).map(Ye),!0).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-".concat(o))})),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(o))}))}}}),[e.inert,e.lockRef.current,e.shards]);var u=O.useCallback((function(e,t){if("touches"in e&&2===e.touches.length)return!c.current.allowPinchZoom;var o,a=He(e),u=n.current,i="deltaX"in e?e.deltaX:u[0]-a[0],l="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,d=Math.abs(i)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=qe(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=qe(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(i||l)&&(r.current=o),!o)return!0;var m=r.current||o;return function(e,t,n,r,o){var a=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),c=a*r,u=n.target,i=t.contains(u),l=!1,s=c>0,d=0,f=0;do{var m=Ge(e,u),v=m[0],p=m[1]-m[2]-a*v;(v||p)&&ze(e,u)&&(d+=p,f+=v),u=u.parentNode}while(!i&&u!==document.body||i&&(t.contains(u)||t===u));return(s&&(o&&0===d||!o&&c>d)||!s&&(o&&0===f||!o&&-c>f))&&(l=!0),l}(m,t,e,"h"===m?i:l,!0)}),[]),i=O.useCallback((function(e){var n=e;if(Qe.length&&Qe[Qe.length-1]===a){var r="deltaY"in n?Xe(n):He(n),o=t.current.filter((function(e){return e.name===n.type&&e.target===n.target&&(t=e.delta,o=r,t[0]===o[0]&&t[1]===o[1]);var t,o}))[0];if(o&&o.should)n.cancelable&&n.preventDefault();else if(!o){var i=(c.current.shards||[]).map(Ye).filter(Boolean).filter((function(e){return e.contains(n.target)}));(i.length>0?u(n,i[0]):!c.current.noIsolation)&&n.cancelable&&n.preventDefault()}}}),[]),l=O.useCallback((function(e,n,r,o){var a={name:e,delta:n,target:r,should:o};t.current.push(a),setTimeout((function(){t.current=t.current.filter((function(e){return e!==a}))}),1)}),[]),s=O.useCallback((function(e){n.current=He(e),r.current=void 0}),[]),d=O.useCallback((function(t){l(t.type,Xe(t),t.target,u(t,e.lockRef.current))}),[]),f=O.useCallback((function(t){l(t.type,He(t),t.target,u(t,e.lockRef.current))}),[]);O.useEffect((function(){return Qe.push(a),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:f}),document.addEventListener("wheel",i,Ke),document.addEventListener("touchmove",i,Ke),document.addEventListener("touchstart",s,Ke),function(){Qe=Qe.filter((function(e){return e!==a})),document.removeEventListener("wheel",i,Ke),document.removeEventListener("touchmove",i,Ke),document.removeEventListener("touchstart",s,Ke)}}),[]);var m=e.removeScrollBar,v=e.inert;return O.createElement(O.Fragment,null,v?O.createElement(a,{styles:Ze(o)}):null,m?O.createElement(Ue,{gapMode:"margin"}):null)},Oe.useMedium(tt),Ne);var tt,nt=O.forwardRef((function(e,t){return O.createElement(ke,ve({},e,{ref:t,sideCar:et}))}));nt.classNames=ke.classNames;const rt=nt;var ot=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},at=new WeakMap,ct=new WeakMap,ut={},it=0,lt=function(e){return e&&(e.host||lt(e.parentNode))},st=function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=lt(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);ut[n]||(ut[n]=new WeakMap);var a=ut[n],c=[],u=new Set,i=new Set(o),l=function(e){e&&!u.has(e)&&(u.add(e),l(e.parentNode))};o.forEach(l);var s=function(e){e&&!i.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,i=(at.get(e)||0)+1,l=(a.get(e)||0)+1;at.set(e,i),a.set(e,l),c.push(e),1===i&&o&&ct.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}}))};return s(t),u.clear(),it++,function(){c.forEach((function(e){var t=at.get(e)-1,o=a.get(e)-1;at.set(e,t),a.set(e,o),t||(ct.has(e)||e.removeAttribute(r),ct.delete(e)),o||e.removeAttribute(n)})),--it||(at=new WeakMap,at=new WeakMap,ct=new WeakMap,ut={})}},dt=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ot(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),st(r,o,n,"aria-hidden")):function(){return null}};const ft="Dialog",[mt,vt]=function(e,t=[]){let n=[];const r=()=>{const t=n.map((e=>(0,O.createContext)(e)));return function(n){const r=(null==n?void 0:n[e])||t;return(0,O.useMemo)((()=>({[`__scope${e}`]:{...n,[e]:r}})),[n,r])}};return r.scopeName=e,[function(t,r){const o=(0,O.createContext)(r),a=n.length;function c(t){const{scope:n,children:r,...c}=t,u=(null==n?void 0:n[e][a])||o,i=(0,O.useMemo)((()=>c),Object.values(c));return(0,O.createElement)(u.Provider,{value:i},r)}return n=[...n,r],c.displayName=t+"Provider",[c,function(n,c){const u=(null==c?void 0:c[e][a])||o,i=(0,O.useContext)(u);if(i)return i;if(void 0!==r)return r;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},L(r,...t)]}(ft),[pt,ht]=mt(ft),gt=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,u=(0,O.useRef)(null),i=(0,O.useRef)(null),[l=!1,s]=I({prop:r,defaultProp:o,onChange:a});return(0,O.createElement)(pt,{scope:t,triggerRef:u,contentRef:i,contentId:D(),titleId:D(),descriptionId:D(),open:l,onOpenChange:s,onOpenToggle:(0,O.useCallback)((()=>s((e=>!e))),[s]),modal:c},n)},bt="DialogPortal",[Et,yt]=mt(bt,{forceMount:void 0}),wt=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=ht(bt,t);return(0,O.createElement)(Et,{scope:t,forceMount:n},O.Children.map(r,(e=>(0,O.createElement)(le,{present:n||a.open},(0,O.createElement)(ie,{asChild:!0,container:o},e)))))},Ct="DialogOverlay",St=(0,O.forwardRef)(((e,t)=>{const n=yt(Ct,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=ht(Ct,e.__scopeDialog);return a.modal?(0,O.createElement)(le,{present:r||a.open},(0,O.createElement)(xt,x({},o,{ref:t}))):null})),xt=(0,O.forwardRef)(((e,t)=>{const{__scopeDialog:n,...r}=e,o=ht(Ct,n);return(0,O.createElement)(rt,{as:F,allowPinchZoom:!0,shards:[o.contentRef]},(0,O.createElement)(K.div,x({"data-state":Mt(o.open)},r,{ref:t,style:{pointerEvents:"auto",...r.style}})))})),Ot="DialogContent",Rt=(0,O.forwardRef)(((e,t)=>{const n=yt(Ot,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=ht(Ot,e.__scopeDialog);return(0,O.createElement)(le,{present:r||a.open},a.modal?(0,O.createElement)(kt,x({},o,{ref:t})):(0,O.createElement)(At,x({},o,{ref:t})))})),kt=(0,O.forwardRef)(((e,t)=>{const n=ht(Ot,e.__scopeDialog),r=(0,O.useRef)(null),o=N(t,n.contentRef,r);return(0,O.useEffect)((()=>{const e=r.current;if(e)return dt(e)}),[]),(0,O.createElement)(Nt,x({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:k(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()})),onPointerDownOutside:k(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:k(e.onFocusOutside,(e=>e.preventDefault()))}))})),At=(0,O.forwardRef)(((e,t)=>{const n=ht(Ot,e.__scopeDialog),r=(0,O.useRef)(!1),o=(0,O.useRef)(!1);return(0,O.createElement)(Nt,x({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,c;(null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented)||(r.current||null===(c=n.triggerRef.current)||void 0===c||c.focus(),t.preventDefault());r.current=!1,o.current=!1},onInteractOutside:t=>{var a,c;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));const u=t.target;(null===(c=n.triggerRef.current)||void 0===c?void 0:c.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}}))})),Nt=(0,O.forwardRef)(((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...c}=e,u=ht(Ot,n),i=N(t,(0,O.useRef)(null));return fe(),(0,O.createElement)(O.Fragment,null,(0,O.createElement)(te,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a},(0,O.createElement)(X,x({role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Mt(u.open)},c,{ref:i,onDismiss:()=>u.onOpenChange(!1)}))),!1)})),Lt="DialogTitle";function Mt(e){return e?"open":"closed"}const _t="DialogTitleWarning",[Tt,Dt]=function(e,t){const n=(0,O.createContext)(t);function r(e){const{children:t,...r}=e,o=(0,O.useMemo)((()=>r),Object.values(r));return(0,O.createElement)(n.Provider,{value:o},t)}return r.displayName=e+"Provider",[r,function(r){const o=(0,O.useContext)(n);if(o)return o;if(void 0!==t)return t;throw new Error(`\`${r}\` must be used within \`${e}\``)}]}(_t,{contentName:Ot,titleName:Lt,docsSlug:"dialog"}),Pt=gt,It=wt,jt=St,Ft=Rt;var Wt='[cmdk-group=""]',Ut='[cmdk-group-items=""]',$t='[cmdk-item=""]',Bt=`${$t}:not([aria-disabled="true"])`,Kt="cmdk-item-select",Vt="data-value",qt=(e,t,n)=>S(e,t,n),zt=O.createContext(void 0),Gt=()=>O.useContext(zt),Ht=O.createContext(void 0),Xt=()=>O.useContext(Ht),Yt=O.createContext(void 0),Zt=O.forwardRef(((e,t)=>{let n=fn((()=>{var t,n;return{search:"",value:null!=(n=null!=(t=e.value)?t:e.defaultValue)?n:"",filtered:{count:0,items:new Map,groups:new Set}}})),r=fn((()=>new Set)),o=fn((()=>new Map)),a=fn((()=>new Map)),c=fn((()=>new Set)),u=sn(e),{label:i,children:l,value:s,onValueChange:d,filter:f,shouldFilter:m,loop:v,disablePointerSelection:p=!1,vimBindings:h=!0,...g}=e,b=O.useId(),E=O.useId(),y=O.useId(),w=O.useRef(null),C=hn();dn((()=>{if(void 0!==s){let e=s.trim();n.current.value=e,S.emit()}}),[s]),dn((()=>{C(6,L)}),[]);let S=O.useMemo((()=>({subscribe:e=>(c.current.add(e),()=>c.current.delete(e)),snapshot:()=>n.current,setState:(e,t,r)=>{var o,a,c;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)N(),k(),C(1,A);else if("value"===e&&(r||C(5,L),void 0!==(null==(o=u.current)?void 0:o.value))){let e=null!=t?t:"";return void(null==(c=(a=u.current).onValueChange)||c.call(a,e))}S.emit()}},emit:()=>{c.current.forEach((e=>e()))}})),[]),x=O.useMemo((()=>({value:(e,t,r)=>{var o;t!==(null==(o=a.current.get(e))?void 0:o.value)&&(a.current.set(e,{value:t,keywords:r}),n.current.filtered.items.set(e,R(t,r)),C(2,(()=>{k(),S.emit()})))},item:(e,t)=>(r.current.add(e),t&&(o.current.has(t)?o.current.get(t).add(e):o.current.set(t,new Set([e]))),C(3,(()=>{N(),k(),n.current.value||A(),S.emit()})),()=>{a.current.delete(e),r.current.delete(e),n.current.filtered.items.delete(e);let t=M();C(4,(()=>{N(),(null==t?void 0:t.getAttribute("id"))===e&&A(),S.emit()}))}),group:e=>(o.current.has(e)||o.current.set(e,new Set),()=>{a.current.delete(e),o.current.delete(e)}),filter:()=>u.current.shouldFilter,label:i||e["aria-label"],disablePointerSelection:p,listId:b,inputId:y,labelId:E,listInnerRef:w})),[]);function R(e,t){var r,o;let a=null!=(o=null==(r=u.current)?void 0:r.filter)?o:qt;return e?a(e,n.current.search,t):0}function k(){if(!n.current.search||!1===u.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach((n=>{let r=o.current.get(n),a=0;r.forEach((t=>{let n=e.get(t);a=Math.max(n,a)})),t.push([n,a])}));let r=w.current;_().sort(((t,n)=>{var r,o;let a=t.getAttribute("id"),c=n.getAttribute("id");return(null!=(r=e.get(c))?r:0)-(null!=(o=e.get(a))?o:0)})).forEach((e=>{let t=e.closest(Ut);t?t.appendChild(e.parentElement===t?e:e.closest(`${Ut} > *`)):r.appendChild(e.parentElement===r?e:e.closest(`${Ut} > *`))})),t.sort(((e,t)=>t[1]-e[1])).forEach((e=>{let t=w.current.querySelector(`${Wt}[${Vt}="${encodeURIComponent(e[0])}"]`);null==t||t.parentElement.appendChild(t)}))}function A(){let e=_().find((e=>"true"!==e.getAttribute("aria-disabled"))),t=null==e?void 0:e.getAttribute(Vt);S.setState("value",t||void 0)}function N(){var e,t,c,i;if(!n.current.search||!1===u.current.shouldFilter)return void(n.current.filtered.count=r.current.size);n.current.filtered.groups=new Set;let l=0;for(let o of r.current){let r=R(null!=(t=null==(e=a.current.get(o))?void 0:e.value)?t:"",null!=(i=null==(c=a.current.get(o))?void 0:c.keywords)?i:[]);n.current.filtered.items.set(o,r),r>0&&l++}for(let[e,t]of o.current)for(let r of t)if(n.current.filtered.items.get(r)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=l}function L(){var e,t,n;let r=M();r&&((null==(e=r.parentElement)?void 0:e.firstChild)===r&&(null==(n=null==(t=r.closest(Wt))?void 0:t.querySelector('[cmdk-group-heading=""]'))||n.scrollIntoView({block:"nearest"})),r.scrollIntoView({block:"nearest"}))}function M(){var e;return null==(e=w.current)?void 0:e.querySelector(`${$t}[aria-selected="true"]`)}function _(){var e;return Array.from(null==(e=w.current)?void 0:e.querySelectorAll(Bt))}function T(e){let t=_()[e];t&&S.setState("value",t.getAttribute(Vt))}function D(e){var t;let n=M(),r=_(),o=r.findIndex((e=>e===n)),a=r[o+e];null!=(t=u.current)&&t.loop&&(a=o+e<0?r[r.length-1]:o+e===r.length?r[0]:r[o+e]),a&&S.setState("value",a.getAttribute(Vt))}function P(e){let t,n=M(),r=null==n?void 0:n.closest(Wt);for(;r&&!t;)r=e>0?un(r,Wt):ln(r,Wt),t=null==r?void 0:r.querySelector(Bt);t?S.setState("value",t.getAttribute(Vt)):D(e)}let I=()=>T(_().length-1),j=e=>{e.preventDefault(),e.metaKey?I():e.altKey?P(1):D(1)},F=e=>{e.preventDefault(),e.metaKey?T(0):e.altKey?P(-1):D(-1)};return O.createElement(K.div,{ref:t,tabIndex:-1,...g,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=g.onKeyDown)||t.call(g,e),!e.defaultPrevented)switch(e.key){case"n":case"j":h&&e.ctrlKey&&j(e);break;case"ArrowDown":j(e);break;case"p":case"k":h&&e.ctrlKey&&F(e);break;case"ArrowUp":F(e);break;case"Home":e.preventDefault(),T(0);break;case"End":e.preventDefault(),I();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=M();if(t){let e=new Event(Kt);t.dispatchEvent(e)}}}}},O.createElement("label",{"cmdk-label":"",htmlFor:x.inputId,id:x.labelId,style:bn},i),gn(e,(e=>O.createElement(Ht.Provider,{value:S},O.createElement(zt.Provider,{value:x},e)))))})),Jt=O.forwardRef(((e,t)=>{var n,r;let o=O.useId(),a=O.useRef(null),c=O.useContext(Yt),u=Gt(),i=sn(e),l=null!=(r=null==(n=i.current)?void 0:n.forceMount)?r:null==c?void 0:c.forceMount;dn((()=>{if(!l)return u.item(o,null==c?void 0:c.id)}),[l]);let s=pn(o,a,[e.value,e.children,a],e.keywords),d=Xt(),f=vn((e=>e.value&&e.value===s.current)),m=vn((e=>!(!l&&!1!==u.filter())||(!e.search||e.filtered.items.get(o)>0)));function v(){var e,t;p(),null==(t=(e=i.current).onSelect)||t.call(e,s.current)}function p(){d.setState("value",s.current,!0)}if(O.useEffect((()=>{let t=a.current;if(t&&!e.disabled)return t.addEventListener(Kt,v),()=>t.removeEventListener(Kt,v)}),[m,e.onSelect,e.disabled]),!m)return null;let{disabled:h,value:g,onSelect:b,forceMount:E,keywords:y,...w}=e;return O.createElement(K.div,{ref:mn([a,t]),...w,id:o,"cmdk-item":"",role:"option","aria-disabled":!!h,"aria-selected":!!f,"data-disabled":!!h,"data-selected":!!f,onPointerMove:h||u.disablePointerSelection?void 0:p,onClick:h?void 0:v},e.children)})),Qt=O.forwardRef(((e,t)=>{let{heading:n,children:r,forceMount:o,...a}=e,c=O.useId(),u=O.useRef(null),i=O.useRef(null),l=O.useId(),s=Gt(),d=vn((e=>!(!o&&!1!==s.filter())||(!e.search||e.filtered.groups.has(c))));dn((()=>s.group(c)),[]),pn(c,u,[e.value,e.heading,i]);let f=O.useMemo((()=>({id:c,forceMount:o})),[o]);return O.createElement(K.div,{ref:mn([u,t]),...a,"cmdk-group":"",role:"presentation",hidden:!d||void 0},n&&O.createElement("div",{ref:i,"cmdk-group-heading":"","aria-hidden":!0,id:l},n),gn(e,(e=>O.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?l:void 0},O.createElement(Yt.Provider,{value:f},e)))))})),en=O.forwardRef(((e,t)=>{let{alwaysRender:n,...r}=e,o=O.useRef(null),a=vn((e=>!e.search));return n||a?O.createElement(K.div,{ref:mn([o,t]),...r,"cmdk-separator":"",role:"separator"}):null})),tn=O.forwardRef(((e,t)=>{let{onValueChange:n,...r}=e,o=null!=e.value,a=Xt(),c=vn((e=>e.search)),u=vn((e=>e.value)),i=Gt(),l=O.useMemo((()=>{var e;let t=null==(e=i.listInnerRef.current)?void 0:e.querySelector(`${$t}[${Vt}="${encodeURIComponent(u)}"]`);return null==t?void 0:t.getAttribute("id")}),[]);return O.useEffect((()=>{null!=e.value&&a.setState("search",e.value)}),[e.value]),O.createElement(K.input,{ref:t,...r,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":i.listId,"aria-labelledby":i.labelId,"aria-activedescendant":l,id:i.inputId,type:"text",value:o?e.value:c,onChange:e=>{o||a.setState("search",e.target.value),null==n||n(e.target.value)}})})),nn=O.forwardRef(((e,t)=>{let{children:n,label:r="Suggestions",...o}=e,a=O.useRef(null),c=O.useRef(null),u=Gt();return O.useEffect((()=>{if(c.current&&a.current){let e,t=c.current,n=a.current,r=new ResizeObserver((()=>{e=requestAnimationFrame((()=>{let e=t.offsetHeight;n.style.setProperty("--cmdk-list-height",e.toFixed(1)+"px")}))}));return r.observe(t),()=>{cancelAnimationFrame(e),r.unobserve(t)}}}),[]),O.createElement(K.div,{ref:mn([a,t]),...o,"cmdk-list":"",role:"listbox","aria-label":r,id:u.listId},gn(e,(e=>O.createElement("div",{ref:mn([c,u.listInnerRef]),"cmdk-list-sizer":""},e))))})),rn=O.forwardRef(((e,t)=>{let{open:n,onOpenChange:r,overlayClassName:o,contentClassName:a,container:c,...u}=e;return O.createElement(Pt,{open:n,onOpenChange:r},O.createElement(It,{container:c},O.createElement(jt,{"cmdk-overlay":"",className:o}),O.createElement(Ft,{"aria-label":e.label,"cmdk-dialog":"",className:a},O.createElement(Zt,{ref:t,...u}))))})),on=O.forwardRef(((e,t)=>vn((e=>0===e.filtered.count))?O.createElement(K.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null)),an=O.forwardRef(((e,t)=>{let{progress:n,children:r,label:o="Loading...",...a}=e;return O.createElement(K.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},gn(e,(e=>O.createElement("div",{"aria-hidden":!0},e))))})),cn=Object.assign(Zt,{List:nn,Item:Jt,Input:tn,Group:Qt,Separator:en,Dialog:rn,Empty:on,Loading:an});function un(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}function ln(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}function sn(e){let t=O.useRef(e);return dn((()=>{t.current=e})),t}var dn="undefined"==typeof window?O.useEffect:O.useLayoutEffect;function fn(e){let t=O.useRef();return void 0===t.current&&(t.current=e()),t}function mn(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function vn(e){let t=Xt(),n=()=>e(t.snapshot());return O.useSyncExternalStore(t.subscribe,n,n)}function pn(e,t,n,r=[]){let o=O.useRef(),a=Gt();return dn((()=>{var c;let u=(()=>{var e;for(let t of n){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():o.current}})(),i=r.map((e=>e.trim()));a.value(e,u,i),null==(c=t.current)||c.setAttribute(Vt,u),o.current=u})),o}var hn=()=>{let[e,t]=O.useState(),n=fn((()=>new Map));return dn((()=>{n.current.forEach((e=>e())),n.current=new Map}),[e]),(e,r)=>{n.current.set(e,r),t({})}};function gn({asChild:e,children:t},n){return e&&O.isValidElement(t)?O.cloneElement(function(e){let t=e.type;return"function"==typeof t?t(e.props):"render"in t?t.render(e.props):e}(t),{ref:t.ref},n(t.props.children)):n(t)}var bn={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function En(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=En(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}const yn=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=En(e))&&(r&&(r+=" "),r+=t);return r},wn=window.wp.data,Cn=window.wp.element,Sn=window.wp.i18n,xn=window.wp.components,On=window.wp.keyboardShortcuts;const Rn=(0,Cn.forwardRef)((function({icon:e,size:t=24,...n},r){return(0,Cn.cloneElement)(e,{width:t,height:t,...n,ref:r})})),kn=window.wp.primitives,An=window.ReactJSXRuntime,Nn=(0,An.jsx)(kn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,An.jsx)(kn.Path,{d:"M13 5c-3.3 0-6 2.7-6 6 0 1.4.5 2.7 1.3 3.7l-3.8 3.8 1.1 1.1 3.8-3.8c1 .8 2.3 1.3 3.7 1.3 3.3 0 6-2.7 6-6S16.3 5 13 5zm0 10.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5z"})});const Ln=(0,wn.combineReducers)({commands:function(e={},t){switch(t.type){case"REGISTER_COMMAND":return{...e,[t.name]:{name:t.name,label:t.label,searchLabel:t.searchLabel,context:t.context,callback:t.callback,icon:t.icon}};case"UNREGISTER_COMMAND":{const{[t.name]:n,...r}=e;return r}}return e},commandLoaders:function(e={},t){switch(t.type){case"REGISTER_COMMAND_LOADER":return{...e,[t.name]:{name:t.name,context:t.context,hook:t.hook}};case"UNREGISTER_COMMAND_LOADER":{const{[t.name]:n,...r}=e;return r}}return e},isOpen:function(e=!1,t){switch(t.type){case"OPEN":return!0;case"CLOSE":return!1}return e},context:function(e="root",t){return"SET_CONTEXT"===t.type?t.context:e}});function Mn(e){return{type:"REGISTER_COMMAND",...e}}function _n(e){return{type:"UNREGISTER_COMMAND",name:e}}function Tn(e){return{type:"REGISTER_COMMAND_LOADER",...e}}function Dn(e){return{type:"UNREGISTER_COMMAND_LOADER",name:e}}function Pn(){return{type:"OPEN"}}function In(){return{type:"CLOSE"}}const jn=(0,wn.createSelector)(((e,t=!1)=>Object.values(e.commands).filter((n=>{const r=n.context&&n.context===e.context;return t?r:!r}))),(e=>[e.commands,e.context])),Fn=(0,wn.createSelector)(((e,t=!1)=>Object.values(e.commandLoaders).filter((n=>{const r=n.context&&n.context===e.context;return t?r:!r}))),(e=>[e.commandLoaders,e.context]));function Wn(e){return e.isOpen}function Un(e){return e.context}function $n(e){return{type:"SET_CONTEXT",context:e}}const Bn=window.wp.privateApis,{lock:Kn,unlock:Vn}=(0,Bn.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/commands"),qn=(0,wn.createReduxStore)("core/commands",{reducer:Ln,actions:c,selectors:u});(0,wn.register)(qn),Vn(qn).registerPrivateActions(i);const zn=(0,Sn.__)("Search commands and settings");function Gn({name:e,search:t,hook:n,setLoader:r,close:o}){var a;const{isLoading:c,commands:u=[]}=null!==(a=n({search:t}))&&void 0!==a?a:{};return(0,Cn.useEffect)((()=>{r(e,c)}),[r,e,c]),u.length?(0,An.jsx)(An.Fragment,{children:u.map((e=>{var n;return(0,An.jsx)(cn.Item,{value:null!==(n=e.searchLabel)&&void 0!==n?n:e.label,onSelect:()=>e.callback({close:o}),id:e.name,children:(0,An.jsxs)(xn.__experimentalHStack,{alignment:"left",className:yn("commands-command-menu__item",{"has-icon":e.icon}),children:[e.icon&&(0,An.jsx)(Rn,{icon:e.icon}),(0,An.jsx)("span",{children:(0,An.jsx)(xn.TextHighlight,{text:e.label,highlight:t})})]})},e.name)}))}):null}function Hn({hook:e,search:t,setLoader:n,close:r}){const o=(0,Cn.useRef)(e),[a,c]=(0,Cn.useState)(0);return(0,Cn.useEffect)((()=>{o.current!==e&&(o.current=e,c((e=>e+1)))}),[e]),(0,An.jsx)(Gn,{hook:o.current,search:t,setLoader:n,close:r},a)}function Xn({isContextual:e,search:t,setLoader:n,close:r}){const{commands:o,loaders:a}=(0,wn.useSelect)((t=>{const{getCommands:n,getCommandLoaders:r}=t(qn);return{commands:n(e),loaders:r(e)}}),[e]);return o.length||a.length?(0,An.jsxs)(cn.Group,{children:[o.map((e=>{var n;return(0,An.jsx)(cn.Item,{value:null!==(n=e.searchLabel)&&void 0!==n?n:e.label,onSelect:()=>e.callback({close:r}),id:e.name,children:(0,An.jsxs)(xn.__experimentalHStack,{alignment:"left",className:yn("commands-command-menu__item",{"has-icon":e.icon}),children:[e.icon&&(0,An.jsx)(Rn,{icon:e.icon}),(0,An.jsx)("span",{children:(0,An.jsx)(xn.TextHighlight,{text:e.label,highlight:t})})]})},e.name)})),a.map((e=>(0,An.jsx)(Hn,{hook:e.hook,search:t,setLoader:n,close:r},e.name)))]}):null}function Yn({isOpen:e,search:t,setSearch:n}){const r=(0,Cn.useRef)(),o=vn((e=>e.value)),a=(0,Cn.useMemo)((()=>{const e=document.querySelector(`[cmdk-item=""][data-value="${o}"]`);return e?.getAttribute("id")}),[o]);return(0,Cn.useEffect)((()=>{e&&r.current.focus()}),[e]),(0,An.jsx)(cn.Input,{ref:r,value:t,onValueChange:n,placeholder:zn,"aria-activedescendant":a,icon:t})}function Zn(){const{registerShortcut:e}=(0,wn.useDispatch)(On.store),[t,n]=(0,Cn.useState)(""),r=(0,wn.useSelect)((e=>e(qn).isOpen()),[]),{open:o,close:a}=(0,wn.useDispatch)(qn),[c,u]=(0,Cn.useState)({});(0,Cn.useEffect)((()=>{e({name:"core/commands",category:"global",description:(0,Sn.__)("Open the command palette."),keyCombination:{modifier:"primary",character:"k"}})}),[e]),(0,On.useShortcut)("core/commands",(e=>{e.defaultPrevented||(e.preventDefault(),r?a():o())}),{bindGlobal:!0});const i=(0,Cn.useCallback)(((e,t)=>u((n=>({...n,[e]:t})))),[]),l=()=>{n(""),a()};if(!r)return!1;const s=Object.values(c).some(Boolean);return(0,An.jsx)(xn.Modal,{className:"commands-command-menu",overlayClassName:"commands-command-menu__overlay",onRequestClose:l,__experimentalHideHeader:!0,contentLabel:(0,Sn.__)("Command palette"),children:(0,An.jsx)("div",{className:"commands-command-menu__container",children:(0,An.jsxs)(cn,{label:zn,onKeyDown:e=>{(e.nativeEvent.isComposing||229===e.keyCode)&&e.preventDefault()},children:[(0,An.jsxs)("div",{className:"commands-command-menu__header",children:[(0,An.jsx)(Yn,{search:t,setSearch:n,isOpen:r}),(0,An.jsx)(Rn,{icon:Nn})]}),(0,An.jsxs)(cn.List,{label:(0,Sn.__)("Command suggestions"),children:[t&&!s&&(0,An.jsx)(cn.Empty,{children:(0,Sn.__)("No results found.")}),(0,An.jsx)(Xn,{search:t,setLoader:i,close:l,isContextual:!0}),t&&(0,An.jsx)(Xn,{search:t,setLoader:i,close:l})]})]})})})}const Jn={};function Qn(e){const{registerCommand:t,unregisterCommand:n}=(0,wn.useDispatch)(qn),r=(0,Cn.useRef)(e.callback);(0,Cn.useEffect)((()=>{r.current=e.callback}),[e.callback]),(0,Cn.useEffect)((()=>{if(!e.disabled)return t({name:e.name,context:e.context,label:e.label,searchLabel:e.searchLabel,icon:e.icon,callback:(...e)=>r.current(...e)}),()=>{n(e.name)}}),[e.name,e.label,e.searchLabel,e.icon,e.context,e.disabled,t,n])}function er(e){const{registerCommandLoader:t,unregisterCommandLoader:n}=(0,wn.useDispatch)(qn);(0,Cn.useEffect)((()=>{if(!e.disabled)return t({name:e.name,hook:e.hook,context:e.context}),()=>{n(e.name)}}),[e.name,e.hook,e.context,e.disabled,t,n])}Kn(Jn,{useCommandContext:function(e){const{getContext:t}=(0,wn.useSelect)(qn),n=(0,Cn.useRef)(t()),{setContext:r}=Vn((0,wn.useDispatch)(qn));(0,Cn.useEffect)((()=>{r(e)}),[e,r]),(0,Cn.useEffect)((()=>{const e=n.current;return()=>r(e)}),[r])}}),(window.wp=window.wp||{}).commands=a})();