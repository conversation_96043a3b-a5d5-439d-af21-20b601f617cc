/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,n)=>{for(var i in n)e.o(n,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:n[i]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{duplicatePattern:()=>q,duplicatePost:()=>M,duplicatePostNative:()=>L,exportPattern:()=>Ee,exportPatternNative:()=>De,orderField:()=>l,permanentlyDeletePost:()=>T,reorderPage:()=>P,reorderPageNative:()=>A,titleField:()=>s,viewPost:()=>f,viewPostRevisions:()=>z});const n=window.wp.i18n,i=window.wp.htmlEntities,r="wp_template",o="wp_template_part";function a(e){return"string"==typeof e.title?(0,i.decodeEntities)(e.title):"rendered"in e.title?(0,i.decodeEntities)(e.title.rendered):"raw"in e.title?(0,i.decodeEntities)(e.title.raw):""}const s={type:"text",id:"title",label:(0,n.__)("Title"),placeholder:(0,n.__)("No title"),getValue:({item:e})=>a(e)},l={type:"integer",id:"menu_order",label:(0,n.__)("Order"),description:(0,n.__)("Determines the order of pages.")},c=window.wp.primitives,d=window.ReactJSXRuntime,u=(0,d.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,d.jsx)(c.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),f={id:"view-post",label:(0,n._x)("View","verb"),isPrimary:!0,icon:u,isEligible:e=>"trash"!==e.status,callback(e,{onActionPerformed:t}){const n=e[0];window.open(n?.link,"_blank"),t&&t(e)}},p=window.wp.data,m=window.wp.coreData,g=window.wp.notices,h=window.wp.element;const w={sort:function(e,t,n){return"asc"===n?e-t:t-e},isValid:function(e,t){if(""===e)return!1;if(!Number.isInteger(Number(e)))return!1;if(t?.elements){const n=t?.elements.map((e=>e.value));if(!n.includes(Number(e)))return!1}return!0},Edit:"integer"};const y={sort:function(e,t,n){return"asc"===n?e.localeCompare(t):t.localeCompare(e)},isValid:function(e,t){if(t?.elements){const n=t?.elements?.map((e=>e.value));if(!n.includes(e))return!1}return!0},Edit:"text"};const _={sort:function(e,t,n){const i=new Date(e).getTime(),r=new Date(t).getTime();return"asc"===n?i-r:r-i},isValid:function(e,t){if(t?.elements){const n=t?.elements.map((e=>e.value));if(!n.includes(e))return!1}return!0},Edit:"datetime"};const b=window.wp.components;const v={datetime:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){const{id:r,label:o}=t,a=t.getValue({item:e}),s=(0,h.useCallback)((e=>n({[r]:e})),[r,n]);return(0,d.jsxs)("fieldset",{className:"dataviews-controls__datetime",children:[!i&&(0,d.jsx)(b.BaseControl.VisualLabel,{as:"legend",children:o}),i&&(0,d.jsx)(b.VisuallyHidden,{as:"legend",children:o}),(0,d.jsx)(b.TimePicker,{currentTime:a,onChange:s,hideLabelFromVision:!0})]})},integer:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){var r;const{id:o,label:a,description:s}=t,l=null!==(r=t.getValue({item:e}))&&void 0!==r?r:"",c=(0,h.useCallback)((e=>n({[o]:Number(e)})),[o,n]);return(0,d.jsx)(b.__experimentalNumberControl,{label:a,help:s,value:l,onChange:c,__next40pxDefaultSize:!0,hideLabelFromVision:i})},radio:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){const{id:r,label:o}=t,a=t.getValue({item:e}),s=(0,h.useCallback)((e=>n({[r]:e})),[r,n]);return t.elements?(0,d.jsx)(b.RadioControl,{label:o,onChange:s,options:t.elements,selected:a,hideLabelFromVision:i}):null},select:function({data:e,field:t,onChange:i,hideLabelFromVision:r}){var o,a;const{id:s,label:l}=t,c=null!==(o=t.getValue({item:e}))&&void 0!==o?o:"",u=(0,h.useCallback)((e=>i({[s]:e})),[s,i]),f=[{label:(0,n.__)("Select item"),value:""},...null!==(a=t?.elements)&&void 0!==a?a:[]];return(0,d.jsx)(b.SelectControl,{label:l,value:c,options:f,onChange:u,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:r})},text:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){const{id:r,label:o,placeholder:a}=t,s=t.getValue({item:e}),l=(0,h.useCallback)((e=>n({[r]:e})),[r,n]);return(0,d.jsx)(b.TextControl,{label:o,placeholder:a,value:null!=s?s:"",onChange:l,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:i})}};function x(e){if(Object.keys(v).includes(e))return v[e];throw"Control "+e+" not found"}function j(e){return e.map((e=>{var t,n,i,r;const o="integer"===(a=e.type)?w:"text"===a?y:"datetime"===a?_:{sort:(e,t,n)=>"number"==typeof e&&"number"==typeof t?"asc"===n?e-t:t-e:"asc"===n?e.localeCompare(t):t.localeCompare(e),isValid:(e,t)=>{if(t?.elements){const n=t?.elements?.map((e=>e.value));if(!n.includes(e))return!1}return!0},Edit:()=>null};var a;const s=e.getValue||(({item:t})=>t[e.id]),l=null!==(t=e.sort)&&void 0!==t?t:function(e,t,n){return o.sort(s({item:e}),s({item:t}),n)},c=null!==(n=e.isValid)&&void 0!==n?n:function(e,t){return o.isValid(s({item:e}),t)},d=function(e,t){return"function"==typeof e.Edit?e.Edit:"string"==typeof e.Edit?x(e.Edit):e.elements?x("select"):"string"==typeof t.Edit?x(t.Edit):t.Edit}(e,o),u=e.render||(e.elements?({item:t})=>{const n=s({item:t});return e?.elements?.find((e=>e.value===n))?.label||s({item:t})}:s);return{...e,label:e.label||e.id,header:e.header||e.label||e.id,getValue:s,render:u,sort:l,isValid:c,Edit:d,enableHiding:null===(i=e.enableHiding)||void 0===i||i,enableSorting:null===(r=e.enableSorting)||void 0===r||r}}))}function S(e,t,n){return j(t.filter((({id:e})=>!!n.fields?.includes(e)))).every((t=>t.isValid(e,{elements:t.elements})))}const U=(0,d.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,d.jsx)(c.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})});function B({title:e,onClose:t}){return(0,d.jsx)(b.__experimentalVStack,{className:"dataforms-layouts-panel__dropdown-header",spacing:4,children:(0,d.jsxs)(b.__experimentalHStack,{alignment:"center",children:[(0,d.jsx)(b.__experimentalHeading,{level:2,size:13,children:e}),(0,d.jsx)(b.__experimentalSpacer,{}),t&&(0,d.jsx)(b.Button,{label:(0,n.__)("Close"),icon:U,onClick:t,size:"small"})]})})}function C({data:e,field:t,onChange:i}){const[r,o]=(0,h.useState)(null),a=(0,h.useMemo)((()=>({anchor:r,placement:"left-start",offset:36,shift:!0})),[r]);return(0,d.jsxs)(b.__experimentalHStack,{ref:o,className:"dataforms-layouts-panel__field",children:[(0,d.jsx)("div",{className:"dataforms-layouts-panel__field-label",children:t.label}),(0,d.jsx)("div",{children:(0,d.jsx)(b.Dropdown,{contentClassName:"dataforms-layouts-panel__field-dropdown",popoverProps:a,focusOnMount:!0,toggleProps:{size:"compact",variant:"tertiary",tooltipPosition:"middle left"},renderToggle:({isOpen:i,onToggle:r})=>(0,d.jsx)(b.Button,{className:"dataforms-layouts-panel__field-control",size:"compact",variant:"tertiary","aria-expanded":i,"aria-label":(0,n.sprintf)((0,n._x)("Edit %s","field"),t.label),onClick:r,children:(0,d.jsx)(t.render,{item:e})}),renderContent:({onClose:n})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(B,{title:t.label,onClose:n}),(0,d.jsx)(t.Edit,{data:e,field:t,onChange:i,hideLabelFromVision:!0},t.id)]})})})]})}const V=[{type:"regular",component:function({data:e,fields:t,form:n,onChange:i}){const r=(0,h.useMemo)((()=>{var e;return j((null!==(e=n.fields)&&void 0!==e?e:[]).map((e=>t.find((({id:t})=>t===e)))).filter((e=>!!e)))}),[t,n.fields]);return(0,d.jsx)(b.__experimentalVStack,{spacing:4,children:r.map((t=>(0,d.jsx)(t.Edit,{data:e,field:t,onChange:i},t.id)))})}},{type:"panel",component:function({data:e,fields:t,form:n,onChange:i}){const r=(0,h.useMemo)((()=>{var e;return j((null!==(e=n.fields)&&void 0!==e?e:[]).map((e=>t.find((({id:t})=>t===e)))).filter((e=>!!e)))}),[t,n.fields]);return(0,d.jsx)(b.__experimentalVStack,{spacing:2,children:r.map((t=>(0,d.jsx)(C,{data:e,field:t,onChange:i},t.id)))})}}];function k({form:e,...t}){var n;const i=(r=null!==(n=e.type)&&void 0!==n?n:"regular",V.find((e=>e.type===r)));var r;return i?(0,d.jsx)(i.component,{form:e,...t}):null}const E=[l],D={fields:["menu_order"]};const P={id:"order-pages",label:(0,n.__)("Order"),isEligible:({status:e})=>"trash"!==e,RenderModal:function({items:e,closeModal:t,onActionPerformed:i}){const[r,o]=(0,h.useState)(e[0]),a=r.menu_order,{editEntityRecord:s,saveEditedEntityRecord:l}=(0,p.useDispatch)(m.store),{createSuccessNotice:c,createErrorNotice:u}=(0,p.useDispatch)(g.store),f=!S(r,E,D);return(0,d.jsx)("form",{onSubmit:async function(o){if(o.preventDefault(),S(r,E,D))try{await s("postType",r.type,r.id,{menu_order:a}),t?.(),await l("postType",r.type,r.id,{throwOnError:!0}),c((0,n.__)("Order updated."),{type:"snackbar"}),i?.(e)}catch(e){const t=e,i=t.message&&"unknown_error"!==t.code?t.message:(0,n.__)("An error occurred while updating the order");u(i,{type:"snackbar"})}},children:(0,d.jsxs)(b.__experimentalVStack,{spacing:"5",children:[(0,d.jsx)("div",{children:(0,n.__)("Determines the order of pages. Pages with the same order value are sorted alphabetically. Negative order values are supported.")}),(0,d.jsx)(k,{data:r,fields:E,form:D,onChange:e=>o({...r,...e})}),(0,d.jsxs)(b.__experimentalHStack,{justify:"right",children:[(0,d.jsx)(b.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{t?.()},children:(0,n.__)("Cancel")}),(0,d.jsx)(b.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",accessibleWhenDisabled:!0,disabled:f,children:(0,n.__)("Save")})]})]})})}},A=undefined,N=[s],F={fields:["title"]},M={id:"duplicate-post",label:(0,n._x)("Duplicate","action label"),isEligible:({status:e})=>"trash"!==e,RenderModal:({items:e,closeModal:t,onActionPerformed:r})=>{const[o,s]=(0,h.useState)({...e[0],title:(0,n.sprintf)((0,n._x)("%s (Copy)","template"),a(e[0]))}),[l,c]=(0,h.useState)(!1),{saveEntityRecord:u}=(0,p.useDispatch)(m.store),{createSuccessNotice:f,createErrorNotice:w}=(0,p.useDispatch)(g.store);return(0,d.jsx)("form",{onSubmit:async function(e){if(e.preventDefault(),l)return;const a={status:"draft",title:o.title,slug:o.title||(0,n.__)("No title"),comment_status:o.comment_status,content:"string"==typeof o.content?o.content:o.content.raw,excerpt:"string"==typeof o.excerpt?o.excerpt:o.excerpt?.raw,meta:o.meta,parent:o.parent,password:o.password,template:o.template,format:o.format,featured_media:o.featured_media,menu_order:o.menu_order,ping_status:o.ping_status},s="wp:action-assign-";Object.keys(o?._links||{}).filter((e=>e.startsWith(s))).map((e=>e.slice(17))).forEach((e=>{o.hasOwnProperty(e)&&(a[e]=o[e])})),c(!0);try{const e=await u("postType",o.type,a,{throwOnError:!0});f((0,n.sprintf)((0,n.__)('"%s" successfully created.'),(0,i.decodeEntities)(e.title?.rendered||o.title)),{id:"duplicate-post-action",type:"snackbar"}),r&&r([e])}catch(e){const t=e,i=t.message&&"unknown_error"!==t.code?t.message:(0,n.__)("An error occurred while duplicating the page.");w(i,{type:"snackbar"})}finally{c(!1),t?.()}},children:(0,d.jsxs)(b.__experimentalVStack,{spacing:3,children:[(0,d.jsx)(k,{data:o,fields:N,form:F,onChange:e=>s((t=>({...t,...e})))}),(0,d.jsxs)(b.__experimentalHStack,{spacing:2,justify:"end",children:[(0,d.jsx)(b.Button,{variant:"tertiary",onClick:t,__next40pxDefaultSize:!0,children:(0,n.__)("Cancel")}),(0,d.jsx)(b.Button,{variant:"primary",type:"submit",isBusy:l,"aria-disabled":l,__next40pxDefaultSize:!0,children:(0,n._x)("Duplicate","action label")})]})]})})}},L=undefined,O=window.wp.url,z={id:"view-post-revisions",context:"list",label(e){var t;const i=null!==(t=e[0]._links?.["version-history"]?.[0]?.count)&&void 0!==t?t:0;return(0,n.sprintf)((0,n.__)("View revisions (%s)"),i)},isEligible(e){var t,n;if("trash"===e.status)return!1;const i=null!==(t=e?._links?.["predecessor-version"]?.[0]?.id)&&void 0!==t?t:null,r=null!==(n=e?._links?.["version-history"]?.[0]?.count)&&void 0!==n?n:0;return!!i&&r>1},callback(e,{onActionPerformed:t}){const n=e[0],i=(0,O.addQueryArgs)("revision.php",{revision:n?._links?.["predecessor-version"]?.[0]?.id});document.location.href=i,t&&t(e)}},I=(0,d.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,d.jsx)(c.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})}),R={id:"permanently-delete",label:(0,n.__)("Permanently delete"),supportsBulk:!0,icon:I,isEligible(e){if(function(e){return e.type===r||e.type===o}(e)||"wp_block"===e.type)return!1;const{status:t,permissions:n}=e;return"trash"===t&&n?.delete},async callback(e,{registry:t,onActionPerformed:i}){const{createSuccessNotice:r,createErrorNotice:o}=t.dispatch(g.store),{deleteEntityRecord:s}=t.dispatch(m.store),l=await Promise.allSettled(e.map((e=>s("postType",e.type,e.id,{force:!0},{throwOnError:!0}))));if(l.every((({status:e})=>"fulfilled"===e))){let t;t=1===l.length?(0,n.sprintf)((0,n.__)('"%s" permanently deleted.'),a(e[0])):(0,n.__)("The items were permanently deleted."),r(t,{type:"snackbar",id:"permanently-delete-post-action"}),i?.(e)}else{let e;if(1===l.length){const t=l[0];e=t.reason?.message?t.reason.message:(0,n.__)("An error occurred while permanently deleting the item.")}else{const t=new Set,i=l.filter((({status:e})=>"rejected"===e));for(const e of i){const n=e;n.reason?.message&&t.add(n.reason.message)}e=0===t.size?(0,n.__)("An error occurred while permanently deleting the items."):1===t.size?(0,n.sprintf)((0,n.__)("An error occurred while permanently deleting the items: %s"),[...t][0]):(0,n.sprintf)((0,n.__)("Some errors occurred while permanently deleting the items: %s"),[...t].join(","))}o(e,{type:"snackbar"})}}},T=R,H=window.wp.patterns,Z=window.wp.privateApis,{lock:$,unlock:G}=(0,Z.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/fields"),{CreatePatternModalContents:J,useDuplicatePatternProps:W}=G(H.privateApis),q={id:"duplicate-pattern",label:(0,n._x)("Duplicate","action label"),isEligible:e=>"wp_template_part"!==e.type,modalHeader:(0,n._x)("Duplicate pattern","action label"),RenderModal:({items:e,closeModal:t})=>{const[i]=e,r=W({pattern:i,onSuccess:()=>t?.()});return(0,d.jsx)(J,{onClose:t,confirmLabel:(0,n._x)("Duplicate","action label"),...r})}};var Q=function(){return Q=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},Q.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function X(e){return e.toLowerCase()}var Y=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],K=/[^A-Z0-9]+/gi;function ee(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function te(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,i=void 0===n?Y:n,r=t.stripRegexp,o=void 0===r?K:r,a=t.transform,s=void 0===a?X:a,l=t.delimiter,c=void 0===l?" ":l,d=ee(ee(e,i,"$1\0$2"),o,"\0"),u=0,f=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(f-1);)f--;return d.slice(u,f).split("\0").map(s).join(c)}(e,Q({delimiter:"."},t))}function ne(e,t){return void 0===t&&(t={}),te(e,Q({delimiter:"-"},t))}"stream"in Blob.prototype||Object.defineProperty(Blob.prototype,"stream",{value(){return new Response(this).body}}),"setBigUint64"in DataView.prototype||Object.defineProperty(DataView.prototype,"setBigUint64",{value(e,t,n){const i=Number(0xffffffffn&t),r=Number(t>>32n);this.setUint32(e+(n?0:4),i,n),this.setUint32(e+(n?4:0),r,n)}});var ie=e=>new DataView(new ArrayBuffer(e)),re=e=>new Uint8Array(e.buffer||e),oe=e=>(new TextEncoder).encode(String(e)),ae=e=>Math.min(4294967295,Number(e)),se=e=>Math.min(65535,Number(e));function le(e,t){if(void 0===t||t instanceof Date||(t=new Date(t)),e instanceof File)return{isFile:1,t:t||new Date(e.lastModified),i:e.stream()};if(e instanceof Response)return{isFile:1,t:t||new Date(e.headers.get("Last-Modified")||Date.now()),i:e.body};if(void 0===t)t=new Date;else if(isNaN(t))throw new Error("Invalid modification date.");if(void 0===e)return{isFile:0,t};if("string"==typeof e)return{isFile:1,t,i:oe(e)};if(e instanceof Blob)return{isFile:1,t,i:e.stream()};if(e instanceof Uint8Array||e instanceof ReadableStream)return{isFile:1,t,i:e};if(e instanceof ArrayBuffer||ArrayBuffer.isView(e))return{isFile:1,t,i:re(e)};if(Symbol.asyncIterator in e)return{isFile:1,t,i:ce(e[Symbol.asyncIterator]())};throw new TypeError("Unsupported input format.")}function ce(e,t=e){return new ReadableStream({async pull(t){let n=0;for(;t.desiredSize>n;){const i=await e.next();if(!i.value){t.close();break}{const e=de(i.value);t.enqueue(e),n+=e.byteLength}}},cancel(e){t.throw?.(e)}})}function de(e){return"string"==typeof e?oe(e):e instanceof Uint8Array?e:re(e)}function ue(e,t,n){let[i,r]=function(e){return e?e instanceof Uint8Array?[e,1]:ArrayBuffer.isView(e)||e instanceof ArrayBuffer?[re(e),1]:[oe(e),0]:[void 0,0]}(t);if(e instanceof File)return{o:pe(i||oe(e.name)),u:BigInt(e.size),l:r};if(e instanceof Response){const t=e.headers.get("content-disposition"),o=t&&t.match(/;\s*filename\*?\s*=\s*(?:UTF-\d+''|)["']?([^;"'\r\n]*)["']?(?:;|$)/i),a=o&&o[1]||e.url&&new URL(e.url).pathname.split("/").findLast(Boolean),s=a&&decodeURIComponent(a),l=n||+e.headers.get("content-length");return{o:pe(i||oe(s)),u:BigInt(l),l:r}}return i=pe(i,void 0!==e||void 0!==n),"string"==typeof e?{o:i,u:BigInt(oe(e).length),l:r}:e instanceof Blob?{o:i,u:BigInt(e.size),l:r}:e instanceof ArrayBuffer||ArrayBuffer.isView(e)?{o:i,u:BigInt(e.byteLength),l:r}:{o:i,u:fe(e,n),l:r}}function fe(e,t){return t>-1?BigInt(t):e?void 0:0n}function pe(e,t=1){if(!e||e.every((e=>47===e)))throw new Error("The file must have a name.");if(t)for(;47===e[e.length-1];)e=e.subarray(0,-1);else 47!==e[e.length-1]&&(e=new Uint8Array([...e,47]));return e}var me=new Uint32Array(256);for(let e=0;e<256;++e){let t=e;for(let e=0;e<8;++e)t=t>>>1^(1&t&&3988292384);me[e]=t}function ge(e,t=0){t^=-1;for(var n=0,i=e.length;n<i;n++)t=t>>>8^me[255&t^e[n]];return(-1^t)>>>0}function he(e,t,n=0){const i=e.getSeconds()>>1|e.getMinutes()<<5|e.getHours()<<11,r=e.getDate()|e.getMonth()+1<<5|e.getFullYear()-1980<<9;t.setUint16(n,i,1),t.setUint16(n+2,r,1)}function we({o:e,l:t},n){return 8*(!t||(n??function(e){try{ye.decode(e)}catch{return 0}return 1}(e)))}var ye=new TextDecoder("utf8",{fatal:1});function _e(e,t=0){const n=ie(30);return n.setUint32(0,1347093252),n.setUint32(4,754976768|t),he(e.t,n,10),n.setUint16(26,e.o.length,1),re(n)}async function*be(e){let{i:t}=e;if("then"in t&&(t=await t),t instanceof Uint8Array)yield t,e.m=ge(t,0),e.u=BigInt(t.length);else{e.u=0n;const n=t.getReader();for(;;){const{value:t,done:i}=await n.read();if(i)break;e.m=ge(t,e.m),e.u+=BigInt(t.length),yield t}}}function ve(e,t){const n=ie(16+(t?8:0));return n.setUint32(0,1347094280),n.setUint32(4,e.isFile?e.m:0,1),t?(n.setBigUint64(8,e.u,1),n.setBigUint64(16,e.u,1)):(n.setUint32(8,ae(e.u),1),n.setUint32(12,ae(e.u),1)),re(n)}function xe(e,t,n=0,i=0){const r=ie(46);return r.setUint32(0,1347092738),r.setUint32(4,755182848),r.setUint16(8,2048|n),he(e.t,r,12),r.setUint32(16,e.isFile?e.m:0,1),r.setUint32(20,ae(e.u),1),r.setUint32(24,ae(e.u),1),r.setUint16(28,e.o.length,1),r.setUint16(30,i,1),r.setUint16(40,e.isFile?33204:16893,1),r.setUint32(42,ae(t),1),re(r)}function je(e,t,n){const i=ie(n);return i.setUint16(0,1,1),i.setUint16(2,n-4,1),16&n&&(i.setBigUint64(4,e.u,1),i.setBigUint64(12,e.u,1)),i.setBigUint64(n-8,t,1),re(i)}function Se(e){return e instanceof File||e instanceof Response?[[e],[e]]:[[e.input,e.name,e.size],[e.input,e.lastModified]]}function Ue(e,t={}){const n={"Content-Type":"application/zip","Content-Disposition":"attachment"};return("bigint"==typeof t.length||Number.isInteger(t.length))&&t.length>0&&(n["Content-Length"]=String(t.length)),t.metadata&&(n["Content-Length"]=String((e=>function(e){let t=BigInt(22),n=0n,i=0;for(const r of e){if(!r.o)throw new Error("Every file must have a non-empty name.");if(void 0===r.u)throw new Error(`Missing size for file "${(new TextDecoder).decode(r.o)}".`);const e=r.u>=0xffffffffn,o=n>=0xffffffffn;n+=BigInt(46+r.o.length+(e&&8))+r.u,t+=BigInt(r.o.length+46+(12*o|28*e)),i||(i=e)}return(i||n>=0xffffffffn)&&(t+=BigInt(76)),t+n}(function*(e){for(const t of e)yield ue(...Se(t)[0])}(e)))(t.metadata))),new Response(Be(e,t),{headers:n})}function Be(e,t={}){const n=function(e){const t=e[Symbol.iterator in e?Symbol.iterator:Symbol.asyncIterator]();return{async next(){const e=await t.next();if(e.done)return e;const[n,i]=Se(e.value);return{done:0,value:Object.assign(le(...i),ue(...n))}},throw:t.throw?.bind(t),[Symbol.asyncIterator](){return this}}}(e);return ce(async function*(e,t){const n=[];let i=0n,r=0n,o=0;for await(const a of e){const e=we(a,t.buffersAreUTF8);yield _e(a,e),yield new Uint8Array(a.o),a.isFile&&(yield*be(a));const s=a.u>=0xffffffffn,l=12*(i>=0xffffffffn)|28*s;yield ve(a,s),n.push(xe(a,i,e,l)),n.push(a.o),l&&n.push(je(a,i,l)),s&&(i+=8n),r++,i+=BigInt(46+a.o.length)+a.u,o||(o=s)}let a=0n;for(const e of n)yield e,a+=BigInt(e.length);if(o||i>=0xffffffffn){const e=ie(76);e.setUint32(0,1347094022),e.setBigUint64(4,BigInt(44),1),e.setUint32(12,755182848),e.setBigUint64(24,r,1),e.setBigUint64(32,r,1),e.setBigUint64(40,a,1),e.setBigUint64(48,i,1),e.setUint32(56,1347094023),e.setBigUint64(64,i+a,1),e.setUint32(72,1,1),yield re(e)}const s=ie(22);s.setUint32(0,1347093766),s.setUint16(8,se(r),1),s.setUint16(10,se(r),1),s.setUint32(12,ae(a),1),s.setUint32(16,ae(i),1),yield re(s)}(n,t),n)}const Ce=window.wp.blob,Ve=(0,d.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,d.jsx)(c.Path,{d:"M18 11.3l-1-1.1-4 4V3h-1.5v11.3L7 10.2l-1 1.1 6.2 5.8 5.8-5.8zm.5 3.7v3.5h-13V15H4v5h16v-5h-1.5z"})});function ke(e){return JSON.stringify({__file:e.type,title:a(e),content:"string"==typeof e.content?e.content:e.content?.raw,syncStatus:e.wp_pattern_sync_status},null,2)}const Ee={id:"export-pattern",label:(0,n.__)("Export as JSON"),icon:Ve,supportsBulk:!0,isEligible:e=>"wp_block"===e.type,callback:async e=>{if(1===e.length)return(0,Ce.downloadBlob)(`${ne(a(e[0])||e[0].slug)}.json`,ke(e[0]),"application/json");const t={},i=e.map((e=>{const n=ne(a(e)||e.slug);return t[n]=(t[n]||0)+1,{name:n+(t[n]>1?"-"+(t[n]-1):"")+".json",lastModified:new Date,input:ke(e)}}));return(0,Ce.downloadBlob)((0,n.__)("patterns-export")+".zip",await Ue(i).blob(),"application/zip")}},De=undefined;(window.wp=window.wp||{}).fields=t})();