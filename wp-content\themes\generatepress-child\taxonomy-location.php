<?php
/**
 * Location Taxonomy Template (City Pages)
 */

get_header();

$current_term = get_queried_object();
$parent_term = null;

// Get parent term (state) if this is a city
if ($current_term->parent != 0) {
    $parent_term = get_term($current_term->parent, 'location');
}
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        
        <!-- Page Header -->
        <div class="taxonomy-header">
            <div class="container">
                <div class="breadcrumbs">
                    <a href="<?php echo home_url(); ?>">Home</a>
                    <?php if ($parent_term) : ?>
                        <span class="separator">›</span>
                        <a href="<?php echo get_term_link($parent_term); ?>">
                            <?php echo esc_html($parent_term->name); ?>
                        </a>
                    <?php endif; ?>
                    <span class="separator">›</span>
                    <span class="current"><?php echo esc_html($current_term->name); ?></span>
                </div>
                
                <h1 class="page-title">
                    <?php if ($parent_term) : ?>
                        Petting Zoos in <?php echo esc_html($current_term->name); ?>, <?php echo esc_html($parent_term->name); ?>
                    <?php else : ?>
                        Petting Zoos in <?php echo esc_html($current_term->name); ?>
                    <?php endif; ?>
                </h1>
                
                <?php if ($current_term->description) : ?>
                    <div class="taxonomy-description">
                        <?php echo wpautop($current_term->description); ?>
                    </div>
                <?php else : ?>
                    <div class="taxonomy-description">
                        <p>Discover amazing petting zoos and animal farms in <?php echo esc_html($current_term->name); ?>. 
                        Perfect for family outings, birthday parties, and educational experiences with friendly animals.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="container">
            
            <!-- Filters Section -->
            <div class="zoo-filters-section">
                <h3>Filter Results</h3>
                <div class="zoo-filters">
                    <select name="animal_type" onchange="applyFilters()">
                        <option value="">All Animal Types</option>
                        <?php
                        $animals = get_terms(array(
                            'taxonomy' => 'animal_type',
                            'hide_empty' => true
                        ));
                        
                        $selected_animal = isset($_GET['animal_type']) ? $_GET['animal_type'] : '';
                        
                        foreach ($animals as $animal) {
                            $selected = ($selected_animal === $animal->slug) ? 'selected' : '';
                            echo '<option value="' . esc_attr($animal->slug) . '" ' . $selected . '>' . esc_html($animal->name) . '</option>';
                        }
                        ?>
                    </select>
                    
                    <select name="zoo_type" onchange="applyFilters()">
                        <option value="">All Zoo Types</option>
                        <?php
                        $zoo_types = get_terms(array(
                            'taxonomy' => 'zoo_type',
                            'hide_empty' => true
                        ));
                        
                        $selected_type = isset($_GET['zoo_type']) ? $_GET['zoo_type'] : '';
                        
                        foreach ($zoo_types as $type) {
                            $selected = ($selected_type === $type->slug) ? 'selected' : '';
                            echo '<option value="' . esc_attr($type->slug) . '" ' . $selected . '>' . esc_html($type->name) . '</option>';
                        }
                        ?>
                    </select>
                    
                    <select name="features" onchange="applyFilters()">
                        <option value="">All Features</option>
                        <?php
                        $features = get_terms(array(
                            'taxonomy' => 'features',
                            'hide_empty' => true
                        ));
                        
                        $selected_feature = isset($_GET['features']) ? $_GET['features'] : '';
                        
                        foreach ($features as $feature) {
                            $selected = ($selected_feature === $feature->slug) ? 'selected' : '';
                            echo '<option value="' . esc_attr($feature->slug) . '" ' . $selected . '>' . esc_html($feature->name) . '</option>';
                        }
                        ?>
                    </select>
                </div>
            </div>

            <!-- Results Count -->
            <?php
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
            
            // Build query args
            $args = array(
                'post_type' => 'petting_zoo',
                'posts_per_page' => 12,
                'paged' => $paged,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'location',
                        'field' => 'term_id',
                        'terms' => $current_term->term_id,
                    )
                )
            );
            
            // Add additional filters
            $tax_query = array('relation' => 'AND');
            $tax_query[] = array(
                'taxonomy' => 'location',
                'field' => 'term_id',
                'terms' => $current_term->term_id,
            );
            
            if (isset($_GET['animal_type']) && !empty($_GET['animal_type'])) {
                $tax_query[] = array(
                    'taxonomy' => 'animal_type',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['animal_type']),
                );
            }
            
            if (isset($_GET['zoo_type']) && !empty($_GET['zoo_type'])) {
                $tax_query[] = array(
                    'taxonomy' => 'zoo_type',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['zoo_type']),
                );
            }
            
            if (isset($_GET['features']) && !empty($_GET['features'])) {
                $tax_query[] = array(
                    'taxonomy' => 'features',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['features']),
                );
            }
            
            $args['tax_query'] = $tax_query;
            
            $zoo_query = new WP_Query($args);
            ?>
            
            <div class="results-info">
                <p>Found <?php echo $zoo_query->found_posts; ?> petting zoo<?php echo $zoo_query->found_posts !== 1 ? 's' : ''; ?> 
                in <?php echo esc_html($current_term->name); ?></p>
            </div>

            <!-- Petting Zoos Grid -->
            <?php if ($zoo_query->have_posts()) : ?>
                <div class="zoo-grid">
                    <?php while ($zoo_query->have_posts()) : $zoo_query->the_post(); ?>
                        <div class="zoo-card">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium'); ?>
                                </a>
                            <?php endif; ?>
                            
                            <div class="zoo-card-content">
                                <h3><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                
                                <?php
                                $address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                                if ($address) : ?>
                                    <div class="location"><?php echo esc_html($address); ?></div>
                                <?php endif; ?>
                                
                                <div class="excerpt">
                                    <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                </div>
                                
                                <!-- Features Tags -->
                                <?php
                                $zoo_features = get_the_terms(get_the_ID(), 'features');
                                if ($zoo_features && !is_wp_error($zoo_features)) : ?>
                                    <div class="features">
                                        <?php 
                                        $feature_count = 0;
                                        foreach ($zoo_features as $feature) : 
                                            if ($feature_count >= 3) break;
                                            ?>
                                            <span class="feature-tag"><?php echo esc_html($feature->name); ?></span>
                                            <?php 
                                            $feature_count++;
                                        endforeach; 
                                        
                                        if (count($zoo_features) > 3) : ?>
                                            <span class="feature-tag">+<?php echo count($zoo_features) - 3; ?> more</span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <a href="<?php the_permalink(); ?>" class="btn">Learn More</a>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <?php
                $pagination = paginate_links(array(
                    'total' => $zoo_query->max_num_pages,
                    'current' => $paged,
                    'format' => '?paged=%#%',
                    'prev_text' => '← Previous',
                    'next_text' => 'Next →',
                ));
                
                if ($pagination) : ?>
                    <div class="pagination-wrapper">
                        <?php echo $pagination; ?>
                    </div>
                <?php endif; ?>

            <?php else : ?>
                <div class="no-results">
                    <h3>No petting zoos found</h3>
                    <p>Sorry, we couldn't find any petting zoos matching your criteria in <?php echo esc_html($current_term->name); ?>.</p>
                    <a href="<?php echo home_url(); ?>" class="btn">Browse All Locations</a>
                </div>
            <?php endif; ?>
            
            <?php wp_reset_postdata(); ?>

            <!-- Child Locations (if this is a state page) -->
            <?php if ($current_term->parent == 0) : // This is a state page ?>
                <?php
                $child_locations = get_terms(array(
                    'taxonomy' => 'location',
                    'hide_empty' => true,
                    'parent' => $current_term->term_id,
                    'orderby' => 'count',
                    'order' => 'DESC'
                ));
                
                if (!empty($child_locations)) : ?>
                    <section class="child-locations section">
                        <h2>Cities in <?php echo esc_html($current_term->name); ?></h2>
                        <div class="cities-grid">
                            <?php foreach ($child_locations as $city) : ?>
                                <a href="<?php echo get_term_link($city); ?>" class="city-card">
                                    <h4><?php echo esc_html($city->name); ?></h4>
                                    <div class="zoo-count"><?php echo $city->count; ?> Petting Zoo<?php echo $city->count !== 1 ? 's' : ''; ?></div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
            <?php endif; ?>

        </div>
    </main>
</div>

<script>
function applyFilters() {
    const params = new URLSearchParams();
    
    document.querySelectorAll('.zoo-filters select').forEach(select => {
        if (select.value) {
            params.set(select.name, select.value);
        }
    });
    
    const currentUrl = window.location.pathname;
    const queryString = params.toString();
    
    if (queryString) {
        window.location.href = currentUrl + '?' + queryString;
    } else {
        window.location.href = currentUrl;
    }
}
</script>

<?php get_footer(); ?>
