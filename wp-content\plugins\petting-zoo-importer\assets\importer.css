/**
 * Petting Zoo Importer Styles
 */

.importer-container {
    max-width: 800px;
    margin: 20px 0;
}

.file-selection {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.file-selection h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
}

.file-selection input[type="file"] {
    width: 100%;
    padding: 10px;
    border: 2px dashed #ccd0d4;
    border-radius: 4px;
    background: #f9f9f9;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.file-selection input[type="file"]:hover {
    border-color: #0073aa;
}

.file-selection .description {
    margin-top: 10px;
    color: #666;
    font-style: italic;
}

.import-actions {
    margin-bottom: 20px;
}

.import-actions .button {
    margin-right: 10px;
    padding: 8px 16px;
    font-size: 14px;
}

.import-actions .button .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

.import-actions .button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.import-progress {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    color: #333;
}

.import-log {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.import-log h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
}

#log-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
}

.log-entry {
    margin-bottom: 5px;
    padding: 3px 0;
}

.log-entry .timestamp {
    color: #666;
    font-weight: bold;
}

.log-info {
    color: #333;
}

.log-success {
    color: #28a745;
    font-weight: 600;
}

.log-warning {
    color: #ffc107;
    font-weight: 600;
}

.log-error {
    color: #dc3545;
    font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
    .importer-container {
        margin: 10px 0;
    }
    
    .file-selection,
    .import-progress,
    .import-log {
        padding: 15px;
    }
    
    .import-actions .button {
        display: block;
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0;
    }
    
    #log-content {
        max-height: 300px;
        font-size: 12px;
    }
}

/* Animation for progress bar */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

.progress-fill {
    animation: pulse 2s infinite;
}

/* Scrollbar styling for log content */
#log-content::-webkit-scrollbar {
    width: 8px;
}

#log-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#log-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#log-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* File input styling improvements */
.file-selection input[type="file"]::file-selector-button {
    background: #0073aa;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 10px;
}

.file-selection input[type="file"]::file-selector-button:hover {
    background: #005a87;
}

/* Success/Error message styling */
.notice-success,
.notice-error {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    border-radius: 4px;
}

.notice-success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.notice-error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

/* Button hover effects */
.import-actions .button:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.import-actions .button-primary:not(:disabled) {
    background: #0073aa;
    border-color: #0073aa;
}

.import-actions .button-primary:not(:disabled):hover {
    background: #005a87;
    border-color: #005a87;
}

.import-actions .button-secondary:not(:disabled) {
    background: #f7f7f7;
    border-color: #ccd0d4;
    color: #555;
}

.import-actions .button-secondary:not(:disabled):hover {
    background: #f1f1f1;
    border-color: #999;
}
