:root{
  --wp-admin-theme-color:#007cba;
  --wp-admin-theme-color--rgb:0, 124, 186;
  --wp-admin-theme-color-darker-10:#006ba1;
  --wp-admin-theme-color-darker-10--rgb:0, 107, 161;
  --wp-admin-theme-color-darker-20:#005a87;
  --wp-admin-theme-color-darker-20--rgb:0, 90, 135;
  --wp-admin-border-width-focus:2px;
  --wp-block-synced-color:#7a00df;
  --wp-block-synced-color--rgb:122, 0, 223;
  --wp-bound-block-color:var(--wp-block-synced-color);
}
@media (min-resolution:192dpi){
  :root{
    --wp-admin-border-width-focus:1.5px;
  }
}

.components-panel__header.interface-complementary-area-header__small{
  background:#fff;
  padding-right:4px;
}
.components-panel__header.interface-complementary-area-header__small .interface-complementary-area-header__small-title{
  margin:0;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:100%;
}
@media (min-width:782px){
  .components-panel__header.interface-complementary-area-header__small{
    display:none;
  }
}

.interface-complementary-area-header{
  background:#fff;
  gap:8px;
  padding-right:12px;
}
.interface-complementary-area-header .interface-complementary-area-header__title{
  margin:0;
}
.interface-complementary-area-header .components-button.has-icon{
  display:none;
  margin-left:auto;
}
.interface-complementary-area-header .components-button.has-icon~.components-button{
  margin-left:0;
}
@media (min-width:782px){
  .interface-complementary-area-header .components-button.has-icon{
    display:flex;
  }
}

.interface-complementary-area{
  background:#fff;
  color:#1e1e1e;
  height:100%;
  overflow:auto;
}
@media (min-width:600px){
  .interface-complementary-area{
    -webkit-overflow-scrolling:touch;
  }
}
@media (min-width:782px){
  .interface-complementary-area{
    width:280px;
  }
}
.interface-complementary-area .components-panel{
  border:none;
  position:relative;
  z-index:0;
}
.interface-complementary-area .components-panel__header{
  position:sticky;
  top:0;
  z-index:1;
}
.interface-complementary-area .components-panel__header.editor-sidebar__panel-tabs{
  top:48px;
}
@media (min-width:782px){
  .interface-complementary-area .components-panel__header.editor-sidebar__panel-tabs{
    top:0;
  }
}
.interface-complementary-area p:not(.components-base-control__help,.components-form-token-field__help){
  margin-top:0;
}
.interface-complementary-area h2{
  color:#1e1e1e;
  font-size:13px;
  font-weight:500;
  margin-bottom:1.5em;
}
.interface-complementary-area h3{
  color:#1e1e1e;
  font-size:11px;
  font-weight:500;
  margin-bottom:1.5em;
  text-transform:uppercase;
}
.interface-complementary-area hr{
  border-bottom:1px solid #f0f0f0;
  border-top:none;
  margin:1.5em 0;
}
.interface-complementary-area div.components-toolbar,.interface-complementary-area div.components-toolbar-group{
  box-shadow:none;
  margin-bottom:1.5em;
}
.interface-complementary-area div.components-toolbar-group:last-child,.interface-complementary-area div.components-toolbar:last-child{
  margin-bottom:0;
}
.interface-complementary-area .block-editor-skip-to-selected-block:focus{
  bottom:10px;
  left:auto;
  right:10px;
  top:auto;
}

.interface-complementary-area__fill{
  height:100%;
}

@media (min-width:782px){
  body.js.is-fullscreen-mode{
    height:calc(100% + 32px);
    margin-top:-32px;
  }
  body.js.is-fullscreen-mode #adminmenumain,body.js.is-fullscreen-mode #wpadminbar{
    display:none;
  }
  body.js.is-fullscreen-mode #wpcontent,body.js.is-fullscreen-mode #wpfooter{
    margin-left:0;
  }
}

html.interface-interface-skeleton__html-container{
  position:fixed;
  width:100%;
}
@media (min-width:782px){
  html.interface-interface-skeleton__html-container:not(:has(.is-zoom-out)){
    position:static;
    width:auto;
  }
}

.interface-interface-skeleton{
  bottom:0;
  display:flex;
  flex-direction:row;
  height:auto;
  max-height:100%;
  position:fixed;
  right:0;
  top:46px;
}
@media (min-width:783px){
  .interface-interface-skeleton{
    top:32px;
  }
  .is-fullscreen-mode .interface-interface-skeleton{
    top:0;
  }
}

.interface-interface-skeleton__editor{
  display:flex;
  flex:0 1 100%;
  flex-direction:column;
  overflow:hidden;
}

.interface-interface-skeleton{
  left:0;
}
@media (min-width:783px){
  .interface-interface-skeleton{
    left:160px;
  }
}
@media (min-width:783px){
  .auto-fold .interface-interface-skeleton{
    left:36px;
  }
}
@media (min-width:961px){
  .auto-fold .interface-interface-skeleton{
    left:160px;
  }
}
.folded .interface-interface-skeleton{
  left:0;
}
@media (min-width:783px){
  .folded .interface-interface-skeleton{
    left:36px;
  }
}

body.is-fullscreen-mode .interface-interface-skeleton{
  left:0 !important;
}

.interface-interface-skeleton__body{
  display:flex;
  flex-grow:1;
  overflow:auto;
  overscroll-behavior-y:none;
  position:relative;
}
@media (min-width:782px){
  .has-footer .interface-interface-skeleton__body{
    padding-bottom:25px;
  }
}

.interface-interface-skeleton__content{
  display:flex;
  flex-direction:column;
  flex-grow:1;
  overflow:auto;
  z-index:20;
}
@media (min-width:782px){
  .interface-interface-skeleton__content{
    z-index:auto;
  }
}

.interface-interface-skeleton__secondary-sidebar,.interface-interface-skeleton__sidebar{
  background:#fff;
  bottom:0;
  color:#1e1e1e;
  flex-shrink:0;
  left:0;
  position:absolute;
  top:0;
  width:auto;
  z-index:100000;
}
@media (min-width:782px){
  .interface-interface-skeleton__secondary-sidebar,.interface-interface-skeleton__sidebar{
    position:relative !important;
  }
}

.interface-interface-skeleton__sidebar{
  border-top:1px solid #e0e0e0;
  overflow:hidden;
}
@media (min-width:782px){
  .interface-interface-skeleton__sidebar{
    box-shadow:-1px 0 0 0 rgba(0,0,0,.133);
    outline:1px solid #0000;
  }
}

.interface-interface-skeleton__secondary-sidebar{
  border-top:1px solid #e0e0e0;
  right:0;
}
@media (min-width:782px){
  .interface-interface-skeleton__secondary-sidebar{
    box-shadow:1px 0 0 0 rgba(0,0,0,.133);
    outline:1px solid #0000;
  }
}

.interface-interface-skeleton__header{
  box-shadow:0 1px 0 0 rgba(0,0,0,.133);
  color:#1e1e1e;
  flex-shrink:0;
  height:auto;
  outline:1px solid #0000;
  z-index:30;
}

.interface-interface-skeleton__footer{
  background-color:#fff;
  border-top:1px solid #e0e0e0;
  bottom:0;
  color:#1e1e1e;
  display:none;
  flex-shrink:0;
  height:auto;
  left:0;
  position:absolute;
  width:100%;
  z-index:90;
}
@media (min-width:782px){
  .interface-interface-skeleton__footer{
    display:flex;
  }
}
.interface-interface-skeleton__footer .block-editor-block-breadcrumb{
  align-items:center;
  background:#fff;
  display:flex;
  font-size:13px;
  height:24px;
  padding:0 18px;
  z-index:30;
}

.interface-interface-skeleton__actions{
  background:#fff;
  bottom:auto;
  color:#1e1e1e;
  left:auto;
  position:fixed !important;
  right:0;
  top:-9999em;
  width:100vw;
  z-index:100000;
}
@media (min-width:782px){
  .interface-interface-skeleton__actions{
    width:280px;
  }
}
.interface-interface-skeleton__actions:focus,.interface-interface-skeleton__actions:focus-within{
  bottom:0;
  top:auto;
}
.is-entity-save-view-open .interface-interface-skeleton__actions:focus,.is-entity-save-view-open .interface-interface-skeleton__actions:focus-within{
  top:46px;
}
@media (min-width:782px){
  .is-entity-save-view-open .interface-interface-skeleton__actions:focus,.is-entity-save-view-open .interface-interface-skeleton__actions:focus-within{
    border-left:1px solid #ddd;
    top:32px;
  }
  .is-fullscreen-mode .is-entity-save-view-open .interface-interface-skeleton__actions:focus,.is-fullscreen-mode .is-entity-save-view-open .interface-interface-skeleton__actions:focus-within{
    top:0;
  }
}

.interface-pinned-items{
  display:flex;
  gap:8px;
}
.interface-pinned-items .components-button{
  display:none;
  margin:0;
}
.interface-pinned-items .components-button[aria-controls="edit-post:block"],.interface-pinned-items .components-button[aria-controls="edit-post:document"],.interface-pinned-items .components-button[aria-controls="edit-site:block-inspector"],.interface-pinned-items .components-button[aria-controls="edit-site:template"]{
  display:flex;
}
.interface-pinned-items .components-button svg{
  max-height:24px;
  max-width:24px;
}
@media (min-width:600px){
  .interface-pinned-items .components-button{
    display:flex;
  }
}

.wp-block[data-type="core/widget-area"]{
  margin-left:auto;
  margin-right:auto;
  max-width:700px;
}
.wp-block[data-type="core/widget-area"] .components-panel__body>.components-panel__body-title{
  background:#fff;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  height:48px;
  margin:0;
  position:relative;
  transform:translateZ(0);
  z-index:1;
}
.wp-block[data-type="core/widget-area"] .components-panel__body>.components-panel__body-title:hover{
  background:#fff;
}
.wp-block[data-type="core/widget-area"] .block-list-appender.wp-block{
  position:relative;
  width:auto;
}
.wp-block[data-type="core/widget-area"] .editor-styles-wrapper .wp-block.wp-block.wp-block.wp-block.wp-block{
  max-width:100%;
}
.wp-block[data-type="core/widget-area"] .components-panel__body.is-opened{
  padding:0;
}

.blocks-widgets-container .wp-block-widget-area__inner-blocks.editor-styles-wrapper{
  margin:0;
  padding:0;
}
.blocks-widgets-container .wp-block-widget-area__inner-blocks.editor-styles-wrapper>.block-editor-block-list__layout{
  margin-top:-48px;
  min-height:32px;
  padding:72px 16px 16px;
}

.wp-block-widget-area__highlight-drop-zone{
  outline:var(--wp-admin-border-width-focus) solid var(--wp-admin-theme-color);
}

body.is-dragging-components-draggable .wp-block[data-type="core/widget-area"] .components-panel__body>.components-panel__body-title,body.is-dragging-components-draggable .wp-block[data-type="core/widget-area"] .components-panel__body>.components-panel__body-title *{
  pointer-events:none;
}

.edit-widgets-error-boundary{
  box-shadow:0 5px 15px #00000014,0 15px 27px #00000012,0 30px 36px #0000000a,0 50px 43px #00000005;
  margin:60px auto auto;
  max-width:780px;
  padding:20px;
}

.edit-widgets-header{
  align-items:center;
  background:#fff;
  display:flex;
  height:60px;
  justify-content:space-between;
  overflow:auto;
}
@media (min-width:600px){
  .edit-widgets-header{
    overflow:visible;
  }
}
.edit-widgets-header .selected-block-tools-wrapper{
  align-items:center;
  display:flex;
  height:60px;
  overflow:hidden;
}
.edit-widgets-header .selected-block-tools-wrapper .block-editor-block-contextual-toolbar{
  border-bottom:0;
  height:100%;
}
.edit-widgets-header .selected-block-tools-wrapper .block-editor-block-toolbar{
  height:100%;
  padding-top:15px;
}
.edit-widgets-header .selected-block-tools-wrapper .block-editor-block-toolbar .components-button:not(.block-editor-block-mover-button){
  height:32px;
}
.edit-widgets-header .selected-block-tools-wrapper .components-toolbar,.edit-widgets-header .selected-block-tools-wrapper .components-toolbar-group{
  border-right:none;
}
.edit-widgets-header .selected-block-tools-wrapper .components-toolbar-group:after,.edit-widgets-header .selected-block-tools-wrapper .components-toolbar:after{
  background-color:#ddd;
  content:"";
  height:24px;
  margin-left:8px;
  margin-top:4px;
  width:1px;
}
.edit-widgets-header .selected-block-tools-wrapper .components-toolbar .components-toolbar-group.components-toolbar-group:after,.edit-widgets-header .selected-block-tools-wrapper .components-toolbar-group .components-toolbar-group.components-toolbar-group:after{
  display:none;
}
.edit-widgets-header .selected-block-tools-wrapper .block-editor-block-mover.is-horizontal .block-editor-block-mover-button{
  height:32px;
  overflow:visible;
}
@media (min-width:600px){
  .edit-widgets-header .selected-block-tools-wrapper .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container{
    position:relative;
    top:-10px;
  }
}

.edit-widgets-header__navigable-toolbar-wrapper{
  align-items:center;
  display:flex;
  flex-shrink:2;
  justify-content:center;
  overflow:hidden;
  padding-left:16px;
  padding-right:8px;
}

.edit-widgets-header__title{
  font-size:20px;
  margin:0 20px 0 0;
  padding:0;
}

.edit-widgets-header__actions{
  align-items:center;
  display:flex;
  gap:4px;
  padding-right:16px;
}
@media (min-width:600px){
  .edit-widgets-header__actions{
    gap:8px;
  }
}

.edit-widgets-header-toolbar{
  gap:8px;
  margin-right:8px;
}
.edit-widgets-header-toolbar>.components-button.has-icon.has-icon.has-icon,.edit-widgets-header-toolbar>.components-dropdown>.components-button.has-icon.has-icon{
  height:32px;
  min-width:32px;
  padding:4px;
}
.edit-widgets-header-toolbar>.components-button.has-icon.has-icon.has-icon.is-pressed,.edit-widgets-header-toolbar>.components-dropdown>.components-button.has-icon.has-icon.is-pressed{
  background:#1e1e1e;
}
.edit-widgets-header-toolbar>.components-button.has-icon.has-icon.has-icon:focus:not(:disabled),.edit-widgets-header-toolbar>.components-dropdown>.components-button.has-icon.has-icon:focus:not(:disabled){
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color), inset 0 0 0 1px #fff;
  outline:1px solid #0000;
}
.edit-widgets-header-toolbar>.components-button.has-icon.has-icon.has-icon:before,.edit-widgets-header-toolbar>.components-dropdown>.components-button.has-icon.has-icon:before{
  display:none;
}

.edit-widgets-header-toolbar__inserter-toggle.edit-widgets-header-toolbar__inserter-toggle{
  padding-left:8px;
  padding-right:8px;
}
@media (min-width:600px){
  .edit-widgets-header-toolbar__inserter-toggle.edit-widgets-header-toolbar__inserter-toggle{
    padding-left:12px;
    padding-right:12px;
  }
}
.edit-widgets-header-toolbar__inserter-toggle.edit-widgets-header-toolbar__inserter-toggle:after{
  content:none;
}
.edit-widgets-header-toolbar__inserter-toggle.edit-widgets-header-toolbar__inserter-toggle svg{
  transition:transform .2s cubic-bezier(.165, .84, .44, 1);
}
@media (prefers-reduced-motion:reduce){
  .edit-widgets-header-toolbar__inserter-toggle.edit-widgets-header-toolbar__inserter-toggle svg{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.edit-widgets-header-toolbar__inserter-toggle.edit-widgets-header-toolbar__inserter-toggle.is-pressed svg{
  transform:rotate(45deg);
}

.edit-widgets-keyboard-shortcut-help-modal__section{
  margin:0 0 2rem;
}
.edit-widgets-keyboard-shortcut-help-modal__section-title{
  font-size:.9rem;
  font-weight:600;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut{
  align-items:baseline;
  border-top:1px solid #ddd;
  display:flex;
  margin-bottom:0;
  padding:.6rem 0;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut:last-child{
  border-bottom:1px solid #ddd;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut:empty{
  display:none;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut-term{
  font-weight:600;
  margin:0 0 0 1rem;
  text-align:right;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut-description{
  flex:1;
  margin:0;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut-key-combination{
  background:none;
  display:block;
  margin:0;
  padding:0;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut-key-combination+.edit-widgets-keyboard-shortcut-help-modal__shortcut-key-combination{
  margin-top:10px;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut-key{
  border-radius:8%;
  margin:0 .2rem;
  padding:.25rem .5rem;
}
.edit-widgets-keyboard-shortcut-help-modal__shortcut-key:last-child{
  margin:0 0 0 .2rem;
}

.components-panel__header.edit-widgets-sidebar__panel-tabs{
  padding-left:0;
}

.edit-widgets-widget-areas__top-container{
  display:flex;
  padding:16px;
}
.edit-widgets-widget-areas__top-container .block-editor-block-icon{
  margin-right:16px;
}

.edit-widgets-notices__snackbar{
  bottom:20px;
  left:0;
  padding-left:16px;
  padding-right:16px;
  position:fixed;
  right:0;
}
@media (min-width:783px){
  .edit-widgets-notices__snackbar{
    left:160px;
  }
}
@media (min-width:783px){
  .auto-fold .edit-widgets-notices__snackbar{
    left:36px;
  }
}
@media (min-width:961px){
  .auto-fold .edit-widgets-notices__snackbar{
    left:160px;
  }
}
.folded .edit-widgets-notices__snackbar{
  left:0;
}
@media (min-width:783px){
  .folded .edit-widgets-notices__snackbar{
    left:36px;
  }
}

body.is-fullscreen-mode .edit-widgets-notices__snackbar{
  left:0 !important;
}

.edit-widgets-notices__dismissible .components-notice,.edit-widgets-notices__pinned .components-notice{
  border-bottom:1px solid #0003;
  box-sizing:border-box;
  min-height:60px;
  padding:0 12px;
}
.edit-widgets-notices__dismissible .components-notice .components-notice__dismiss,.edit-widgets-notices__pinned .components-notice .components-notice__dismiss{
  margin-top:12px;
}

.edit-widgets-layout__inserter-panel{
  display:flex;
  flex-direction:column;
  height:100%;
}
.edit-widgets-layout__inserter-panel .block-editor-inserter__menu{
  overflow:hidden;
}

.edit-widgets-layout__inserter-panel-header{
  display:flex;
  justify-content:flex-end;
  padding-right:8px;
  padding-top:8px;
}

.edit-widgets-layout__inserter-panel-content{
  height:calc(100% - 44px);
}
.edit-widgets-layout__inserter-panel-content .block-editor-inserter__tablist-and-close{
  display:none;
}
@media (min-width:782px){
  .edit-widgets-layout__inserter-panel-content{
    height:100%;
  }
}

.components-popover.more-menu-dropdown__content{
  z-index:99998;
}

.edit-widgets-welcome-guide{
  width:312px;
}
.edit-widgets-welcome-guide__image{
  background:#00a0d2;
  margin:0 0 16px;
}
.edit-widgets-welcome-guide__image>img{
  display:block;
  max-width:100%;
  object-fit:cover;
}
.edit-widgets-welcome-guide__heading{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:24px;
  line-height:1.4;
  margin:16px 0;
  padding:0 32px;
}
.edit-widgets-welcome-guide__text{
  font-size:13px;
  line-height:1.4;
  margin:0 0 24px;
  padding:0 32px;
}
.edit-widgets-welcome-guide__inserter-icon{
  margin:0 4px;
  vertical-align:text-top;
}

.edit-widgets-block-editor{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  position:relative;
}
.edit-widgets-block-editor,.edit-widgets-block-editor .block-editor-writing-flow,.edit-widgets-block-editor>div:last-of-type{
  display:flex;
  flex-direction:column;
  flex-grow:1;
}
.edit-widgets-block-editor .edit-widgets-main-block-list{
  height:100%;
}
.edit-widgets-block-editor .components-button{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
}
.edit-widgets-block-editor .components-button.has-icon,.edit-widgets-block-editor .components-button.is-tertiary{
  padding:6px;
}

.edit-widgets-editor__list-view-panel{
  display:flex;
  flex-direction:column;
  height:100%;
  min-width:350px;
}

.edit-widgets-editor__list-view-panel-content{
  height:calc(100% - 44px);
  overflow-y:auto;
  padding:8px;
}

.edit-widgets-editor__list-view-panel-header{
  align-items:center;
  border-bottom:1px solid #ddd;
  display:flex;
  height:48px;
  justify-content:space-between;
  padding-left:16px;
  padding-right:4px;
}

body.js.appearance_page_gutenberg-widgets,body.js.widgets-php{
  background:#fff;
}
body.js.appearance_page_gutenberg-widgets #wpcontent,body.js.widgets-php #wpcontent{
  padding-left:0;
}
body.js.appearance_page_gutenberg-widgets #wpbody-content,body.js.widgets-php #wpbody-content{
  padding-bottom:0;
}
body.js.appearance_page_gutenberg-widgets #wpbody-content>div:not(.blocks-widgets-container):not(#screen-meta),body.js.appearance_page_gutenberg-widgets #wpfooter,body.js.widgets-php #wpbody-content>div:not(.blocks-widgets-container):not(#screen-meta),body.js.widgets-php #wpfooter{
  display:none;
}
body.js.appearance_page_gutenberg-widgets .a11y-speak-region,body.js.widgets-php .a11y-speak-region{
  left:-1px;
  top:-1px;
}
body.js.appearance_page_gutenberg-widgets ul#adminmenu a.wp-has-current-submenu:after,body.js.appearance_page_gutenberg-widgets ul#adminmenu>li.current>a.current:after,body.js.widgets-php ul#adminmenu a.wp-has-current-submenu:after,body.js.widgets-php ul#adminmenu>li.current>a.current:after{
  border-right-color:#fff;
}
body.js.appearance_page_gutenberg-widgets .media-frame select.attachment-filters:last-of-type,body.js.widgets-php .media-frame select.attachment-filters:last-of-type{
  max-width:100%;
  width:auto;
}

.blocks-widgets-container{
  box-sizing:border-box;
}
.blocks-widgets-container *,.blocks-widgets-container :after,.blocks-widgets-container :before{
  box-sizing:inherit;
}
@media (min-width:600px){
  .blocks-widgets-container{
    bottom:0;
    left:0;
    min-height:calc(100vh - 46px);
    position:absolute;
    right:0;
    top:0;
  }
}
@media (min-width:782px){
  .blocks-widgets-container{
    min-height:calc(100vh - 32px);
  }
}
.blocks-widgets-container .interface-interface-skeleton__content{
  background-color:#f0f0f0;
}

.blocks-widgets-container .editor-styles-wrapper{
  margin:auto;
  max-width:700px;
}

.edit-widgets-sidebar .components-button.interface-complementary-area__pin-unpin-item{
  display:none;
}

.js .widgets-php .notice{
  display:none !important;
}

body.admin-color-light{
  --wp-admin-theme-color:#0085ba;
  --wp-admin-theme-color--rgb:0, 133, 186;
  --wp-admin-theme-color-darker-10:#0073a1;
  --wp-admin-theme-color-darker-10--rgb:0, 115, 161;
  --wp-admin-theme-color-darker-20:#006187;
  --wp-admin-theme-color-darker-20--rgb:0, 97, 135;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-light{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-modern{
  --wp-admin-theme-color:#3858e9;
  --wp-admin-theme-color--rgb:56, 88, 233;
  --wp-admin-theme-color-darker-10:#2145e6;
  --wp-admin-theme-color-darker-10--rgb:33, 69, 230;
  --wp-admin-theme-color-darker-20:#183ad6;
  --wp-admin-theme-color-darker-20--rgb:24, 58, 214;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-modern{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-blue{
  --wp-admin-theme-color:#096484;
  --wp-admin-theme-color--rgb:9, 100, 132;
  --wp-admin-theme-color-darker-10:#07526c;
  --wp-admin-theme-color-darker-10--rgb:7, 82, 108;
  --wp-admin-theme-color-darker-20:#064054;
  --wp-admin-theme-color-darker-20--rgb:6, 64, 84;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-blue{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-coffee{
  --wp-admin-theme-color:#46403c;
  --wp-admin-theme-color--rgb:70, 64, 60;
  --wp-admin-theme-color-darker-10:#383330;
  --wp-admin-theme-color-darker-10--rgb:56, 51, 48;
  --wp-admin-theme-color-darker-20:#2b2724;
  --wp-admin-theme-color-darker-20--rgb:43, 39, 36;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-coffee{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ectoplasm{
  --wp-admin-theme-color:#523f6d;
  --wp-admin-theme-color--rgb:82, 63, 109;
  --wp-admin-theme-color-darker-10:#46365d;
  --wp-admin-theme-color-darker-10--rgb:70, 54, 93;
  --wp-admin-theme-color-darker-20:#3a2c4d;
  --wp-admin-theme-color-darker-20--rgb:58, 44, 77;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ectoplasm{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-midnight{
  --wp-admin-theme-color:#e14d43;
  --wp-admin-theme-color--rgb:225, 77, 67;
  --wp-admin-theme-color-darker-10:#dd382d;
  --wp-admin-theme-color-darker-10--rgb:221, 56, 45;
  --wp-admin-theme-color-darker-20:#d02c21;
  --wp-admin-theme-color-darker-20--rgb:208, 44, 33;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-midnight{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ocean{
  --wp-admin-theme-color:#627c83;
  --wp-admin-theme-color--rgb:98, 124, 131;
  --wp-admin-theme-color-darker-10:#576e74;
  --wp-admin-theme-color-darker-10--rgb:87, 110, 116;
  --wp-admin-theme-color-darker-20:#4c6066;
  --wp-admin-theme-color-darker-20--rgb:76, 96, 102;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ocean{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-sunrise{
  --wp-admin-theme-color:#dd823b;
  --wp-admin-theme-color--rgb:221, 130, 59;
  --wp-admin-theme-color-darker-10:#d97426;
  --wp-admin-theme-color-darker-10--rgb:217, 116, 38;
  --wp-admin-theme-color-darker-20:#c36922;
  --wp-admin-theme-color-darker-20--rgb:195, 105, 34;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-sunrise{
    --wp-admin-border-width-focus:1.5px;
  }
}