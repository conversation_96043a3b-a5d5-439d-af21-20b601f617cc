/**
 * Petting Zoo Directory JavaScript
 */

jQuery(document).ready(function($) {
    
    // FAQ Toggle Functionality
    $('.faq-question').click(function() {
        var answer = $(this).next('.faq-answer');
        
        // Close all other FAQ answers
        $('.faq-answer').not(answer).removeClass('active').slideUp();
        
        // Toggle current FAQ answer
        answer.toggleClass('active').slideToggle();
    });
    
    // Zoo Finder Functionality
    $('#zoo-finder-form').on('submit', function(e) {
        e.preventDefault();
        
        var location = $('#finder-location').val();
        var animalType = $('#finder-animal').val();
        
        if (!location && !animalType) {
            alert('Please select a location or animal type to search.');
            return;
        }
        
        // Build search URL
        var searchUrl = window.location.origin;
        
        if (location) {
            searchUrl += '/location/' + location + '/';
        }
        
        if (animalType) {
            if (location) {
                searchUrl += '?animal=' + animalType;
            } else {
                searchUrl += '/animals/' + animalType + '/';
            }
        }
        
        window.location.href = searchUrl;
    });
    
    // Geolocation for "Near Me" functionality
    $('#find-near-me').click(function(e) {
        e.preventDefault();
        
        if (navigator.geolocation) {
            $(this).text('Finding your location...');
            
            navigator.geolocation.getCurrentPosition(function(position) {
                var lat = position.coords.latitude;
                var lng = position.coords.longitude;
                
                // AJAX call to find nearest zoos
                $.ajax({
                    url: pettingZooAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'find_nearest_zoos',
                        latitude: lat,
                        longitude: lng,
                        nonce: pettingZooAjax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            // Redirect to results page with coordinates
                            window.location.href = window.location.origin + '/nearest-zoos/?lat=' + lat + '&lng=' + lng;
                        } else {
                            alert('Sorry, we couldn\'t find any petting zoos near you.');
                        }
                        $('#find-near-me').text('Find Petting Zoos Near Me');
                    },
                    error: function() {
                        alert('There was an error finding your location. Please try again.');
                        $('#find-near-me').text('Find Petting Zoos Near Me');
                    }
                });
            }, function() {
                alert('Unable to retrieve your location. Please select a city manually.');
                $('#find-near-me').text('Find Petting Zoos Near Me');
            });
        } else {
            alert('Geolocation is not supported by this browser.');
        }
    });
    
    // Filter functionality for taxonomy pages
    $('.zoo-filters').on('change', 'select', function() {
        var filters = {};
        
        $('.zoo-filters select').each(function() {
            var filterName = $(this).attr('name');
            var filterValue = $(this).val();
            
            if (filterValue) {
                filters[filterName] = filterValue;
            }
        });
        
        // Build query string
        var queryString = $.param(filters);
        var currentUrl = window.location.pathname;
        
        if (queryString) {
            window.location.href = currentUrl + '?' + queryString;
        } else {
            window.location.href = currentUrl;
        }
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Load more functionality for infinite scroll (if needed)
    var page = 2;
    var loading = false;
    
    $('#load-more-zoos').click(function(e) {
        e.preventDefault();
        
        if (loading) return;
        
        loading = true;
        $(this).text('Loading...');
        
        $.ajax({
            url: pettingZooAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'load_more_zoos',
                page: page,
                nonce: pettingZooAjax.nonce,
                // Add any current filters
                filters: getCurrentFilters()
            },
            success: function(response) {
                if (response.success && response.data.html) {
                    $('.zoo-grid').append(response.data.html);
                    page++;
                    
                    if (!response.data.has_more) {
                        $('#load-more-zoos').hide();
                    } else {
                        $('#load-more-zoos').text('Load More Petting Zoos');
                    }
                } else {
                    $('#load-more-zoos').hide();
                }
                loading = false;
            },
            error: function() {
                $('#load-more-zoos').text('Load More Petting Zoos');
                loading = false;
            }
        });
    });
    
    // Helper function to get current filters
    function getCurrentFilters() {
        var filters = {};
        var urlParams = new URLSearchParams(window.location.search);
        
        urlParams.forEach(function(value, key) {
            filters[key] = value;
        });
        
        return filters;
    }
    
    // Initialize tooltips (if using Bootstrap or similar)
    if (typeof $().tooltip === 'function') {
        $('[data-toggle="tooltip"]').tooltip();
    }
    
    // Image lazy loading fallback
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // Mobile menu toggle (if needed)
    $('.mobile-menu-toggle').click(function() {
        $('.mobile-menu').toggleClass('active');
    });
    
    // Search autocomplete functionality
    $('#zoo-search-input').on('input', function() {
        var query = $(this).val();
        
        if (query.length >= 3) {
            $.ajax({
                url: pettingZooAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'zoo_search_autocomplete',
                    query: query,
                    nonce: pettingZooAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        displaySearchSuggestions(response.data);
                    }
                }
            });
        } else {
            hideSearchSuggestions();
        }
    });
    
    function displaySearchSuggestions(suggestions) {
        var suggestionsHtml = '';
        
        suggestions.forEach(function(suggestion) {
            suggestionsHtml += '<div class="search-suggestion" data-url="' + suggestion.url + '">' + suggestion.title + '</div>';
        });
        
        $('#search-suggestions').html(suggestionsHtml).show();
    }
    
    function hideSearchSuggestions() {
        $('#search-suggestions').hide();
    }
    
    // Handle search suggestion clicks
    $(document).on('click', '.search-suggestion', function() {
        window.location.href = $(this).data('url');
    });
    
    // Hide suggestions when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('#zoo-search-input, #search-suggestions').length) {
            hideSearchSuggestions();
        }
    });
    
});
