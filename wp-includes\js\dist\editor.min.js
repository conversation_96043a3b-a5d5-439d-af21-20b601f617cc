/*! This file is auto-generated */
(()=>{var e={4306:function(e,t){var s,o,n;
/*!
	autosize 4.0.4
	license: MIT
	http://www.jacklmoore.com/autosize
*/o=[e,t],s=function(e,t){"use strict";var s,o,n="function"==typeof Map?new Map:(s=[],o=[],{has:function(e){return s.indexOf(e)>-1},get:function(e){return o[s.indexOf(e)]},set:function(e,t){-1===s.indexOf(e)&&(s.push(e),o.push(t))},delete:function(e){var t=s.indexOf(e);t>-1&&(s.splice(t,1),o.splice(t,1))}}),i=function(e){return new Event(e,{bubbles:!0})};try{new Event("test")}catch(e){i=function(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!1),t}}function r(e){if(e&&e.nodeName&&"TEXTAREA"===e.nodeName&&!n.has(e)){var t=null,s=null,o=null,r=function(){e.clientWidth!==s&&p()},a=function(t){window.removeEventListener("resize",r,!1),e.removeEventListener("input",p,!1),e.removeEventListener("keyup",p,!1),e.removeEventListener("autosize:destroy",a,!1),e.removeEventListener("autosize:update",p,!1),Object.keys(t).forEach((function(s){e.style[s]=t[s]})),n.delete(e)}.bind(e,{height:e.style.height,resize:e.style.resize,overflowY:e.style.overflowY,overflowX:e.style.overflowX,wordWrap:e.style.wordWrap});e.addEventListener("autosize:destroy",a,!1),"onpropertychange"in e&&"oninput"in e&&e.addEventListener("keyup",p,!1),window.addEventListener("resize",r,!1),e.addEventListener("input",p,!1),e.addEventListener("autosize:update",p,!1),e.style.overflowX="hidden",e.style.wordWrap="break-word",n.set(e,{destroy:a,update:p}),l()}function l(){var s=window.getComputedStyle(e,null);"vertical"===s.resize?e.style.resize="none":"both"===s.resize&&(e.style.resize="horizontal"),t="content-box"===s.boxSizing?-(parseFloat(s.paddingTop)+parseFloat(s.paddingBottom)):parseFloat(s.borderTopWidth)+parseFloat(s.borderBottomWidth),isNaN(t)&&(t=0),p()}function c(t){var s=e.style.width;e.style.width="0px",e.offsetWidth,e.style.width=s,e.style.overflowY=t}function d(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push({node:e.parentNode,scrollTop:e.parentNode.scrollTop}),e=e.parentNode;return t}function u(){if(0!==e.scrollHeight){var o=d(e),n=document.documentElement&&document.documentElement.scrollTop;e.style.height="",e.style.height=e.scrollHeight+t+"px",s=e.clientWidth,o.forEach((function(e){e.node.scrollTop=e.scrollTop})),n&&(document.documentElement.scrollTop=n)}}function p(){u();var t=Math.round(parseFloat(e.style.height)),s=window.getComputedStyle(e,null),n="content-box"===s.boxSizing?Math.round(parseFloat(s.height)):e.offsetHeight;if(n<t?"hidden"===s.overflowY&&(c("scroll"),u(),n="content-box"===s.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight):"hidden"!==s.overflowY&&(c("hidden"),u(),n="content-box"===s.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight),o!==n){o=n;var r=i("autosize:resized");try{e.dispatchEvent(r)}catch(e){}}}}function a(e){var t=n.get(e);t&&t.destroy()}function l(e){var t=n.get(e);t&&t.update()}var c=null;"undefined"==typeof window||"function"!=typeof window.getComputedStyle?((c=function(e){return e}).destroy=function(e){return e},c.update=function(e){return e}):((c=function(e,t){return e&&Array.prototype.forEach.call(e.length?e:[e],(function(e){return r(e,t)})),e}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],a),e},c.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],l),e}),t.default=c,e.exports=t.default},void 0===(n="function"==typeof s?s.apply(t,o):s)||(e.exports=n)},6109:e=>{e.exports=function(e,t,s){return((s=window.getComputedStyle)?s(e):e.currentStyle)[t.replace(/-(\w)/gi,(function(e,t){return t.toUpperCase()}))]}},66:e=>{"use strict";var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===s}(e)}(e)};var s="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function o(e,t){return!1!==t.clone&&t.isMergeableObject(e)?l((s=e,Array.isArray(s)?[]:{}),e,t):e;var s}function n(e,t,s){return e.concat(t).map((function(e){return o(e,s)}))}function i(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function r(e,t){try{return t in e}catch(e){return!1}}function a(e,t,s){var n={};return s.isMergeableObject(e)&&i(e).forEach((function(t){n[t]=o(e[t],s)})),i(t).forEach((function(i){(function(e,t){return r(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,i)||(r(e,i)&&s.isMergeableObject(t[i])?n[i]=function(e,t){if(!t.customMerge)return l;var s=t.customMerge(e);return"function"==typeof s?s:l}(i,s)(e[i],t[i],s):n[i]=o(t[i],s))})),n}function l(e,s,i){(i=i||{}).arrayMerge=i.arrayMerge||n,i.isMergeableObject=i.isMergeableObject||t,i.cloneUnlessOtherwiseSpecified=o;var r=Array.isArray(s);return r===Array.isArray(e)?r?i.arrayMerge(e,s,i):a(e,s,i):o(s,i)}l.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,s){return l(e,s,t)}),{})};var c=l;e.exports=c},5215:e=>{"use strict";e.exports=function e(t,s){if(t===s)return!0;if(t&&s&&"object"==typeof t&&"object"==typeof s){if(t.constructor!==s.constructor)return!1;var o,n,i;if(Array.isArray(t)){if((o=t.length)!=s.length)return!1;for(n=o;0!=n--;)if(!e(t[n],s[n]))return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if((o=(i=Object.keys(t)).length)!==Object.keys(s).length)return!1;for(n=o;0!=n--;)if(!Object.prototype.hasOwnProperty.call(s,i[n]))return!1;for(n=o;0!=n--;){var r=i[n];if(!e(t[r],s[r]))return!1}return!0}return t!=t&&s!=s}},461:(e,t,s)=>{var o=s(6109);e.exports=function(e){var t=o(e,"line-height"),s=parseFloat(t,10);if(t===s+""){var n=e.style.lineHeight;e.style.lineHeight=t+"em",t=o(e,"line-height"),s=parseFloat(t,10),n?e.style.lineHeight=n:delete e.style.lineHeight}if(-1!==t.indexOf("pt")?(s*=4,s/=3):-1!==t.indexOf("mm")?(s*=96,s/=25.4):-1!==t.indexOf("cm")?(s*=96,s/=2.54):-1!==t.indexOf("in")?s*=96:-1!==t.indexOf("pc")&&(s*=16),s=Math.round(s),"normal"===t){var i=e.nodeName,r=document.createElement(i);r.innerHTML="&nbsp;","TEXTAREA"===i.toUpperCase()&&r.setAttribute("rows","1");var a=o(e,"font-size");r.style.fontSize=a,r.style.padding="0px",r.style.border="0px";var l=document.body;l.appendChild(r),s=r.offsetHeight,l.removeChild(r)}return s}},628:(e,t,s)=>{"use strict";var o=s(4067);function n(){}function i(){}i.resetWarningCache=n,e.exports=function(){function e(e,t,s,n,i,r){if(r!==o){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var s={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:n};return s.PropTypes=s,s}},5826:(e,t,s)=>{e.exports=s(628)()},4067:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},4462:function(e,t,s){"use strict";var o,n=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var s in t)t.hasOwnProperty(s)&&(e[s]=t[s])},function(e,t){function s(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(s.prototype=t.prototype,new s)}),i=this&&this.__assign||Object.assign||function(e){for(var t,s=1,o=arguments.length;s<o;s++)for(var n in t=arguments[s])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},r=this&&this.__rest||function(e,t){var s={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(s[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&(s[o[n]]=e[o[n]])}return s};t.__esModule=!0;var a=s(1609),l=s(5826),c=s(4306),d=s(461),u="autosize:resized",p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={lineHeight:null},t.textarea=null,t.onResize=function(e){t.props.onResize&&t.props.onResize(e)},t.updateLineHeight=function(){t.textarea&&t.setState({lineHeight:d(t.textarea)})},t.onChange=function(e){var s=t.props.onChange;t.currentValue=e.currentTarget.value,s&&s(e)},t}return n(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props,s=t.maxRows,o=t.async;"number"==typeof s&&this.updateLineHeight(),"number"==typeof s||o?setTimeout((function(){return e.textarea&&c(e.textarea)})):this.textarea&&c(this.textarea),this.textarea&&this.textarea.addEventListener(u,this.onResize)},t.prototype.componentWillUnmount=function(){this.textarea&&(this.textarea.removeEventListener(u,this.onResize),c.destroy(this.textarea))},t.prototype.render=function(){var e=this,t=this.props,s=(t.onResize,t.maxRows),o=(t.onChange,t.style),n=(t.innerRef,t.children),l=r(t,["onResize","maxRows","onChange","style","innerRef","children"]),c=this.state.lineHeight,d=s&&c?c*s:null;return a.createElement("textarea",i({},l,{onChange:this.onChange,style:d?i({},o,{maxHeight:d}):o,ref:function(t){e.textarea=t,"function"==typeof e.props.innerRef?e.props.innerRef(t):e.props.innerRef&&(e.props.innerRef.current=t)}}),n)},t.prototype.componentDidUpdate=function(){this.textarea&&c.update(this.textarea)},t.defaultProps={rows:1,async:!1},t.propTypes={rows:l.number,maxRows:l.number,onResize:l.func,innerRef:l.any,async:l.bool},t}(a.Component);t.TextareaAutosize=a.forwardRef((function(e,t){return a.createElement(p,i({},e,{innerRef:t}))}))},4132:(e,t,s)=>{"use strict";var o=s(4462);t.A=o.TextareaAutosize},9681:e=>{var t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Ấ:"A",Ắ:"A",Ẳ:"A",Ẵ:"A",Ặ:"A",Æ:"AE",Ầ:"A",Ằ:"A",Ȃ:"A",Ả:"A",Ạ:"A",Ẩ:"A",Ẫ:"A",Ậ:"A",Ç:"C",Ḉ:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ế:"E",Ḗ:"E",Ề:"E",Ḕ:"E",Ḝ:"E",Ȇ:"E",Ẻ:"E",Ẽ:"E",Ẹ:"E",Ể:"E",Ễ:"E",Ệ:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ḯ:"I",Ȋ:"I",Ỉ:"I",Ị:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ố:"O",Ṍ:"O",Ṓ:"O",Ȏ:"O",Ỏ:"O",Ọ:"O",Ổ:"O",Ỗ:"O",Ộ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ớ:"O",Ợ:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ủ:"U",Ụ:"U",Ử:"U",Ữ:"U",Ự:"U",Ý:"Y",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",ấ:"a",ắ:"a",ẳ:"a",ẵ:"a",ặ:"a",æ:"ae",ầ:"a",ằ:"a",ȃ:"a",ả:"a",ạ:"a",ẩ:"a",ẫ:"a",ậ:"a",ç:"c",ḉ:"c",è:"e",é:"e",ê:"e",ë:"e",ế:"e",ḗ:"e",ề:"e",ḕ:"e",ḝ:"e",ȇ:"e",ẻ:"e",ẽ:"e",ẹ:"e",ể:"e",ễ:"e",ệ:"e",ì:"i",í:"i",î:"i",ï:"i",ḯ:"i",ȋ:"i",ỉ:"i",ị:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ố:"o",ṍ:"o",ṓ:"o",ȏ:"o",ỏ:"o",ọ:"o",ổ:"o",ỗ:"o",ộ:"o",ờ:"o",ở:"o",ỡ:"o",ớ:"o",ợ:"o",ù:"u",ú:"u",û:"u",ü:"u",ủ:"u",ụ:"u",ử:"u",ữ:"u",ự:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",C̆:"C",c̆:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",Ǵ:"G",ĝ:"g",ǵ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ḫ:"H",ḫ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ḱ:"K",ḱ:"k",K̆:"K",k̆:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ḿ:"M",ḿ:"m",M̆:"M",m̆:"m",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",N̆:"N",n̆:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",P̆:"P",p̆:"p",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",R̆:"R",r̆:"r",Ȓ:"R",ȓ:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",Ș:"S",ș:"s",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",ț:"t",Ț:"T",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",T̆:"T",t̆:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ȗ:"U",ȗ:"u",V̆:"V",v̆:"v",Ŵ:"W",ŵ:"w",Ẃ:"W",ẃ:"w",X̆:"X",x̆:"x",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Y̆:"Y",y̆:"y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ứ:"U",ứ:"u",Ṹ:"U",ṹ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o",Þ:"TH",þ:"th",Ṕ:"P",ṕ:"p",Ṥ:"S",ṥ:"s",X́:"X",x́:"x",Ѓ:"Г",ѓ:"г",Ќ:"К",ќ:"к",A̋:"A",a̋:"a",E̋:"E",e̋:"e",I̋:"I",i̋:"i",Ǹ:"N",ǹ:"n",Ồ:"O",ồ:"o",Ṑ:"O",ṑ:"o",Ừ:"U",ừ:"u",Ẁ:"W",ẁ:"w",Ỳ:"Y",ỳ:"y",Ȁ:"A",ȁ:"a",Ȅ:"E",ȅ:"e",Ȉ:"I",ȉ:"i",Ȍ:"O",ȍ:"o",Ȑ:"R",ȑ:"r",Ȕ:"U",ȕ:"u",B̌:"B",b̌:"b",Č̣:"C",č̣:"c",Ê̌:"E",ê̌:"e",F̌:"F",f̌:"f",Ǧ:"G",ǧ:"g",Ȟ:"H",ȟ:"h",J̌:"J",ǰ:"j",Ǩ:"K",ǩ:"k",M̌:"M",m̌:"m",P̌:"P",p̌:"p",Q̌:"Q",q̌:"q",Ř̩:"R",ř̩:"r",Ṧ:"S",ṧ:"s",V̌:"V",v̌:"v",W̌:"W",w̌:"w",X̌:"X",x̌:"x",Y̌:"Y",y̌:"y",A̧:"A",a̧:"a",B̧:"B",b̧:"b",Ḑ:"D",ḑ:"d",Ȩ:"E",ȩ:"e",Ɛ̧:"E",ɛ̧:"e",Ḩ:"H",ḩ:"h",I̧:"I",i̧:"i",Ɨ̧:"I",ɨ̧:"i",M̧:"M",m̧:"m",O̧:"O",o̧:"o",Q̧:"Q",q̧:"q",U̧:"U",u̧:"u",X̧:"X",x̧:"x",Z̧:"Z",z̧:"z",й:"и",Й:"И",ё:"е",Ё:"Е"},s=Object.keys(t).join("|"),o=new RegExp(s,"g"),n=new RegExp(s,"");function i(e){return t[e]}var r=function(e){return e.replace(o,i)};e.exports=r,e.exports.has=function(e){return!!e.match(n)},e.exports.remove=r},1609:e=>{"use strict";e.exports=window.React}},t={};function s(o){var n=t[o];if(void 0!==n)return n.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,s),i.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};(()=>{"use strict";s.r(o),s.d(o,{AlignmentToolbar:()=>sm,Autocomplete:()=>tm,AutosaveMonitor:()=>na,BlockAlignmentToolbar:()=>om,BlockControls:()=>nm,BlockEdit:()=>im,BlockEditorKeyboardShortcuts:()=>rm,BlockFormatControls:()=>am,BlockIcon:()=>lm,BlockInspector:()=>cm,BlockList:()=>dm,BlockMover:()=>um,BlockNavigationDropdown:()=>pm,BlockSelectionClearer:()=>hm,BlockSettingsMenu:()=>mm,BlockTitle:()=>gm,BlockToolbar:()=>_m,CharacterCount:()=>vp,ColorPalette:()=>fm,ContrastChecker:()=>bm,CopyHandler:()=>ym,DefaultBlockAppender:()=>xm,DocumentBar:()=>da,DocumentOutline:()=>xa,DocumentOutlineCheck:()=>va,EditorHistoryRedo:()=>Pa,EditorHistoryUndo:()=>Ca,EditorKeyboardShortcuts:()=>sa,EditorKeyboardShortcutsRegister:()=>wa,EditorNotices:()=>Ea,EditorProvider:()=>Kh,EditorSnackbars:()=>Ba,EntitiesSavedStates:()=>Fa,ErrorBoundary:()=>Ga,FontSizePicker:()=>vm,InnerBlocks:()=>Sm,Inserter:()=>wm,InspectorAdvancedControls:()=>km,InspectorControls:()=>Pm,LocalAutosaveMonitor:()=>Ya,MediaPlaceholder:()=>Im,MediaUpload:()=>Nm,MediaUploadCheck:()=>Am,MultiSelectScrollIntoView:()=>Dm,NavigableToolbar:()=>Rm,ObserveTyping:()=>Mm,PageAttributesCheck:()=>Ka,PageAttributesOrder:()=>Xa,PageAttributesPanel:()=>ml,PageAttributesParent:()=>ul,PageTemplate:()=>Cl,PanelColorSettings:()=>Cm,PlainText:()=>jm,PluginBlockSettingsMenuItem:()=>Ol,PluginDocumentSettingPanel:()=>Ml,PluginMoreMenuItem:()=>Ll,PluginPostPublishPanel:()=>zl,PluginPostStatusInfo:()=>Wl,PluginPrePublishPanel:()=>ql,PluginPreviewMenuItem:()=>Ql,PluginSidebar:()=>Xl,PluginSidebarMoreMenuItem:()=>Jl,PostAuthor:()=>pc,PostAuthorCheck:()=>hc,PostAuthorPanel:()=>gc,PostComments:()=>fc,PostDiscussionPanel:()=>wc,PostExcerpt:()=>Sc,PostExcerptCheck:()=>kc,PostExcerptPanel:()=>Ic,PostFeaturedImage:()=>zc,PostFeaturedImageCheck:()=>Rc,PostFeaturedImagePanel:()=>Gc,PostFormat:()=>Zc,PostFormatCheck:()=>$c,PostLastRevision:()=>Qc,PostLastRevisionCheck:()=>Yc,PostLastRevisionPanel:()=>Xc,PostLockedModal:()=>Jc,PostPendingStatus:()=>td,PostPendingStatusCheck:()=>ed,PostPingbacks:()=>bc,PostPreviewButton:()=>sd,PostPublishButton:()=>rd,PostPublishButtonLabel:()=>od,PostPublishPanel:()=>vu,PostSavedState:()=>Mu,PostSchedule:()=>Ed,PostScheduleCheck:()=>Ou,PostScheduleLabel:()=>Bd,PostSchedulePanel:()=>Fu,PostSlug:()=>zu,PostSlugCheck:()=>Vu,PostSticky:()=>Iu,PostStickyCheck:()=>Bu,PostSwitchToDraftButton:()=>Hu,PostSyncStatus:()=>Gu,PostTaxonomies:()=>Wu,PostTaxonomiesCheck:()=>Zu,PostTaxonomiesFlatTermSelector:()=>Hd,PostTaxonomiesHierarchicalTermSelector:()=>Jd,PostTaxonomiesPanel:()=>Ku,PostTemplatePanel:()=>rc,PostTextEditor:()=>Qu,PostTitle:()=>np,PostTitleRaw:()=>ip,PostTrash:()=>ap,PostTrashCheck:()=>rp,PostTypeSupportCheck:()=>qa,PostURL:()=>cp,PostURLCheck:()=>dp,PostURLLabel:()=>up,PostURLPanel:()=>hp,PostVisibility:()=>cd,PostVisibilityCheck:()=>gp,PostVisibilityLabel:()=>ud,RichText:()=>em,RichTextShortcut:()=>Em,RichTextToolbarButton:()=>Tm,ServerSideRender:()=>Qh(),SkipToSelectedBlock:()=>Om,TableOfContents:()=>Sp,TextEditorGlobalKeyboardShortcuts:()=>Jm,ThemeSupportCheck:()=>Dc,TimeToRead:()=>xp,URLInput:()=>Lm,URLInputButton:()=>Fm,URLPopover:()=>Vm,UnsavedChangesWarning:()=>kp,VisualEditorGlobalKeyboardShortcuts:()=>Xm,Warning:()=>Um,WordCount:()=>bp,WritingFlow:()=>zm,__unstableRichTextInputEvent:()=>Bm,cleanForSlug:()=>eg,createCustomColorsHOC:()=>Hm,getColorClassName:()=>Gm,getColorObjectByAttributeValues:()=>$m,getColorObjectByColorValue:()=>Wm,getFontSize:()=>Zm,getFontSizeClass:()=>Ym,getTemplatePartIcon:()=>W,mediaUpload:()=>Np,privateApis:()=>yf,registerEntityAction:()=>xf,store:()=>qi,storeConfig:()=>Ki,transformStyles:()=>m.transformStyles,unregisterEntityAction:()=>vf,useEntitiesSavedStatesIsDirty:()=>Oa,usePostScheduleLabel:()=>Id,usePostURLLabel:()=>pp,usePostVisibilityLabel:()=>pd,userAutocompleter:()=>Xi,withColorContext:()=>Km,withColors:()=>qm,withFontSizes:()=>Qm});var e={};s.r(e),s.d(e,{__experimentalGetDefaultTemplatePartAreas:()=>rs,__experimentalGetDefaultTemplateType:()=>as,__experimentalGetDefaultTemplateTypes:()=>is,__experimentalGetTemplateInfo:()=>ls,__unstableIsEditorReady:()=>Xe,canInsertBlockType:()=>ts,canUserUseUnfilteredHTML:()=>He,didPostSaveRequestFail:()=>Ce,didPostSaveRequestSucceed:()=>Pe,getActivePostLock:()=>ze,getAdjacentBlockClientId:()=>Ct,getAutosaveAttribute:()=>ue,getBlock:()=>ut,getBlockAttributes:()=>dt,getBlockCount:()=>ft,getBlockHierarchyRootClientId:()=>Pt,getBlockIndex:()=>Vt,getBlockInsertionPoint:()=>qt,getBlockListSettings:()=>ns,getBlockMode:()=>Zt,getBlockName:()=>lt,getBlockOrder:()=>Ft,getBlockRootClientId:()=>kt,getBlockSelectionEnd:()=>yt,getBlockSelectionStart:()=>bt,getBlocks:()=>pt,getBlocksByClientId:()=>_t,getClientIdsOfDescendants:()=>ht,getClientIdsWithDescendants:()=>mt,getCurrentPost:()=>te,getCurrentPostAttribute:()=>le,getCurrentPostId:()=>oe,getCurrentPostLastRevisionId:()=>re,getCurrentPostRevisionsCount:()=>ie,getCurrentPostType:()=>se,getCurrentTemplateId:()=>ne,getDeviceType:()=>tt,getEditedPostAttribute:()=>de,getEditedPostContent:()=>Ie,getEditedPostPreviewLink:()=>Te,getEditedPostSlug:()=>Re,getEditedPostVisibility:()=>pe,getEditorBlocks:()=>$e,getEditorMode:()=>nt,getEditorSelection:()=>Qe,getEditorSelectionEnd:()=>qe,getEditorSelectionStart:()=>Ke,getEditorSettings:()=>Je,getFirstMultiSelectedBlockClientId:()=>Nt,getGlobalBlockCount:()=>gt,getInserterItems:()=>ss,getLastMultiSelectedBlockClientId:()=>At,getMultiSelectedBlockClientIds:()=>Bt,getMultiSelectedBlocks:()=>It,getMultiSelectedBlocksEndClientId:()=>Lt,getMultiSelectedBlocksStartClientId:()=>Ot,getNextBlockClientId:()=>Et,getPermalink:()=>De,getPermalinkParts:()=>Me,getPostEdits:()=>ae,getPostLockUser:()=>Ue,getPostTypeLabel:()=>cs,getPreviousBlockClientId:()=>jt,getRenderingMode:()=>et,getSelectedBlock:()=>St,getSelectedBlockClientId:()=>wt,getSelectedBlockCount:()=>xt,getSelectedBlocksInitialCaretPosition:()=>Tt,getStateBeforeOptimisticTransaction:()=>it,getSuggestedPostFormat:()=>Be,getTemplate:()=>Jt,getTemplateLock:()=>es,hasChangedContent:()=>Q,hasEditorRedo:()=>K,hasEditorUndo:()=>Y,hasInserterItems:()=>os,hasMultiSelection:()=>Gt,hasNonPostEntityChanges:()=>J,hasSelectedBlock:()=>vt,hasSelectedInnerBlock:()=>zt,inSomeHistory:()=>rt,isAncestorMultiSelected:()=>Mt,isAutosavingPost:()=>je,isBlockInsertionPointVisible:()=>Qt,isBlockMultiSelected:()=>Rt,isBlockSelected:()=>Ut,isBlockValid:()=>ct,isBlockWithinSelection:()=>Ht,isCaretWithinFormattedText:()=>Kt,isCleanNewPost:()=>ee,isCurrentPostPending:()=>he,isCurrentPostPublished:()=>me,isCurrentPostScheduled:()=>ge,isDeletingPost:()=>we,isEditedPostAutosaveable:()=>ye,isEditedPostBeingScheduled:()=>xe,isEditedPostDateFloating:()=>ve,isEditedPostDirty:()=>X,isEditedPostEmpty:()=>be,isEditedPostNew:()=>q,isEditedPostPublishable:()=>_e,isEditedPostSaveable:()=>fe,isEditorPanelEnabled:()=>Ze,isEditorPanelOpened:()=>Ye,isEditorPanelRemoved:()=>We,isFirstMultiSelectedBlock:()=>Dt,isInserterOpened:()=>ot,isListViewOpened:()=>st,isMultiSelecting:()=>$t,isPermalinkEditable:()=>Ae,isPostAutosavingLocked:()=>Fe,isPostLockTakeover:()=>Ve,isPostLocked:()=>Oe,isPostSavingLocked:()=>Le,isPreviewingPost:()=>Ee,isPublishSidebarEnabled:()=>Ge,isPublishSidebarOpened:()=>ds,isPublishingPost:()=>Ne,isSavingNonPostEntityChanges:()=>ke,isSavingPost:()=>Se,isSelectionEnabled:()=>Wt,isTyping:()=>Yt,isValidTemplate:()=>Xt});var t={};s.r(t),s.d(t,{__experimentalTearDownEditor:()=>ys,__unstableSaveForPreview:()=>Ts,autosave:()=>Es,clearSelectedBlock:()=>co,closePublishSidebar:()=>Xs,createUndoLevel:()=>Ns,disablePublishSidebar:()=>Rs,editPost:()=>ks,enablePublishSidebar:()=>Ds,enterFormattedText:()=>To,exitFormattedText:()=>Bo,hideInsertionPoint:()=>xo,insertBlock:()=>fo,insertBlocks:()=>bo,insertDefaultBlock:()=>Io,lockPostAutosaving:()=>Ls,lockPostSaving:()=>Ms,mergeBlocks:()=>So,moveBlockToPosition:()=>_o,moveBlocksDown:()=>mo,moveBlocksUp:()=>go,multiSelect:()=>lo,openPublishSidebar:()=>Qs,receiveBlocks:()=>so,redo:()=>Bs,refreshPost:()=>Cs,removeBlock:()=>Po,removeBlocks:()=>ko,removeEditorPanel:()=>Ws,replaceBlock:()=>ho,replaceBlocks:()=>po,resetBlocks:()=>to,resetEditorBlocks:()=>Vs,resetPost:()=>xs,savePost:()=>Ps,selectBlock:()=>io,setDeviceType:()=>Hs,setEditedPost:()=>Ss,setIsInserterOpened:()=>Zs,setIsListViewOpened:()=>Ys,setRenderingMode:()=>zs,setTemplateValidity:()=>vo,setupEditor:()=>bs,setupEditorState:()=>ws,showInsertionPoint:()=>yo,startMultiSelect:()=>ro,startTyping:()=>jo,stopMultiSelect:()=>ao,stopTyping:()=>Eo,switchEditorMode:()=>qs,synchronizeTemplate:()=>wo,toggleBlockMode:()=>Co,toggleDistractionFree:()=>Ks,toggleEditorPanelEnabled:()=>Gs,toggleEditorPanelOpened:()=>$s,togglePublishSidebar:()=>Js,toggleSelection:()=>uo,trashPost:()=>js,undo:()=>Is,unlockPostAutosaving:()=>Fs,unlockPostSaving:()=>Os,updateBlock:()=>oo,updateBlockAttributes:()=>no,updateBlockListSettings:()=>No,updateEditorSettings:()=>Us,updatePost:()=>vs,updatePostLock:()=>As});var n={};s.r(n),s.d(n,{createTemplate:()=>Ci,hideBlockTypes:()=>Ei,registerEntityAction:()=>vi,registerPostTypeActions:()=>ki,removeTemplates:()=>Ii,revertTemplate:()=>Bi,saveDirtyEntities:()=>Ti,setCurrentTemplateId:()=>Pi,setIsReady:()=>Si,showBlockTypes:()=>ji,unregisterEntityAction:()=>wi});var i={};s.r(i),s.d(i,{getEntityActions:()=>Wi,getInserterSidebarToggleRef:()=>zi,getInsertionPoint:()=>Vi,getListViewToggleRef:()=>Ui,getPostBlocksByName:()=>Yi,getPostIcon:()=>Gi,hasPostMetaChanges:()=>$i,isEntityReady:()=>Zi});var r={};s.r(r),s.d(r,{closeModal:()=>Pr,disableComplementaryArea:()=>br,enableComplementaryArea:()=>fr,openModal:()=>kr,pinItem:()=>yr,setDefaultComplementaryArea:()=>_r,setFeatureDefaults:()=>Sr,setFeatureValue:()=>wr,toggleFeature:()=>vr,unpinItem:()=>xr});var a={};s.r(a),s.d(a,{getActiveComplementaryArea:()=>Cr,isComplementaryAreaLoading:()=>jr,isFeatureActive:()=>Tr,isItemPinned:()=>Er,isModalActive:()=>Br});var l={};s.r(l),s.d(l,{ActionItem:()=>Fr,ComplementaryArea:()=>Kr,ComplementaryAreaMoreMenuItem:()=>Ur,FullscreenMode:()=>qr,InterfaceSkeleton:()=>ta,NavigableRegion:()=>Xr,PinnedItems:()=>Hr,store:()=>Nr});const c=window.wp.data,d=window.wp.coreData,u=window.wp.element,p=window.wp.compose,h=window.wp.hooks,m=window.wp.blockEditor,g={...m.SETTINGS_DEFAULTS,richEditingEnabled:!0,codeEditingEnabled:!0,fontLibraryEnabled:!0,enableCustomFields:void 0,defaultRenderingMode:"post-only"};const _=(0,c.combineReducers)({actions:function(e={},t){var s;switch(t.type){case"REGISTER_ENTITY_ACTION":return{...e,[t.kind]:{...e[t.kind],[t.name]:[...(null!==(s=e[t.kind]?.[t.name])&&void 0!==s?s:[]).filter((e=>e.id!==t.config.id)),t.config]}};case"UNREGISTER_ENTITY_ACTION":var o;return{...e,[t.kind]:{...e[t.kind],[t.name]:(null!==(o=e[t.kind]?.[t.name])&&void 0!==o?o:[]).filter((e=>e.id!==t.actionId))}}}return e},isReady:function(e={},t){return"SET_IS_READY"===t.type?{...e,[t.kind]:{...e[t.kind],[t.name]:!0}}:e}});function f(e){return e&&"object"==typeof e&&"raw"in e?e.raw:e}const b=(0,c.combineReducers)({postId:function(e=null,t){return"SET_EDITED_POST"===t.type?t.postId:e},postType:function(e=null,t){return"SET_EDITED_POST"===t.type?t.postType:e},templateId:function(e=null,t){return"SET_CURRENT_TEMPLATE_ID"===t.type?t.id:e},saving:function(e={},t){switch(t.type){case"REQUEST_POST_UPDATE_START":case"REQUEST_POST_UPDATE_FINISH":return{pending:"REQUEST_POST_UPDATE_START"===t.type,options:t.options||{}}}return e},deleting:function(e={},t){switch(t.type){case"REQUEST_POST_DELETE_START":case"REQUEST_POST_DELETE_FINISH":return{pending:"REQUEST_POST_DELETE_START"===t.type}}return e},postLock:function(e={isLocked:!1},t){return"UPDATE_POST_LOCK"===t.type?t.lock:e},template:function(e={isValid:!0},t){return"SET_TEMPLATE_VALIDITY"===t.type?{...e,isValid:t.isValid}:e},postSavingLock:function(e={},t){switch(t.type){case"LOCK_POST_SAVING":return{...e,[t.lockName]:!0};case"UNLOCK_POST_SAVING":{const{[t.lockName]:s,...o}=e;return o}}return e},editorSettings:function(e=g,t){return"UPDATE_EDITOR_SETTINGS"===t.type?{...e,...t.settings}:e},postAutosavingLock:function(e={},t){switch(t.type){case"LOCK_POST_AUTOSAVING":return{...e,[t.lockName]:!0};case"UNLOCK_POST_AUTOSAVING":{const{[t.lockName]:s,...o}=e;return o}}return e},renderingMode:function(e="post-only",t){return"SET_RENDERING_MODE"===t.type?t.mode:e},deviceType:function(e="Desktop",t){return"SET_DEVICE_TYPE"===t.type?t.deviceType:e},removedPanels:function(e=[],t){if("REMOVE_PANEL"===t.type)if(!e.includes(t.panelName))return[...e,t.panelName];return e},blockInserterPanel:function(e=!1,t){switch(t.type){case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value}return e},inserterSidebarToggleRef:function(e={current:null}){return e},listViewPanel:function(e=!1,t){switch(t.type){case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen}return e},listViewToggleRef:function(e={current:null}){return e},publishSidebarActive:function(e=!1,t){switch(t.type){case"OPEN_PUBLISH_SIDEBAR":return!0;case"CLOSE_PUBLISH_SIDEBAR":return!1;case"TOGGLE_PUBLISH_SIDEBAR":return!e}return e},dataviews:_}),y=window.wp.blocks,x=window.wp.date,v=window.wp.url,w=window.wp.deprecated;var S=s.n(w);const k=window.wp.primitives,P=window.ReactJSXRuntime,C=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),j=window.wp.preferences,E=new Set(["meta"]),T="SAVE_POST_NOTICE_ID",B="TRASH_POST_NOTICE_ID",I=/%(?:postname|pagename)%/,N=6e4,A=["title","excerpt","content"],D="uncategorized",R="wp_template",M="wp_template_part",O="wp_block",L="wp_navigation",F={custom:"custom",theme:"theme",plugin:"plugin"},V=["wp_template","wp_template_part"],U=[...V,"wp_block","wp_navigation"],z=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M18.5 10.5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),H=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{fillRule:"evenodd",d:"M18 5.5h-8v8h8.5V6a.5.5 0 00-.5-.5zm-9.5 8h-3V6a.5.5 0 01.5-.5h2.5v8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),G=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),$=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-17.6 1L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})});function W(e){return"header"===e?z:"footer"===e?H:"sidebar"===e?G:$}const Z={},Y=(0,c.createRegistrySelector)((e=>()=>e(d.store).hasUndo())),K=(0,c.createRegistrySelector)((e=>()=>e(d.store).hasRedo()));function q(e){return"auto-draft"===te(e).status}function Q(e){return"content"in ae(e)}const X=(0,c.createRegistrySelector)((e=>t=>{const s=se(t),o=oe(t);return e(d.store).hasEditsForEntityRecord("postType",s,o)})),J=(0,c.createRegistrySelector)((e=>t=>{const s=e(d.store).__experimentalGetDirtyEntityRecords(),{type:o,id:n}=te(t);return s.some((e=>"postType"!==e.kind||e.name!==o||e.key!==n))}));function ee(e){return!X(e)&&q(e)}const te=(0,c.createRegistrySelector)((e=>t=>{const s=oe(t),o=se(t),n=e(d.store).getRawEntityRecord("postType",o,s);return n||Z}));function se(e){return e.postType}function oe(e){return e.postId}function ne(e){return e.templateId}function ie(e){var t;return null!==(t=te(e)._links?.["version-history"]?.[0]?.count)&&void 0!==t?t:0}function re(e){var t;return null!==(t=te(e)._links?.["predecessor-version"]?.[0]?.id)&&void 0!==t?t:null}const ae=(0,c.createRegistrySelector)((e=>t=>{const s=se(t),o=oe(t);return e(d.store).getEntityRecordEdits("postType",s,o)||Z}));function le(e,t){switch(t){case"type":return se(e);case"id":return oe(e);default:const s=te(e);if(!s.hasOwnProperty(t))break;return f(s[t])}}const ce=(0,c.createSelector)(((e,t)=>{const s=ae(e);return s.hasOwnProperty(t)?{...le(e,t),...s[t]}:le(e,t)}),((e,t)=>[le(e,t),ae(e)[t]]));function de(e,t){if("content"===t)return Ie(e);const s=ae(e);return s.hasOwnProperty(t)?E.has(t)?ce(e,t):s[t]:le(e,t)}const ue=(0,c.createRegistrySelector)((e=>(t,s)=>{if(!A.includes(s)&&"preview_link"!==s)return;const o=se(t);if("wp_template"===o)return!1;const n=oe(t),i=e(d.store).getCurrentUser()?.id,r=e(d.store).getAutosave(o,n,i);return r?f(r[s]):void 0}));function pe(e){if("private"===de(e,"status"))return"private";return de(e,"password")?"password":"public"}function he(e){return"pending"===te(e).status}function me(e,t){const s=t||te(e);return-1!==["publish","private"].indexOf(s.status)||"future"===s.status&&!(0,x.isInTheFuture)(new Date(Number((0,x.getDate)(s.date))-N))}function ge(e){return"future"===te(e).status&&!me(e)}function _e(e){const t=te(e);return X(e)||-1===["publish","private","future"].indexOf(t.status)}function fe(e){return!Se(e)&&(!!de(e,"title")||!!de(e,"excerpt")||!be(e)||"native"===u.Platform.OS)}const be=(0,c.createRegistrySelector)((e=>t=>{const s=oe(t),o=se(t),n=e(d.store).getEditedEntityRecord("postType",o,s);if("function"!=typeof n.content)return!n.content;const i=de(t,"blocks");if(0===i.length)return!0;if(i.length>1)return!1;const r=i[0].name;return(r===(0,y.getDefaultBlockName)()||r===(0,y.getFreeformContentHandlerName)())&&!Ie(t)})),ye=(0,c.createRegistrySelector)((e=>t=>{if(!fe(t))return!1;if(Fe(t))return!1;const s=se(t);if("wp_template"===s)return!1;const o=oe(t),n=e(d.store).hasFetchedAutosaves(s,o),i=e(d.store).getCurrentUser()?.id,r=e(d.store).getAutosave(s,o,i);return!!n&&(!r||(!!Q(t)||["title","excerpt","meta"].some((e=>f(r[e])!==de(t,e)))))}));function xe(e){const t=de(e,"date"),s=new Date(Number((0,x.getDate)(t))-N);return(0,x.isInTheFuture)(s)}function ve(e){const t=de(e,"date"),s=de(e,"modified"),o=te(e).status;return("draft"===o||"auto-draft"===o||"pending"===o)&&(t===s||null===t)}function we(e){return!!e.deleting.pending}function Se(e){return!!e.saving.pending}const ke=(0,c.createRegistrySelector)((e=>t=>{const s=e(d.store).__experimentalGetEntitiesBeingSaved(),{type:o,id:n}=te(t);return s.some((e=>"postType"!==e.kind||e.name!==o||e.key!==n))})),Pe=(0,c.createRegistrySelector)((e=>t=>{const s=se(t),o=oe(t);return!e(d.store).getLastEntitySaveError("postType",s,o)})),Ce=(0,c.createRegistrySelector)((e=>t=>{const s=se(t),o=oe(t);return!!e(d.store).getLastEntitySaveError("postType",s,o)}));function je(e){return Se(e)&&Boolean(e.saving.options?.isAutosave)}function Ee(e){return Se(e)&&Boolean(e.saving.options?.isPreview)}function Te(e){if(e.saving.pending||Se(e))return;let t=ue(e,"preview_link");t&&"draft"!==te(e).status||(t=de(e,"link"),t&&(t=(0,v.addQueryArgs)(t,{preview:!0})));const s=de(e,"featured_media");return t&&s?(0,v.addQueryArgs)(t,{_thumbnail_id:s}):t}const Be=(0,c.createRegistrySelector)((e=>()=>{const t=e(m.store).getBlocks();if(t.length>2)return null;let s;if(1===t.length&&(s=t[0].name,"core/embed"===s)){const e=t[0].attributes?.providerNameSlug;["youtube","vimeo"].includes(e)?s="core/video":["spotify","soundcloud"].includes(e)&&(s="core/audio")}switch(2===t.length&&"core/paragraph"===t[1].name&&(s=t[0].name),s){case"core/image":return"image";case"core/quote":case"core/pullquote":return"quote";case"core/gallery":return"gallery";case"core/video":return"video";case"core/audio":return"audio";default:return null}})),Ie=(0,c.createRegistrySelector)((e=>t=>{const s=oe(t),o=se(t),n=e(d.store).getEditedEntityRecord("postType",o,s);if(n){if("function"==typeof n.content)return n.content(n);if(n.blocks)return(0,y.__unstableSerializeAndClean)(n.blocks);if(n.content)return n.content}return""}));function Ne(e){return Se(e)&&!me(e)&&"publish"===de(e,"status")}function Ae(e){const t=de(e,"permalink_template");return I.test(t)}function De(e){const t=Me(e);if(!t)return null;const{prefix:s,postName:o,suffix:n}=t;return Ae(e)?s+o+n:s}function Re(e){return de(e,"slug")||(0,v.cleanForSlug)(de(e,"title"))||oe(e)}function Me(e){const t=de(e,"permalink_template");if(!t)return null;const s=de(e,"slug")||de(e,"generated_slug"),[o,n]=t.split(I);return{prefix:o,postName:s,suffix:n}}function Oe(e){return e.postLock.isLocked}function Le(e){return Object.keys(e.postSavingLock).length>0}function Fe(e){return Object.keys(e.postAutosavingLock).length>0}function Ve(e){return e.postLock.isTakeover}function Ue(e){return e.postLock.user}function ze(e){return e.postLock.activePostLock}function He(e){return Boolean(te(e)._links?.hasOwnProperty("wp:action-unfiltered-html"))}const Ge=(0,c.createRegistrySelector)((e=>()=>!!e(j.store).get("core","isPublishSidebarEnabled"))),$e=(0,c.createSelector)((e=>de(e,"blocks")||(0,y.parse)(Ie(e))),(e=>[de(e,"blocks"),Ie(e)]));function We(e,t){return e.removedPanels.includes(t)}const Ze=(0,c.createRegistrySelector)((e=>(t,s)=>{const o=e(j.store).get("core","inactivePanels");return!We(t,s)&&!o?.includes(s)})),Ye=(0,c.createRegistrySelector)((e=>(t,s)=>{const o=e(j.store).get("core","openPanels");return!!o?.includes(s)}));function Ke(e){return S()("select('core/editor').getEditorSelectionStart",{since:"5.8",alternative:"select('core/editor').getEditorSelection"}),de(e,"selection")?.selectionStart}function qe(e){return S()("select('core/editor').getEditorSelectionStart",{since:"5.8",alternative:"select('core/editor').getEditorSelection"}),de(e,"selection")?.selectionEnd}function Qe(e){return de(e,"selection")}function Xe(e){return!!e.postId}function Je(e){return e.editorSettings}function et(e){return e.renderingMode}const tt=(0,c.createRegistrySelector)((e=>t=>"zoom-out"===e(m.store).__unstableGetEditorMode()?"Desktop":t.deviceType));function st(e){return e.listViewPanel}function ot(e){return!!e.blockInserterPanel}const nt=(0,c.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(j.store).get("core","editorMode"))&&void 0!==t?t:"visual"}));function it(){return S()("select('core/editor').getStateBeforeOptimisticTransaction",{since:"5.7",hint:"No state history is kept on this store anymore"}),null}function rt(){return S()("select('core/editor').inSomeHistory",{since:"5.7",hint:"No state history is kept on this store anymore"}),!1}function at(e){return(0,c.createRegistrySelector)((t=>(s,...o)=>(S()("`wp.data.select( 'core/editor' )."+e+"`",{since:"5.3",alternative:"`wp.data.select( 'core/block-editor' )."+e+"`",version:"6.2"}),t(m.store)[e](...o))))}const lt=at("getBlockName"),ct=at("isBlockValid"),dt=at("getBlockAttributes"),ut=at("getBlock"),pt=at("getBlocks"),ht=at("getClientIdsOfDescendants"),mt=at("getClientIdsWithDescendants"),gt=at("getGlobalBlockCount"),_t=at("getBlocksByClientId"),ft=at("getBlockCount"),bt=at("getBlockSelectionStart"),yt=at("getBlockSelectionEnd"),xt=at("getSelectedBlockCount"),vt=at("hasSelectedBlock"),wt=at("getSelectedBlockClientId"),St=at("getSelectedBlock"),kt=at("getBlockRootClientId"),Pt=at("getBlockHierarchyRootClientId"),Ct=at("getAdjacentBlockClientId"),jt=at("getPreviousBlockClientId"),Et=at("getNextBlockClientId"),Tt=at("getSelectedBlocksInitialCaretPosition"),Bt=at("getMultiSelectedBlockClientIds"),It=at("getMultiSelectedBlocks"),Nt=at("getFirstMultiSelectedBlockClientId"),At=at("getLastMultiSelectedBlockClientId"),Dt=at("isFirstMultiSelectedBlock"),Rt=at("isBlockMultiSelected"),Mt=at("isAncestorMultiSelected"),Ot=at("getMultiSelectedBlocksStartClientId"),Lt=at("getMultiSelectedBlocksEndClientId"),Ft=at("getBlockOrder"),Vt=at("getBlockIndex"),Ut=at("isBlockSelected"),zt=at("hasSelectedInnerBlock"),Ht=at("isBlockWithinSelection"),Gt=at("hasMultiSelection"),$t=at("isMultiSelecting"),Wt=at("isSelectionEnabled"),Zt=at("getBlockMode"),Yt=at("isTyping"),Kt=at("isCaretWithinFormattedText"),qt=at("getBlockInsertionPoint"),Qt=at("isBlockInsertionPointVisible"),Xt=at("isValidTemplate"),Jt=at("getTemplate"),es=at("getTemplateLock"),ts=at("canInsertBlockType"),ss=at("getInserterItems"),os=at("hasInserterItems"),ns=at("getBlockListSettings");function is(e){return Je(e)?.defaultTemplateTypes}const rs=(0,c.createSelector)((e=>{var t;return(null!==(t=Je(e)?.defaultTemplatePartAreas)&&void 0!==t?t:[]).map((e=>({...e,icon:W(e.icon)})))}),(e=>[Je(e)?.defaultTemplatePartAreas])),as=(0,c.createSelector)(((e,t)=>{var s;const o=is(e);return o&&null!==(s=Object.values(o).find((e=>e.slug===t)))&&void 0!==s?s:Z}),(e=>[is(e)])),ls=(0,c.createSelector)(((e,t)=>{if(!t)return Z;const{description:s,slug:o,title:n,area:i}=t,{title:r,description:a}=as(e,o),l="string"==typeof n?n:n?.rendered;return{title:l&&l!==o?l:r||o,description:("string"==typeof s?s:s?.raw)||a,icon:rs(e).find((e=>i===e.area))?.icon||C}}),(e=>[is(e),rs(e)])),cs=(0,c.createRegistrySelector)((e=>t=>{const s=se(t),o=e(d.store).getPostType(s);return o?.labels?.singular_name}));function ds(e){return e.publishSidebarActive}const us=window.wp.a11y,ps=window.wp.apiFetch;var hs=s.n(ps);const ms=window.wp.notices,gs=window.wp.i18n;function _s(e,t){return`wp-autosave-block-editor-post-${t?"auto-draft":e}`}function fs(e,t){window.sessionStorage.removeItem(_s(e,t))}const bs=(e,t,s)=>({dispatch:o})=>{o.setEditedPost(e.type,e.id);if("auto-draft"===e.status&&s){let n;n="content"in t?t.content:e.content.raw;let i=(0,y.parse)(n);i=(0,y.synchronizeBlocksWithTemplate)(i,s),o.resetEditorBlocks(i,{__unstableShouldCreateUndoLevel:!1})}t&&Object.values(t).some((([t,s])=>{var o;return s!==(null!==(o=e[t]?.raw)&&void 0!==o?o:e[t])}))&&o.editPost(t)};function ys(){return S()("wp.data.dispatch( 'core/editor' ).__experimentalTearDownEditor",{since:"6.5"}),{type:"DO_NOTHING"}}function xs(){return S()("wp.data.dispatch( 'core/editor' ).resetPost",{since:"6.0",version:"6.3",alternative:"Initialize the editor with the setupEditorState action"}),{type:"DO_NOTHING"}}function vs(){return S()("wp.data.dispatch( 'core/editor' ).updatePost",{since:"5.7",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}function ws(e){return S()("wp.data.dispatch( 'core/editor' ).setupEditorState",{since:"6.5",alternative:"wp.data.dispatch( 'core/editor' ).setEditedPost"}),Ss(e.type,e.id)}function Ss(e,t){return{type:"SET_EDITED_POST",postType:e,postId:t}}const ks=(e,t)=>({select:s,registry:o})=>{const{id:n,type:i}=s.getCurrentPost();o.dispatch(d.store).editEntityRecord("postType",i,n,e,t)},Ps=(e={})=>async({select:t,dispatch:s,registry:o})=>{if(!t.isEditedPostSaveable())return;const n=t.getEditedPostContent();e.isAutosave||s.editPost({content:n},{undoIgnore:!0});const i=t.getCurrentPost();let r={id:i.id,...o.select(d.store).getEntityRecordNonTransientEdits("postType",i.type,i.id),content:n};s({type:"REQUEST_POST_UPDATE_START",options:e});let a=!1;try{r=await(0,h.applyFiltersAsync)("editor.preSavePost",r,e)}catch(e){a=e}if(!a)try{await o.dispatch(d.store).saveEntityRecord("postType",i.type,r,e)}catch(e){a=e.message&&"unknown_error"!==e.code?e.message:(0,gs.__)("An error occurred while updating.")}if(a||(a=o.select(d.store).getLastEntitySaveError("postType",i.type,i.id)),!a)try{await(0,h.applyFilters)("editor.__unstableSavePost",Promise.resolve(),e)}catch(e){a=e}if(!a)try{await(0,h.doActionAsync)("editor.savePost",{id:i.id},e)}catch(e){a=e}if(s({type:"REQUEST_POST_UPDATE_FINISH",options:e}),a){const e=function(e){const{post:t,edits:s,error:o}=e;if(o&&"rest_autosave_no_changes"===o.code)return[];const n=["publish","private","future"],i=-1!==n.indexOf(t.status),r={publish:(0,gs.__)("Publishing failed."),private:(0,gs.__)("Publishing failed."),future:(0,gs.__)("Scheduling failed.")};let a=i||-1===n.indexOf(s.status)?(0,gs.__)("Updating failed."):r[s.status];return o.message&&!/<\/?[^>]*>/.test(o.message)&&(a=[a,o.message].join(" ")),[a,{id:T}]}({post:i,edits:r,error:a});e.length&&o.dispatch(ms.store).createErrorNotice(...e)}else{const s=t.getCurrentPost(),n=function(e){var t;const{previousPost:s,post:o,postType:n}=e;if(e.options?.isAutosave)return[];const i=["publish","private","future"],r=i.includes(s.status),a=i.includes(o.status),l="trash"===o.status&&"trash"!==s.status;let c,d,u=null!==(t=n?.viewable)&&void 0!==t&&t;l?(c=n.labels.item_trashed,u=!1):r||a?r&&!a?(c=n.labels.item_reverted_to_draft,u=!1):c=!r&&a?{publish:n.labels.item_published,private:n.labels.item_published_privately,future:n.labels.item_scheduled}[o.status]:n.labels.item_updated:(c=(0,gs.__)("Draft saved."),d=!0);const p=[];return u&&p.push({label:d?(0,gs.__)("View Preview"):n.labels.view_item,url:o.link}),[c,{id:T,type:"snackbar",actions:p}]}({previousPost:i,post:s,postType:await o.resolveSelect(d.store).getPostType(s.type),options:e});n.length&&o.dispatch(ms.store).createSuccessNotice(...n),e.isAutosave||o.dispatch(m.store).__unstableMarkLastChangeAsPersistent()}};function Cs(){return S()("wp.data.dispatch( 'core/editor' ).refreshPost",{since:"6.0",version:"6.3",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}const js=()=>async({select:e,dispatch:t,registry:s})=>{const o=e.getCurrentPostType(),n=await s.resolveSelect(d.store).getPostType(o);s.dispatch(ms.store).removeNotice(B);const{rest_base:i,rest_namespace:r="wp/v2"}=n;t({type:"REQUEST_POST_DELETE_START"});try{const s=e.getCurrentPost();await hs()({path:`/${r}/${i}/${s.id}`,method:"DELETE"}),await t.savePost()}catch(e){s.dispatch(ms.store).createErrorNotice(...(a={error:e},[a.error.message&&"unknown_error"!==a.error.code?a.error.message:(0,gs.__)("Trashing failed"),{id:B}]))}var a;t({type:"REQUEST_POST_DELETE_FINISH"})},Es=({local:e=!1,...t}={})=>async({select:s,dispatch:o})=>{const n=s.getCurrentPost();if("wp_template"!==n.type)if(e){const e=s.isEditedPostNew(),t=s.getEditedPostAttribute("title"),o=s.getEditedPostAttribute("content"),i=s.getEditedPostAttribute("excerpt");!function(e,t,s,o,n){window.sessionStorage.setItem(_s(e,t),JSON.stringify({post_title:s,content:o,excerpt:n}))}(n.id,e,t,o,i)}else await o.savePost({isAutosave:!0,...t})},Ts=({forceIsAutosaveable:e}={})=>async({select:t,dispatch:s})=>{if((e||t.isEditedPostAutosaveable())&&!t.isPostLocked()){["draft","auto-draft"].includes(t.getEditedPostAttribute("status"))?await s.savePost({isPreview:!0}):await s.autosave({isPreview:!0})}return t.getEditedPostPreviewLink()},Bs=()=>({registry:e})=>{e.dispatch(d.store).redo()},Is=()=>({registry:e})=>{e.dispatch(d.store).undo()};function Ns(){return S()("wp.data.dispatch( 'core/editor' ).createUndoLevel",{since:"6.0",version:"6.3",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}function As(e){return{type:"UPDATE_POST_LOCK",lock:e}}const Ds=()=>({registry:e})=>{e.dispatch(j.store).set("core","isPublishSidebarEnabled",!0)},Rs=()=>({registry:e})=>{e.dispatch(j.store).set("core","isPublishSidebarEnabled",!1)};function Ms(e){return{type:"LOCK_POST_SAVING",lockName:e}}function Os(e){return{type:"UNLOCK_POST_SAVING",lockName:e}}function Ls(e){return{type:"LOCK_POST_AUTOSAVING",lockName:e}}function Fs(e){return{type:"UNLOCK_POST_AUTOSAVING",lockName:e}}const Vs=(e,t={})=>({select:s,dispatch:o,registry:n})=>{const{__unstableShouldCreateUndoLevel:i,selection:r}=t,a={blocks:e,selection:r};if(!1!==i){const{id:e,type:t}=s.getCurrentPost();if(n.select(d.store).getEditedEntityRecord("postType",t,e).blocks===a.blocks)return void n.dispatch(d.store).__unstableCreateUndoLevel("postType",t,e);a.content=({blocks:e=[]})=>(0,y.__unstableSerializeAndClean)(e)}o.editPost(a)};function Us(e){return{type:"UPDATE_EDITOR_SETTINGS",settings:e}}const zs=e=>({dispatch:t,registry:s,select:o})=>{o.__unstableIsEditorReady()&&(s.dispatch(m.store).clearSelectedBlock(),t.editPost({selection:void 0},{undoIgnore:!0})),t({type:"SET_RENDERING_MODE",mode:e})};function Hs(e){return{type:"SET_DEVICE_TYPE",deviceType:e}}const Gs=e=>({registry:t})=>{var s;const o=null!==(s=t.select(j.store).get("core","inactivePanels"))&&void 0!==s?s:[];let n;n=!!o?.includes(e)?o.filter((t=>t!==e)):[...o,e],t.dispatch(j.store).set("core","inactivePanels",n)},$s=e=>({registry:t})=>{var s;const o=null!==(s=t.select(j.store).get("core","openPanels"))&&void 0!==s?s:[];let n;n=!!o?.includes(e)?o.filter((t=>t!==e)):[...o,e],t.dispatch(j.store).set("core","openPanels",n)};function Ws(e){return{type:"REMOVE_PANEL",panelName:e}}function Zs(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}function Ys(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}const Ks=()=>({dispatch:e,registry:t})=>{const s=t.select(j.store).get("core","distractionFree");s&&t.dispatch(j.store).set("core","fixedToolbar",!1),s||t.batch((()=>{t.dispatch(j.store).set("core","fixedToolbar",!0),e.setIsInserterOpened(!1),e.setIsListViewOpened(!1)})),t.batch((()=>{t.dispatch(j.store).set("core","distractionFree",!s),t.dispatch(ms.store).createInfoNotice(s?(0,gs.__)("Distraction free off."):(0,gs.__)("Distraction free on."),{id:"core/editor/distraction-free-mode/notice",type:"snackbar",actions:[{label:(0,gs.__)("Undo"),onClick:()=>{t.batch((()=>{t.dispatch(j.store).set("core","fixedToolbar",!!s),t.dispatch(j.store).toggle("core","distractionFree")}))}}]})}))},qs=e=>({dispatch:t,registry:s})=>{if(s.dispatch(j.store).set("core","editorMode",e),"visual"!==e&&s.dispatch(m.store).clearSelectedBlock(),"visual"===e)(0,us.speak)((0,gs.__)("Visual editor selected"),"assertive");else if("text"===e){s.select(j.store).get("core","distractionFree")&&t.toggleDistractionFree(),(0,us.speak)((0,gs.__)("Code editor selected"),"assertive")}};function Qs(){return{type:"OPEN_PUBLISH_SIDEBAR"}}function Xs(){return{type:"CLOSE_PUBLISH_SIDEBAR"}}function Js(){return{type:"TOGGLE_PUBLISH_SIDEBAR"}}const eo=e=>(...t)=>({registry:s})=>{S()("`wp.data.dispatch( 'core/editor' )."+e+"`",{since:"5.3",alternative:"`wp.data.dispatch( 'core/block-editor' )."+e+"`",version:"6.2"}),s.dispatch(m.store)[e](...t)},to=eo("resetBlocks"),so=eo("receiveBlocks"),oo=eo("updateBlock"),no=eo("updateBlockAttributes"),io=eo("selectBlock"),ro=eo("startMultiSelect"),ao=eo("stopMultiSelect"),lo=eo("multiSelect"),co=eo("clearSelectedBlock"),uo=eo("toggleSelection"),po=eo("replaceBlocks"),ho=eo("replaceBlock"),mo=eo("moveBlocksDown"),go=eo("moveBlocksUp"),_o=eo("moveBlockToPosition"),fo=eo("insertBlock"),bo=eo("insertBlocks"),yo=eo("showInsertionPoint"),xo=eo("hideInsertionPoint"),vo=eo("setTemplateValidity"),wo=eo("synchronizeTemplate"),So=eo("mergeBlocks"),ko=eo("removeBlocks"),Po=eo("removeBlock"),Co=eo("toggleBlockMode"),jo=eo("startTyping"),Eo=eo("stopTyping"),To=eo("enterFormattedText"),Bo=eo("exitFormattedText"),Io=eo("insertDefaultBlock"),No=eo("updateBlockListSettings"),Ao=window.wp.htmlEntities;const Do=window.wp.components,Ro=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})});var Mo=function(){return Mo=Object.assign||function(e){for(var t,s=1,o=arguments.length;s<o;s++)for(var n in t=arguments[s])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Mo.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function Oo(e){return e.toLowerCase()}var Lo=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],Fo=/[^A-Z0-9]+/gi;function Vo(e,t,s){return t instanceof RegExp?e.replace(t,s):t.reduce((function(e,t){return e.replace(t,s)}),e)}function Uo(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var s=t.splitRegexp,o=void 0===s?Lo:s,n=t.stripRegexp,i=void 0===n?Fo:n,r=t.transform,a=void 0===r?Oo:r,l=t.delimiter,c=void 0===l?" ":l,d=Vo(Vo(e,o,"$1\0$2"),i,"\0"),u=0,p=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(p-1);)p--;return d.slice(u,p).split("\0").map(a).join(c)}(e,Mo({delimiter:"."},t))}function zo(e,t){return void 0===t&&(t={}),Uo(e,Mo({delimiter:"-"},t))}const Ho=()=>(0,c.useSelect)((e=>e(d.store).getEntityRecords("postType",M,{per_page:-1})),[]),Go=(e,t)=>{const s=e.toLowerCase(),o=t.map((e=>e.title.rendered.toLowerCase()));if(!o.includes(s))return e;let n=2;for(;o.includes(`${s} ${n}`);)n++;return`${e} ${n}`},$o=e=>zo(e).replace(/[^\w-]+/g,"")||"wp-custom-part";function Wo({modalTitle:e,...t}){const s=(0,c.useSelect)((e=>e(d.store).getPostType(M)?.labels?.add_new_item),[]);return(0,P.jsx)(Do.Modal,{title:e||s,onRequestClose:t.closeModal,overlayClassName:"editor-create-template-part-modal",focusOnMount:"firstContentElement",size:"medium",children:(0,P.jsx)(Zo,{...t})})}function Zo({defaultArea:e=D,blocks:t=[],confirmLabel:s=(0,gs.__)("Add"),closeModal:o,onCreate:n,onError:i,defaultTitle:r=""}){const{createErrorNotice:a}=(0,c.useDispatch)(ms.store),{saveEntityRecord:l}=(0,c.useDispatch)(d.store),h=Ho(),[m,g]=(0,u.useState)(r),[_,f]=(0,u.useState)(e),[b,x]=(0,u.useState)(!1),v=(0,p.useInstanceId)(Wo),w=(0,c.useSelect)((e=>e(qi).__experimentalGetDefaultTemplatePartAreas()),[]);return(0,P.jsx)("form",{onSubmit:async e=>{e.preventDefault(),await async function(){if(m&&!b)try{x(!0);const e=Go(m,h),s=$o(e),o=await l("postType",M,{slug:s,title:e,content:(0,y.serialize)(t),area:_},{throwOnError:!0});await n(o)}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,gs.__)("An error occurred while creating the template part.");a(t,{type:"snackbar"}),i?.()}finally{x(!1)}}()},children:(0,P.jsxs)(Do.__experimentalVStack,{spacing:"4",children:[(0,P.jsx)(Do.TextControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,gs.__)("Name"),value:m,onChange:g,required:!0}),(0,P.jsx)(Do.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,gs.__)("Area"),id:`editor-create-template-part-modal__area-selection-${v}`,className:"editor-create-template-part-modal__area-base-control",children:(0,P.jsx)(Do.__experimentalRadioGroup,{label:(0,gs.__)("Area"),className:"editor-create-template-part-modal__area-radio-group",id:`editor-create-template-part-modal__area-selection-${v}`,onChange:f,checked:_,children:w.map((({icon:e,label:t,area:s,description:o})=>(0,P.jsx)(Do.__experimentalRadio,{value:s,className:"editor-create-template-part-modal__area-radio",children:(0,P.jsxs)(Do.Flex,{align:"start",justify:"start",children:[(0,P.jsx)(Do.FlexItem,{children:(0,P.jsx)(Do.Icon,{icon:e})}),(0,P.jsxs)(Do.FlexBlock,{className:"editor-create-template-part-modal__option-label",children:[t,(0,P.jsx)("div",{children:o})]}),(0,P.jsx)(Do.FlexItem,{className:"editor-create-template-part-modal__checkbox",children:_===s&&(0,P.jsx)(Do.Icon,{icon:Ro})})]})},t)))})}),(0,P.jsxs)(Do.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{o()},children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit","aria-disabled":!m||b,isBusy:b,children:s})]})]})})}function Yo(e){return e.type===R||e.type===M}function Ko(e){return"string"==typeof e.title?(0,Ao.decodeEntities)(e.title):"rendered"in e.title?(0,Ao.decodeEntities)(e.title.rendered):"raw"in e.title?(0,Ao.decodeEntities)(e.title.raw):""}function qo(e){return!!e&&([e.source,e.source].includes(F.custom)&&!Boolean("wp_template"===e.type&&e?.plugin)&&!e.has_theme_file)}const Qo={id:"duplicate-template-part",label:(0,gs._x)("Duplicate","action label"),isEligible:e=>e.type===M,modalHeader:(0,gs._x)("Duplicate template part","action label"),RenderModal:({items:e,closeModal:t})=>{const[s]=e,o=(0,u.useMemo)((()=>{var e;return null!==(e=s.blocks)&&void 0!==e?e:(0,y.parse)("string"==typeof s.content?s.content:s.content.raw,{__unstableSkipMigrationLogs:!0})}),[s.content,s.blocks]),{createSuccessNotice:n}=(0,c.useDispatch)(ms.store);return(0,P.jsx)(Zo,{blocks:o,defaultArea:s.area,defaultTitle:(0,gs.sprintf)((0,gs._x)("%s (Copy)","template part"),Ko(s)),onCreate:function(){n((0,gs.sprintf)((0,gs._x)('"%s" duplicated.',"template part"),Ko(s)),{type:"snackbar",id:"edit-site-patterns-success"}),t?.()},onError:t,confirmLabel:(0,gs._x)("Duplicate","action label"),closeModal:t})}},Xo=Qo,Jo=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M5.5 12h1.75l-2.5 3-2.5-3H4a8 8 0 113.134 6.35l.907-1.194A6.5 6.5 0 105.5 12zm9.53 1.97l-2.28-2.28V8.5a.75.75 0 00-1.5 0V12a.747.747 0 00.218.529l1.282-.84-1.28.842 2.5 2.5a.75.75 0 101.06-1.061z"})}),en=window.wp.privateApis,{lock:tn,unlock:sn}=(0,en.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/editor"),on={id:"reset-post",label:(0,gs.__)("Reset"),isEligible:e=>Yo(e)&&e?.source===F.custom&&(Boolean("wp_template"===e.type&&e?.plugin)||e?.has_theme_file),icon:Jo,supportsBulk:!0,hideModalHeader:!0,RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o,n]=(0,u.useState)(!1),{revertTemplate:i}=sn((0,c.useDispatch)(qi)),{saveEditedEntityRecord:r}=(0,c.useDispatch)(d.store),{createSuccessNotice:a,createErrorNotice:l}=(0,c.useDispatch)(ms.store);return(0,P.jsxs)(Do.__experimentalVStack,{spacing:"5",children:[(0,P.jsx)(Do.__experimentalText,{children:(0,gs.__)("Reset to default and clear all customizations?")}),(0,P.jsxs)(Do.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:t,disabled:o,accessibleWhenDisabled:!0,children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"primary",onClick:async()=>{n(!0),await(async()=>{try{for(const t of e)await i(t,{allowUndo:!1}),await r("postType",t.type,t.id);a(e.length>1?(0,gs.sprintf)((0,gs.__)("%s items reset."),e.length):(0,gs.sprintf)((0,gs.__)('"%s" reset.'),Ko(e[0])),{type:"snackbar",id:"revert-template-action"})}catch(t){let s;s=e[0].type===R?1===e.length?(0,gs.__)("An error occurred while reverting the template."):(0,gs.__)("An error occurred while reverting the templates."):1===e.length?(0,gs.__)("An error occurred while reverting the template part."):(0,gs.__)("An error occurred while reverting the template parts.");const o=t,n=o.message&&"unknown_error"!==o.code?o.message:s;l(n,{type:"snackbar"})}})(),s?.(e),n(!1),t?.()},isBusy:o,disabled:o,accessibleWhenDisabled:!0,children:(0,gs.__)("Reset")})]})]})}},nn=on,rn=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})}),an={id:"move-to-trash",label:(0,gs.__)("Move to trash"),isPrimary:!0,icon:rn,isEligible:e=>!Yo(e)&&"wp_block"!==e.type&&(!!e.status&&!["auto-draft","trash"].includes(e.status)&&e.permissions?.delete),supportsBulk:!0,hideModalHeader:!0,RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o,n]=(0,u.useState)(!1),{createSuccessNotice:i,createErrorNotice:r}=(0,c.useDispatch)(ms.store),{deleteEntityRecord:a}=(0,c.useDispatch)(d.store);return(0,P.jsxs)(Do.__experimentalVStack,{spacing:"5",children:[(0,P.jsx)(Do.__experimentalText,{children:1===e.length?(0,gs.sprintf)((0,gs.__)('Are you sure you want to move "%s" to the trash?'),Ko(e[0])):(0,gs.sprintf)((0,gs._n)("Are you sure you want to move %d item to the trash ?","Are you sure you want to move %d items to the trash ?",e.length),e.length)}),(0,P.jsxs)(Do.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:t,disabled:o,accessibleWhenDisabled:!0,children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"primary",onClick:async()=>{n(!0);const o=await Promise.allSettled(e.map((e=>a("postType",e.type,e.id.toString(),{},{throwOnError:!0}))));if(o.every((({status:e})=>"fulfilled"===e))){let t;t=1===o.length?(0,gs.sprintf)((0,gs.__)('"%s" moved to the trash.'),Ko(e[0])):(0,gs.sprintf)((0,gs._n)("%s item moved to the trash.","%s items moved to the trash.",e.length),e.length),i(t,{type:"snackbar",id:"move-to-trash-action"})}else{let e;if(1===o.length){const t=o[0];e=t.reason?.message?t.reason.message:(0,gs.__)("An error occurred while moving the item to the trash.")}else{const t=new Set,s=o.filter((({status:e})=>"rejected"===e));for(const e of s){const s=e;s.reason?.message&&t.add(s.reason.message)}e=0===t.size?(0,gs.__)("An error occurred while moving the items to the trash."):1===t.size?(0,gs.sprintf)((0,gs.__)("An error occurred while moving the item to the trash: %s"),[...t][0]):(0,gs.sprintf)((0,gs.__)("Some errors occurred while moving the items to the trash: %s"),[...t].join(","))}r(e,{type:"snackbar"})}s&&s(e),n(!1),t?.()},isBusy:o,disabled:o,accessibleWhenDisabled:!0,children:(0,gs._x)("Trash","verb")})]})]})}},ln=an,cn=window.wp.patterns,{PATTERN_TYPES:dn}=sn(cn.privateApis),un={id:"rename-post",label:(0,gs.__)("Rename"),isEligible:e=>"trash"!==e.status&&([R,M,...Object.values(dn)].includes(e.type)?function(e){return e.type===R}(e)?qo(e)&&e.is_custom&&e.permissions?.update:function(e){return e.type===M}(e)?e.source===F.custom&&!e?.has_theme_file&&e.permissions?.update:e.type===dn.user&&e.permissions?.update:e.permissions?.update),RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o]=e,[n,i]=(0,u.useState)((()=>Ko(o))),{editEntityRecord:r,saveEditedEntityRecord:a}=(0,c.useDispatch)(d.store),{createSuccessNotice:l,createErrorNotice:p}=(0,c.useDispatch)(ms.store);return(0,P.jsx)("form",{onSubmit:async function(c){c.preventDefault();try{await r("postType",o.type,o.id,{title:n}),i(""),t?.(),await a("postType",o.type,o.id,{throwOnError:!0}),l((0,gs.__)("Name updated"),{type:"snackbar"}),s?.(e)}catch(e){const t=e,s=t.message&&"unknown_error"!==t.code?t.message:(0,gs.__)("An error occurred while updating the name");p(s,{type:"snackbar"})}},children:(0,P.jsxs)(Do.__experimentalVStack,{spacing:"5",children:[(0,P.jsx)(Do.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,gs.__)("Name"),value:n,onChange:i,required:!0}),(0,P.jsxs)(Do.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{t?.()},children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",children:(0,gs.__)("Save")})]})]})})}},pn=un,hn={id:"restore",label:(0,gs.__)("Restore"),isPrimary:!0,icon:Jo,supportsBulk:!0,isEligible:e=>!Yo(e)&&"wp_block"!==e.type&&"trash"===e.status&&e.permissions?.update,async callback(e,{registry:t,onActionPerformed:s}){const{createSuccessNotice:o,createErrorNotice:n}=t.dispatch(ms.store),{editEntityRecord:i,saveEditedEntityRecord:r}=t.dispatch(d.store);await Promise.allSettled(e.map((e=>i("postType",e.type,e.id,{status:"draft"}))));const a=await Promise.allSettled(e.map((e=>r("postType",e.type,e.id,{throwOnError:!0}))));if(a.every((({status:e})=>"fulfilled"===e))){let t;t=1===e.length?(0,gs.sprintf)((0,gs.__)('"%s" has been restored.'),Ko(e[0])):"page"===e[0].type?(0,gs.sprintf)((0,gs.__)("%d pages have been restored."),e.length):(0,gs.sprintf)((0,gs.__)("%d posts have been restored."),e.length),o(t,{type:"snackbar",id:"restore-post-action"}),s&&s(e)}else{let e;if(1===a.length){const t=a[0];e=t.reason?.message?t.reason.message:(0,gs.__)("An error occurred while restoring the post.")}else{const t=new Set,s=a.filter((({status:e})=>"rejected"===e));for(const e of s){const s=e;s.reason?.message&&t.add(s.reason.message)}e=0===t.size?(0,gs.__)("An error occurred while restoring the posts."):1===t.size?(0,gs.sprintf)((0,gs.__)("An error occurred while restoring the posts: %s"),[...t][0]):(0,gs.sprintf)((0,gs.__)("Some errors occurred while restoring the posts: %s"),[...t].join(","))}n(e,{type:"snackbar"})}}},mn=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),gn={id:"view-post",label:(0,gs._x)("View","verb"),isPrimary:!0,icon:mn,isEligible:e=>"trash"!==e.status,callback(e,{onActionPerformed:t}){const s=e[0];window.open(s?.link,"_blank"),t&&t(e)}},_n={id:"view-post-revisions",context:"list",label(e){var t;const s=null!==(t=e[0]._links?.["version-history"]?.[0]?.count)&&void 0!==t?t:0;return(0,gs.sprintf)((0,gs.__)("View revisions (%s)"),s)},isEligible(e){var t,s;if("trash"===e.status)return!1;const o=null!==(t=e?._links?.["predecessor-version"]?.[0]?.id)&&void 0!==t?t:null,n=null!==(s=e?._links?.["version-history"]?.[0]?.count)&&void 0!==s?s:0;return!!o&&n>1},callback(e,{onActionPerformed:t}){const s=e[0],o=(0,v.addQueryArgs)("revision.php",{revision:s?._links?.["predecessor-version"]?.[0]?.id});document.location.href=o,t&&t(e)}},{lock:fn,unlock:bn}=(0,en.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/fields"),{CreatePatternModalContents:yn,useDuplicatePatternProps:xn}=bn(cn.privateApis),vn={id:"duplicate-pattern",label:(0,gs._x)("Duplicate","action label"),isEligible:e=>"wp_template_part"!==e.type,modalHeader:(0,gs._x)("Duplicate pattern","action label"),RenderModal:({items:e,closeModal:t})=>{const[s]=e,o=xn({pattern:s,onSuccess:()=>t?.()});return(0,P.jsx)(yn,{onClose:t,confirmLabel:(0,gs._x)("Duplicate","action label"),...o})}},wn=vn;const Sn={sort:function(e,t,s){return"asc"===s?e-t:t-e},isValid:function(e,t){if(""===e)return!1;if(!Number.isInteger(Number(e)))return!1;if(t?.elements){const s=t?.elements.map((e=>e.value));if(!s.includes(Number(e)))return!1}return!0},Edit:"integer"};const kn={sort:function(e,t,s){return"asc"===s?e.localeCompare(t):t.localeCompare(e)},isValid:function(e,t){if(t?.elements){const s=t?.elements?.map((e=>e.value));if(!s.includes(e))return!1}return!0},Edit:"text"};const Pn={sort:function(e,t,s){const o=new Date(e).getTime(),n=new Date(t).getTime();return"asc"===s?o-n:n-o},isValid:function(e,t){if(t?.elements){const s=t?.elements.map((e=>e.value));if(!s.includes(e))return!1}return!0},Edit:"datetime"};const Cn={datetime:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){const{id:n,label:i}=t,r=t.getValue({item:e}),a=(0,u.useCallback)((e=>s({[n]:e})),[n,s]);return(0,P.jsxs)("fieldset",{className:"dataviews-controls__datetime",children:[!o&&(0,P.jsx)(Do.BaseControl.VisualLabel,{as:"legend",children:i}),o&&(0,P.jsx)(Do.VisuallyHidden,{as:"legend",children:i}),(0,P.jsx)(Do.TimePicker,{currentTime:r,onChange:a,hideLabelFromVision:!0})]})},integer:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){var n;const{id:i,label:r,description:a}=t,l=null!==(n=t.getValue({item:e}))&&void 0!==n?n:"",c=(0,u.useCallback)((e=>s({[i]:Number(e)})),[i,s]);return(0,P.jsx)(Do.__experimentalNumberControl,{label:r,help:a,value:l,onChange:c,__next40pxDefaultSize:!0,hideLabelFromVision:o})},radio:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){const{id:n,label:i}=t,r=t.getValue({item:e}),a=(0,u.useCallback)((e=>s({[n]:e})),[n,s]);return t.elements?(0,P.jsx)(Do.RadioControl,{label:i,onChange:a,options:t.elements,selected:r,hideLabelFromVision:o}):null},select:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){var n,i;const{id:r,label:a}=t,l=null!==(n=t.getValue({item:e}))&&void 0!==n?n:"",c=(0,u.useCallback)((e=>s({[r]:e})),[r,s]),d=[{label:(0,gs.__)("Select item"),value:""},...null!==(i=t?.elements)&&void 0!==i?i:[]];return(0,P.jsx)(Do.SelectControl,{label:a,value:l,options:d,onChange:c,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:o})},text:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){const{id:n,label:i,placeholder:r}=t,a=t.getValue({item:e}),l=(0,u.useCallback)((e=>s({[n]:e})),[n,s]);return(0,P.jsx)(Do.TextControl,{label:i,placeholder:r,value:null!=a?a:"",onChange:l,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:o})}};function jn(e){if(Object.keys(Cn).includes(e))return Cn[e];throw"Control "+e+" not found"}function En(e){return e.map((e=>{var t,s,o,n;const i="integer"===(r=e.type)?Sn:"text"===r?kn:"datetime"===r?Pn:{sort:(e,t,s)=>"number"==typeof e&&"number"==typeof t?"asc"===s?e-t:t-e:"asc"===s?e.localeCompare(t):t.localeCompare(e),isValid:(e,t)=>{if(t?.elements){const s=t?.elements?.map((e=>e.value));if(!s.includes(e))return!1}return!0},Edit:()=>null};var r;const a=e.getValue||(({item:t})=>t[e.id]),l=null!==(t=e.sort)&&void 0!==t?t:function(e,t,s){return i.sort(a({item:e}),a({item:t}),s)},c=null!==(s=e.isValid)&&void 0!==s?s:function(e,t){return i.isValid(a({item:e}),t)},d=function(e,t){return"function"==typeof e.Edit?e.Edit:"string"==typeof e.Edit?jn(e.Edit):e.elements?jn("select"):"string"==typeof t.Edit?jn(t.Edit):t.Edit}(e,i),u=e.render||(e.elements?({item:t})=>{const s=a({item:t});return e?.elements?.find((e=>e.value===s))?.label||a({item:t})}:a);return{...e,label:e.label||e.id,header:e.header||e.label||e.id,getValue:a,render:u,sort:l,isValid:c,Edit:d,enableHiding:null===(o=e.enableHiding)||void 0===o||o,enableSorting:null===(n=e.enableSorting)||void 0===n||n}}))}function Tn(e,t,s){return En(t.filter((({id:e})=>!!s.fields?.includes(e)))).every((t=>t.isValid(e,{elements:t.elements})))}const Bn=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})});function In({title:e,onClose:t}){return(0,P.jsx)(Do.__experimentalVStack,{className:"dataforms-layouts-panel__dropdown-header",spacing:4,children:(0,P.jsxs)(Do.__experimentalHStack,{alignment:"center",children:[(0,P.jsx)(Do.__experimentalHeading,{level:2,size:13,children:e}),(0,P.jsx)(Do.__experimentalSpacer,{}),t&&(0,P.jsx)(Do.Button,{label:(0,gs.__)("Close"),icon:Bn,onClick:t,size:"small"})]})})}function Nn({data:e,field:t,onChange:s}){const[o,n]=(0,u.useState)(null),i=(0,u.useMemo)((()=>({anchor:o,placement:"left-start",offset:36,shift:!0})),[o]);return(0,P.jsxs)(Do.__experimentalHStack,{ref:n,className:"dataforms-layouts-panel__field",children:[(0,P.jsx)("div",{className:"dataforms-layouts-panel__field-label",children:t.label}),(0,P.jsx)("div",{children:(0,P.jsx)(Do.Dropdown,{contentClassName:"dataforms-layouts-panel__field-dropdown",popoverProps:i,focusOnMount:!0,toggleProps:{size:"compact",variant:"tertiary",tooltipPosition:"middle left"},renderToggle:({isOpen:s,onToggle:o})=>(0,P.jsx)(Do.Button,{className:"dataforms-layouts-panel__field-control",size:"compact",variant:"tertiary","aria-expanded":s,"aria-label":(0,gs.sprintf)((0,gs._x)("Edit %s","field"),t.label),onClick:o,children:(0,P.jsx)(t.render,{item:e})}),renderContent:({onClose:o})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(In,{title:t.label,onClose:o}),(0,P.jsx)(t.Edit,{data:e,field:t,onChange:s,hideLabelFromVision:!0},t.id)]})})})]})}const An=[{type:"regular",component:function({data:e,fields:t,form:s,onChange:o}){const n=(0,u.useMemo)((()=>{var e;return En((null!==(e=s.fields)&&void 0!==e?e:[]).map((e=>t.find((({id:t})=>t===e)))).filter((e=>!!e)))}),[t,s.fields]);return(0,P.jsx)(Do.__experimentalVStack,{spacing:4,children:n.map((t=>(0,P.jsx)(t.Edit,{data:e,field:t,onChange:o},t.id)))})}},{type:"panel",component:function({data:e,fields:t,form:s,onChange:o}){const n=(0,u.useMemo)((()=>{var e;return En((null!==(e=s.fields)&&void 0!==e?e:[]).map((e=>t.find((({id:t})=>t===e)))).filter((e=>!!e)))}),[t,s.fields]);return(0,P.jsx)(Do.__experimentalVStack,{spacing:2,children:n.map((t=>(0,P.jsx)(Nn,{data:e,field:t,onChange:o},t.id)))})}}];function Dn({form:e,...t}){var s;const o=(n=null!==(s=e.type)&&void 0!==s?s:"regular",An.find((e=>e.type===n)));var n;return o?(0,P.jsx)(o.component,{form:e,...t}):null}const Rn=[{type:"integer",id:"menu_order",label:(0,gs.__)("Order"),description:(0,gs.__)("Determines the order of pages.")}],Mn={fields:["menu_order"]};const On={id:"order-pages",label:(0,gs.__)("Order"),isEligible:({status:e})=>"trash"!==e,RenderModal:function({items:e,closeModal:t,onActionPerformed:s}){const[o,n]=(0,u.useState)(e[0]),i=o.menu_order,{editEntityRecord:r,saveEditedEntityRecord:a}=(0,c.useDispatch)(d.store),{createSuccessNotice:l,createErrorNotice:p}=(0,c.useDispatch)(ms.store),h=!Tn(o,Rn,Mn);return(0,P.jsx)("form",{onSubmit:async function(n){if(n.preventDefault(),Tn(o,Rn,Mn))try{await r("postType",o.type,o.id,{menu_order:i}),t?.(),await a("postType",o.type,o.id,{throwOnError:!0}),l((0,gs.__)("Order updated."),{type:"snackbar"}),s?.(e)}catch(e){const t=e,s=t.message&&"unknown_error"!==t.code?t.message:(0,gs.__)("An error occurred while updating the order");p(s,{type:"snackbar"})}},children:(0,P.jsxs)(Do.__experimentalVStack,{spacing:"5",children:[(0,P.jsx)("div",{children:(0,gs.__)("Determines the order of pages. Pages with the same order value are sorted alphabetically. Negative order values are supported.")}),(0,P.jsx)(Dn,{data:o,fields:Rn,form:Mn,onChange:e=>n({...o,...e})}),(0,P.jsxs)(Do.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{t?.()},children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",accessibleWhenDisabled:!0,disabled:h,children:(0,gs.__)("Save")})]})]})})}},Ln=On;"stream"in Blob.prototype||Object.defineProperty(Blob.prototype,"stream",{value(){return new Response(this).body}}),"setBigUint64"in DataView.prototype||Object.defineProperty(DataView.prototype,"setBigUint64",{value(e,t,s){const o=Number(0xffffffffn&t),n=Number(t>>32n);this.setUint32(e+(s?0:4),o,s),this.setUint32(e+(s?4:0),n,s)}});var Fn=e=>new DataView(new ArrayBuffer(e)),Vn=e=>new Uint8Array(e.buffer||e),Un=e=>(new TextEncoder).encode(String(e)),zn=e=>Math.min(4294967295,Number(e)),Hn=e=>Math.min(65535,Number(e));function Gn(e,t){if(void 0===t||t instanceof Date||(t=new Date(t)),e instanceof File)return{isFile:1,t:t||new Date(e.lastModified),i:e.stream()};if(e instanceof Response)return{isFile:1,t:t||new Date(e.headers.get("Last-Modified")||Date.now()),i:e.body};if(void 0===t)t=new Date;else if(isNaN(t))throw new Error("Invalid modification date.");if(void 0===e)return{isFile:0,t};if("string"==typeof e)return{isFile:1,t,i:Un(e)};if(e instanceof Blob)return{isFile:1,t,i:e.stream()};if(e instanceof Uint8Array||e instanceof ReadableStream)return{isFile:1,t,i:e};if(e instanceof ArrayBuffer||ArrayBuffer.isView(e))return{isFile:1,t,i:Vn(e)};if(Symbol.asyncIterator in e)return{isFile:1,t,i:$n(e[Symbol.asyncIterator]())};throw new TypeError("Unsupported input format.")}function $n(e,t=e){return new ReadableStream({async pull(t){let s=0;for(;t.desiredSize>s;){const o=await e.next();if(!o.value){t.close();break}{const e=Wn(o.value);t.enqueue(e),s+=e.byteLength}}},cancel(e){t.throw?.(e)}})}function Wn(e){return"string"==typeof e?Un(e):e instanceof Uint8Array?e:Vn(e)}function Zn(e,t,s){let[o,n]=function(e){return e?e instanceof Uint8Array?[e,1]:ArrayBuffer.isView(e)||e instanceof ArrayBuffer?[Vn(e),1]:[Un(e),0]:[void 0,0]}(t);if(e instanceof File)return{o:Kn(o||Un(e.name)),u:BigInt(e.size),l:n};if(e instanceof Response){const t=e.headers.get("content-disposition"),i=t&&t.match(/;\s*filename\*?\s*=\s*(?:UTF-\d+''|)["']?([^;"'\r\n]*)["']?(?:;|$)/i),r=i&&i[1]||e.url&&new URL(e.url).pathname.split("/").findLast(Boolean),a=r&&decodeURIComponent(r),l=s||+e.headers.get("content-length");return{o:Kn(o||Un(a)),u:BigInt(l),l:n}}return o=Kn(o,void 0!==e||void 0!==s),"string"==typeof e?{o,u:BigInt(Un(e).length),l:n}:e instanceof Blob?{o,u:BigInt(e.size),l:n}:e instanceof ArrayBuffer||ArrayBuffer.isView(e)?{o,u:BigInt(e.byteLength),l:n}:{o,u:Yn(e,s),l:n}}function Yn(e,t){return t>-1?BigInt(t):e?void 0:0n}function Kn(e,t=1){if(!e||e.every((e=>47===e)))throw new Error("The file must have a name.");if(t)for(;47===e[e.length-1];)e=e.subarray(0,-1);else 47!==e[e.length-1]&&(e=new Uint8Array([...e,47]));return e}var qn=new Uint32Array(256);for(let e=0;e<256;++e){let t=e;for(let e=0;e<8;++e)t=t>>>1^(1&t&&3988292384);qn[e]=t}function Qn(e,t=0){t^=-1;for(var s=0,o=e.length;s<o;s++)t=t>>>8^qn[255&t^e[s]];return(-1^t)>>>0}function Xn(e,t,s=0){const o=e.getSeconds()>>1|e.getMinutes()<<5|e.getHours()<<11,n=e.getDate()|e.getMonth()+1<<5|e.getFullYear()-1980<<9;t.setUint16(s,o,1),t.setUint16(s+2,n,1)}function Jn({o:e,l:t},s){return 8*(!t||(s??function(e){try{ei.decode(e)}catch{return 0}return 1}(e)))}var ei=new TextDecoder("utf8",{fatal:1});function ti(e,t=0){const s=Fn(30);return s.setUint32(0,1347093252),s.setUint32(4,754976768|t),Xn(e.t,s,10),s.setUint16(26,e.o.length,1),Vn(s)}async function*si(e){let{i:t}=e;if("then"in t&&(t=await t),t instanceof Uint8Array)yield t,e.m=Qn(t,0),e.u=BigInt(t.length);else{e.u=0n;const s=t.getReader();for(;;){const{value:t,done:o}=await s.read();if(o)break;e.m=Qn(t,e.m),e.u+=BigInt(t.length),yield t}}}function oi(e,t){const s=Fn(16+(t?8:0));return s.setUint32(0,1347094280),s.setUint32(4,e.isFile?e.m:0,1),t?(s.setBigUint64(8,e.u,1),s.setBigUint64(16,e.u,1)):(s.setUint32(8,zn(e.u),1),s.setUint32(12,zn(e.u),1)),Vn(s)}function ni(e,t,s=0,o=0){const n=Fn(46);return n.setUint32(0,1347092738),n.setUint32(4,755182848),n.setUint16(8,2048|s),Xn(e.t,n,12),n.setUint32(16,e.isFile?e.m:0,1),n.setUint32(20,zn(e.u),1),n.setUint32(24,zn(e.u),1),n.setUint16(28,e.o.length,1),n.setUint16(30,o,1),n.setUint16(40,e.isFile?33204:16893,1),n.setUint32(42,zn(t),1),Vn(n)}function ii(e,t,s){const o=Fn(s);return o.setUint16(0,1,1),o.setUint16(2,s-4,1),16&s&&(o.setBigUint64(4,e.u,1),o.setBigUint64(12,e.u,1)),o.setBigUint64(s-8,t,1),Vn(o)}function ri(e){return e instanceof File||e instanceof Response?[[e],[e]]:[[e.input,e.name,e.size],[e.input,e.lastModified]]}function ai(e,t={}){const s={"Content-Type":"application/zip","Content-Disposition":"attachment"};return("bigint"==typeof t.length||Number.isInteger(t.length))&&t.length>0&&(s["Content-Length"]=String(t.length)),t.metadata&&(s["Content-Length"]=String((e=>function(e){let t=BigInt(22),s=0n,o=0;for(const n of e){if(!n.o)throw new Error("Every file must have a non-empty name.");if(void 0===n.u)throw new Error(`Missing size for file "${(new TextDecoder).decode(n.o)}".`);const e=n.u>=0xffffffffn,i=s>=0xffffffffn;s+=BigInt(46+n.o.length+(e&&8))+n.u,t+=BigInt(n.o.length+46+(12*i|28*e)),o||(o=e)}return(o||s>=0xffffffffn)&&(t+=BigInt(76)),t+s}(function*(e){for(const t of e)yield Zn(...ri(t)[0])}(e)))(t.metadata))),new Response(li(e,t),{headers:s})}function li(e,t={}){const s=function(e){const t=e[Symbol.iterator in e?Symbol.iterator:Symbol.asyncIterator]();return{async next(){const e=await t.next();if(e.done)return e;const[s,o]=ri(e.value);return{done:0,value:Object.assign(Gn(...o),Zn(...s))}},throw:t.throw?.bind(t),[Symbol.asyncIterator](){return this}}}(e);return $n(async function*(e,t){const s=[];let o=0n,n=0n,i=0;for await(const r of e){const e=Jn(r,t.buffersAreUTF8);yield ti(r,e),yield new Uint8Array(r.o),r.isFile&&(yield*si(r));const a=r.u>=0xffffffffn,l=12*(o>=0xffffffffn)|28*a;yield oi(r,a),s.push(ni(r,o,e,l)),s.push(r.o),l&&s.push(ii(r,o,l)),a&&(o+=8n),n++,o+=BigInt(46+r.o.length)+r.u,i||(i=a)}let r=0n;for(const e of s)yield e,r+=BigInt(e.length);if(i||o>=0xffffffffn){const e=Fn(76);e.setUint32(0,1347094022),e.setBigUint64(4,BigInt(44),1),e.setUint32(12,755182848),e.setBigUint64(24,n,1),e.setBigUint64(32,n,1),e.setBigUint64(40,r,1),e.setBigUint64(48,o,1),e.setUint32(56,1347094023),e.setBigUint64(64,o+r,1),e.setUint32(72,1,1),yield Vn(e)}const a=Fn(22);a.setUint32(0,1347093766),a.setUint16(8,Hn(n),1),a.setUint16(10,Hn(n),1),a.setUint32(12,zn(r),1),a.setUint32(16,zn(o),1),yield Vn(a)}(s,t),s)}const ci=window.wp.blob,di=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M18 11.3l-1-1.1-4 4V3h-1.5v11.3L7 10.2l-1 1.1 6.2 5.8 5.8-5.8zm.5 3.7v3.5h-13V15H4v5h16v-5h-1.5z"})}),ui="wp_template",pi="wp_template_part";function hi(e){return"string"==typeof e.title?(0,Ao.decodeEntities)(e.title):"rendered"in e.title?(0,Ao.decodeEntities)(e.title.rendered):"raw"in e.title?(0,Ao.decodeEntities)(e.title.raw):""}function mi(e){return JSON.stringify({__file:e.type,title:hi(e),content:"string"==typeof e.content?e.content:e.content?.raw,syncStatus:e.wp_pattern_sync_status},null,2)}const gi={id:"export-pattern",label:(0,gs.__)("Export as JSON"),icon:di,supportsBulk:!0,isEligible:e=>"wp_block"===e.type,callback:async e=>{if(1===e.length)return(0,ci.downloadBlob)(`${zo(hi(e[0])||e[0].slug)}.json`,mi(e[0]),"application/json");const t={},s=e.map((e=>{const s=zo(hi(e)||e.slug);return t[s]=(t[s]||0)+1,{name:s+(t[s]>1?"-"+(t[s]-1):"")+".json",lastModified:new Date,input:mi(e)}}));return(0,ci.downloadBlob)((0,gs.__)("patterns-export")+".zip",await ai(s).blob(),"application/zip")}},_i={id:"permanently-delete",label:(0,gs.__)("Permanently delete"),supportsBulk:!0,icon:rn,isEligible(e){if(function(e){return e.type===ui||e.type===pi}(e)||"wp_block"===e.type)return!1;const{status:t,permissions:s}=e;return"trash"===t&&s?.delete},async callback(e,{registry:t,onActionPerformed:s}){const{createSuccessNotice:o,createErrorNotice:n}=t.dispatch(ms.store),{deleteEntityRecord:i}=t.dispatch(d.store),r=await Promise.allSettled(e.map((e=>i("postType",e.type,e.id,{force:!0},{throwOnError:!0}))));if(r.every((({status:e})=>"fulfilled"===e))){let t;t=1===r.length?(0,gs.sprintf)((0,gs.__)('"%s" permanently deleted.'),hi(e[0])):(0,gs.__)("The items were permanently deleted."),o(t,{type:"snackbar",id:"permanently-delete-post-action"}),s?.(e)}else{let e;if(1===r.length){const t=r[0];e=t.reason?.message?t.reason.message:(0,gs.__)("An error occurred while permanently deleting the item.")}else{const t=new Set,s=r.filter((({status:e})=>"rejected"===e));for(const e of s){const s=e;s.reason?.message&&t.add(s.reason.message)}e=0===t.size?(0,gs.__)("An error occurred while permanently deleting the items."):1===t.size?(0,gs.sprintf)((0,gs.__)("An error occurred while permanently deleting the items: %s"),[...t][0]):(0,gs.sprintf)((0,gs.__)("Some errors occurred while permanently deleting the items: %s"),[...t].join(","))}n(e,{type:"snackbar"})}}},fi=_i,{PATTERN_TYPES:bi}=sn(cn.privateApis),yi={id:"delete-post",label:(0,gs.__)("Delete"),isPrimary:!0,icon:rn,isEligible:e=>Yo(e)?qo(e):e.type===bi.user,supportsBulk:!0,hideModalHeader:!0,RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o,n]=(0,u.useState)(!1),{removeTemplates:i}=sn((0,c.useDispatch)(qi));return(0,P.jsxs)(Do.__experimentalVStack,{spacing:"5",children:[(0,P.jsx)(Do.__experimentalText,{children:e.length>1?(0,gs.sprintf)((0,gs._n)("Delete %d item?","Delete %d items?",e.length),e.length):(0,gs.sprintf)((0,gs._x)('Delete "%s"?',"template part"),Ko(e[0]))}),(0,P.jsxs)(Do.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(Do.Button,{variant:"tertiary",onClick:t,disabled:o,accessibleWhenDisabled:!0,__next40pxDefaultSize:!0,children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.Button,{variant:"primary",onClick:async()=>{n(!0),await i(e,{allowUndo:!1}),s?.(e),n(!1),t?.()},isBusy:o,disabled:o,accessibleWhenDisabled:!0,__next40pxDefaultSize:!0,children:(0,gs.__)("Delete")})]})]})}},xi=yi;function vi(e,t,s){return{type:"REGISTER_ENTITY_ACTION",kind:e,name:t,config:s}}function wi(e,t,s){return{type:"UNREGISTER_ENTITY_ACTION",kind:e,name:t,actionId:s}}function Si(e,t){return{type:"SET_IS_READY",kind:e,name:t}}const ki=e=>async({registry:t})=>{if(sn(t.select(qi)).isEntityReady("postType",e))return;sn(t.dispatch(qi)).setIsReady("postType",e);const s=await t.resolveSelect(d.store).getPostType(e),o=await t.resolveSelect(d.store).canUser("create",{kind:"postType",name:e}),n=await t.resolveSelect(d.store).getCurrentTheme(),i=[s.viewable?gn:void 0,s?.supports?.revisions?_n:void 0,void 0,"wp_template_part"===s.slug&&o&&n?.is_block_theme?Xo:void 0,o&&"wp_block"===s.slug?wn:void 0,s.supports?.title?pn:void 0,s?.supports?.["page-attributes"]?Ln:void 0,"wp_block"===s.slug?gi:void 0,nn,hn,xi,ln,fi];t.batch((()=>{i.forEach((s=>{s&&sn(t.dispatch(qi)).registerEntityAction("postType",e,s)}))})),(0,h.doAction)("core.registerPostTypeActions",e)};function Pi(e){return{type:"SET_CURRENT_TEMPLATE_ID",id:e}}const Ci=e=>async({select:t,dispatch:s,registry:o})=>{const n=await o.dispatch(d.store).saveEntityRecord("postType","wp_template",e);return o.dispatch(d.store).editEntityRecord("postType",t.getCurrentPostType(),t.getCurrentPostId(),{template:n.slug}),o.dispatch(ms.store).createSuccessNotice((0,gs.__)("Custom template created. You're in template mode now."),{type:"snackbar",actions:[{label:(0,gs.__)("Go back"),onClick:()=>s.setRenderingMode(t.getEditorSettings().defaultRenderingMode)}]}),n},ji=e=>({registry:t})=>{var s;const o=(null!==(s=t.select(j.store).get("core","hiddenBlockTypes"))&&void 0!==s?s:[]).filter((t=>!(Array.isArray(e)?e:[e]).includes(t)));t.dispatch(j.store).set("core","hiddenBlockTypes",o)},Ei=e=>({registry:t})=>{var s;const o=null!==(s=t.select(j.store).get("core","hiddenBlockTypes"))&&void 0!==s?s:[],n=new Set([...o,...Array.isArray(e)?e:[e]]);t.dispatch(j.store).set("core","hiddenBlockTypes",[...n])},Ti=({onSave:e,dirtyEntityRecords:t=[],entitiesToSkip:s=[],close:o}={})=>({registry:n})=>{const i=[{kind:"postType",name:"wp_navigation"}],r="site-editor-save-success",a=n.select(d.store).getEntityRecord("root","__unstableBase")?.home;n.dispatch(ms.store).removeNotice(r);const l=t.filter((({kind:e,name:t,key:o,property:n})=>!s.some((s=>s.kind===e&&s.name===t&&s.key===o&&s.property===n))));o?.(l);const c=[],u=[];l.forEach((({kind:e,name:t,key:s,property:o})=>{"root"===e&&"site"===t?c.push(o):(i.some((s=>s.kind===e&&s.name===t))&&n.dispatch(d.store).editEntityRecord(e,t,s,{status:"publish"}),u.push(n.dispatch(d.store).saveEditedEntityRecord(e,t,s)))})),c.length&&u.push(n.dispatch(d.store).__experimentalSaveSpecifiedEntityEdits("root","site",void 0,c)),n.dispatch(m.store).__unstableMarkLastChangeAsPersistent(),Promise.all(u).then((t=>e?e(t):t)).then((e=>{e.some((e=>void 0===e))?n.dispatch(ms.store).createErrorNotice((0,gs.__)("Saving failed.")):n.dispatch(ms.store).createSuccessNotice((0,gs.__)("Site updated."),{type:"snackbar",id:r,actions:[{label:(0,gs.__)("View site"),url:a}]})})).catch((e=>n.dispatch(ms.store).createErrorNotice(`${(0,gs.__)("Saving failed.")} ${e}`)))},Bi=(e,{allowUndo:t=!0}={})=>async({registry:s})=>{const o="edit-site-template-reverted";var n;if(s.dispatch(ms.store).removeNotice(o),(n=e)&&n.source===F.custom&&(Boolean(n?.plugin)||n?.has_theme_file))try{const n=s.select(d.store).getEntityConfig("postType",e.type);if(!n)return void s.dispatch(ms.store).createErrorNotice((0,gs.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const i=(0,v.addQueryArgs)(`${n.baseURL}/${e.id}`,{context:"edit",source:e.origin}),r=await hs()({path:i});if(!r)return void s.dispatch(ms.store).createErrorNotice((0,gs.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const a=({blocks:e=[]})=>(0,y.__unstableSerializeAndClean)(e),l=s.select(d.store).getEditedEntityRecord("postType",e.type,e.id);s.dispatch(d.store).editEntityRecord("postType",e.type,e.id,{content:a,blocks:l.blocks,source:"custom"},{undoIgnore:!0});const c=(0,y.parse)(r?.content?.raw);if(s.dispatch(d.store).editEntityRecord("postType",e.type,r.id,{content:a,blocks:c,source:"theme"}),t){const t=()=>{s.dispatch(d.store).editEntityRecord("postType",e.type,l.id,{content:a,blocks:l.blocks,source:"custom"})};s.dispatch(ms.store).createSuccessNotice((0,gs.__)("Template reset."),{type:"snackbar",id:o,actions:[{label:(0,gs.__)("Undo"),onClick:t}]})}}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,gs.__)("Template revert failed. Please reload.");s.dispatch(ms.store).createErrorNotice(t,{type:"snackbar"})}else s.dispatch(ms.store).createErrorNotice((0,gs.__)("This template is not revertable."),{type:"snackbar"})},Ii=e=>async({registry:t})=>{const s=e.every((e=>e?.has_theme_file)),o=await Promise.allSettled(e.map((e=>t.dispatch(d.store).deleteEntityRecord("postType",e.type,e.id,{force:!0},{throwOnError:!0}))));if(o.every((({status:e})=>"fulfilled"===e))){let o;if(1===e.length){let t;"string"==typeof e[0].title?t=e[0].title:"string"==typeof e[0].title?.rendered?t=e[0].title?.rendered:"string"==typeof e[0].title?.raw&&(t=e[0].title?.raw),o=s?(0,gs.sprintf)((0,gs.__)('"%s" reset.'),(0,Ao.decodeEntities)(t)):(0,gs.sprintf)((0,gs._x)('"%s" deleted.',"template part"),(0,Ao.decodeEntities)(t))}else o=s?(0,gs.__)("Items reset."):(0,gs.__)("Items deleted.");t.dispatch(ms.store).createSuccessNotice(o,{type:"snackbar",id:"editor-template-deleted-success"})}else{let e;if(1===o.length)e=o[0].reason?.message?o[0].reason.message:s?(0,gs.__)("An error occurred while reverting the item."):(0,gs.__)("An error occurred while deleting the item.");else{const t=new Set,n=o.filter((({status:e})=>"rejected"===e));for(const e of n)e.reason?.message&&t.add(e.reason.message);e=0===t.size?(0,gs.__)("An error occurred while deleting the items."):1===t.size?s?(0,gs.sprintf)((0,gs.__)("An error occurred while reverting the items: %s"),[...t][0]):(0,gs.sprintf)((0,gs.__)("An error occurred while deleting the items: %s"),[...t][0]):s?(0,gs.sprintf)((0,gs.__)("Some errors occurred while reverting the items: %s"),[...t].join(",")):(0,gs.sprintf)((0,gs.__)("Some errors occurred while deleting the items: %s"),[...t].join(","))}t.dispatch(ms.store).createErrorNotice(e,{type:"snackbar"})}};var Ni=s(5215),Ai=s.n(Ni);const Di=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.******* 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})}),Ri=(0,P.jsx)(k.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,P.jsx)(k.Path,{d:"M12 4c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14.5c-3.6 0-6.5-2.9-6.5-6.5S8.4 5.5 12 5.5s6.5 2.9 6.5 6.5-2.9 6.5-6.5 6.5zM9 16l4.5-3L15 8.4l-4.5 3L9 16z"})}),Mi=(0,P.jsxs)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,P.jsx)(k.Path,{d:"M15.5 7.5h-7V9h7V7.5Zm-7 3.5h7v1.5h-7V11Zm7 3.5h-7V16h7v-1.5Z"}),(0,P.jsx)(k.Path,{d:"M17 4H7a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM7 5.5h10a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5Z"})]}),Oi=(0,P.jsx)(k.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,P.jsx)(k.Path,{d:"M17.8 2l-.9.3c-.1 0-3.6 1-5.2 2.1C10 5.5 9.3 6.5 8.9 7.1c-.6.9-1.7 4.7-1.7 6.3l-.9 2.3c-.2.4 0 .8.4 1 .1 0 .2.1.3.1.3 0 .6-.2.7-.5l.6-1.5c.3 0 .7-.1 1.2-.2.7-.1 1.4-.3 2.2-.5.8-.2 1.6-.5 2.4-.8.7-.3 1.4-.7 1.9-1.2s.8-1.2 1-1.9c.2-.7.3-1.6.4-2.4.1-.8.1-1.7.2-2.5 0-.8.1-1.5.2-2.1V2zm-1.9 5.6c-.1.8-.2 1.5-.3 2.1-.2.6-.4 1-.6 1.3-.3.3-.8.6-1.4.9-.7.3-1.4.5-2.2.8-.6.2-1.3.3-1.8.4L15 7.5c.3-.3.6-.7 1-1.1 0 .4 0 .8-.1 1.2zM6 20h8v-1.5H6V20z"})}),Li=[];const Fi={rootClientId:void 0,insertionIndex:void 0,filterValue:void 0},Vi=(0,c.createRegistrySelector)((e=>(0,c.createSelector)((t=>{if("object"==typeof t.blockInserterPanel)return t.blockInserterPanel;if("template-locked"===et(t)){const[t]=e(m.store).getBlocksByName("core/post-content");if(t)return{rootClientId:t,insertionIndex:void 0,filterValue:void 0}}return Fi}),(t=>{const[s]=e(m.store).getBlocksByName("core/post-content");return[t.blockInserterPanel,et(t),s]}))));function Ui(e){return e.listViewToggleRef}function zi(e){return e.inserterSidebarToggleRef}const Hi={wp_block:Di,wp_navigation:Ri,page:Mi,post:Oi},Gi=(0,c.createRegistrySelector)((e=>(t,s,o)=>{{if("wp_template_part"===s||"wp_template"===s)return rs(t).find((e=>o.area===e.area))?.icon||C;if(Hi[s])return Hi[s];const n=e(d.store).getPostType(s);return"string"==typeof n?.icon&&n.icon.startsWith("dashicons-")?n.icon.slice(10):Mi}})),$i=(0,c.createRegistrySelector)((e=>(t,s,o)=>{const{type:n,id:i}=te(t),r=e(d.store).getEntityRecordNonTransientEdits("postType",s||n,o||i);if(!r?.meta)return!1;const a=e(d.store).getEntityRecord("postType",s||n,o||i)?.meta;return!Ai()({...a,footnotes:void 0},{...r.meta,footnotes:void 0})}));function Wi(e,...t){return function(e,t,s){var o;return null!==(o=e.actions[t]?.[s])&&void 0!==o?o:Li}(e.dataviews,...t)}function Zi(e,...t){return function(e,t,s){return e.isReady[t]?.[s]}(e.dataviews,...t)}const Yi=(0,c.createRegistrySelector)((e=>(0,c.createSelector)(((t,s)=>{s=Array.isArray(s)?s:[s];const{getBlocksByName:o,getBlockParents:n,getBlockName:i}=e(m.store);return o(s).filter((e=>n(e).every((e=>{const t=i(e);return"core/query"!==t&&!s.includes(t)}))))}),(()=>[e(m.store).getBlocks()])))),Ki={reducer:b,selectors:e,actions:t},qi=(0,c.createReduxStore)("core/editor",{...Ki});(0,c.register)(qi),sn(qi).registerPrivateActions(n),sn(qi).registerPrivateSelectors(i);function Qi(e){const t=e.avatar_urls&&e.avatar_urls[24]?(0,P.jsx)("img",{className:"editor-autocompleters__user-avatar",alt:"",src:e.avatar_urls[24]}):(0,P.jsx)("span",{className:"editor-autocompleters__no-avatar"});return(0,P.jsxs)(P.Fragment,{children:[t,(0,P.jsx)("span",{className:"editor-autocompleters__user-name",children:e.name}),(0,P.jsx)("span",{className:"editor-autocompleters__user-slug",children:e.slug})]})}(0,h.addFilter)("blocks.registerBlockType","core/editor/custom-sources-backwards-compatibility/shim-attribute-source",(function(e){var t;const s=Object.fromEntries(Object.entries(null!==(t=e.attributes)&&void 0!==t?t:{}).filter((([,{source:e}])=>"meta"===e)).map((([e,{meta:t}])=>[e,t])));return Object.entries(s).length&&(e.edit=(e=>(0,p.createHigherOrderComponent)((t=>({attributes:s,setAttributes:o,...n})=>{const i=(0,c.useSelect)((e=>e(qi).getCurrentPostType()),[]),[r,a]=(0,d.useEntityProp)("postType",i,"meta"),l=(0,u.useMemo)((()=>({...s,...Object.fromEntries(Object.entries(e).map((([e,t])=>[e,r[t]])))})),[s,r]);return(0,P.jsx)(t,{attributes:l,setAttributes:t=>{const s=Object.fromEntries(Object.entries(null!=t?t:{}).filter((([t])=>t in e)).map((([t,s])=>[e[t],s])));Object.entries(s).length&&a(s),o(t)},...n})}),"withMetaAttributeSource"))(s)(e.edit)),e}));const Xi={name:"users",className:"editor-autocompleters__user",triggerPrefix:"@",useItems(e){const t=(0,c.useSelect)((t=>{const{getUsers:s}=t(d.store);return s({context:"view",search:encodeURIComponent(e)})}),[e]),s=(0,u.useMemo)((()=>t?t.map((e=>({key:`user-${e.slug}`,value:e,label:Qi(e)}))):[]),[t]);return[s]},getOptionCompletion:e=>`@${e.slug}`};(0,h.addFilter)("editor.Autocomplete.completers","editor/autocompleters/set-default-completers",(function(e=[]){return e.push({...Xi}),e}));const Ji=window.wp.mediaUtils;(0,h.addFilter)("editor.MediaUpload","core/editor/components/media-upload",(()=>Ji.MediaUpload));const{PatternOverridesControls:er,ResetOverridesControl:tr,PatternOverridesBlockControls:sr,PATTERN_TYPES:or,PARTIAL_SYNCING_SUPPORTED_BLOCKS:nr,PATTERN_SYNC_TYPES:ir}=sn(cn.privateApis),rr=(0,p.createHigherOrderComponent)((e=>t=>{const s=!!nr[t.name];return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(e,{...t},"edit"),t.isSelected&&s&&(0,P.jsx)(ar,{...t}),s&&(0,P.jsx)(sr,{})]})}),"withPatternOverrideControls");function ar(e){const t=(0,m.useBlockEditingMode)(),{hasPatternOverridesSource:s,isEditingSyncedPattern:o}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getEditedPostAttribute:s}=e(qi);return{hasPatternOverridesSource:!!(0,y.getBlockBindingsSource)("core/pattern-overrides"),isEditingSyncedPattern:t()===or.user&&s("meta")?.wp_pattern_sync_status!==ir.unsynced&&s("wp_pattern_sync_status")!==ir.unsynced}}),[]),n=e.attributes.metadata?.bindings,i=!!n&&Object.values(n).some((e=>"core/pattern-overrides"===e.source)),r=o&&"default"===t,a=!o&&!!e.attributes.metadata?.name&&"disabled"!==t&&i;return s?(0,P.jsxs)(P.Fragment,{children:[r&&(0,P.jsx)(er,{...e}),a&&(0,P.jsx)(tr,{...e})]}):null}(0,h.addFilter)("editor.BlockEdit","core/editor/with-pattern-override-controls",rr);const lr=window.wp.keyboardShortcuts;function cr(e){var t,s,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(s=cr(e[t]))&&(o&&(o+=" "),o+=s)}else for(s in e)e[s]&&(o&&(o+=" "),o+=s);return o}const dr=function(){for(var e,t,s=0,o="",n=arguments.length;s<n;s++)(e=arguments[s])&&(t=cr(e))&&(o&&(o+=" "),o+=t);return o},ur=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})}),pr=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})}),hr=window.wp.viewport;function mr(e){return["core/edit-post","core/edit-site"].includes(e)?(S()(`${e} interface scope`,{alternative:"core interface scope",hint:"core/edit-post and core/edit-site are merging.",version:"6.6"}),"core"):e}function gr(e,t){return"core"===e&&"edit-site/template"===t?(S()("edit-site/template sidebar",{alternative:"edit-post/document",version:"6.6"}),"edit-post/document"):"core"===e&&"edit-site/block-inspector"===t?(S()("edit-site/block-inspector sidebar",{alternative:"edit-post/block",version:"6.6"}),"edit-post/block"):t}const _r=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e=mr(e),area:t=gr(e,t)}),fr=(e,t)=>({registry:s,dispatch:o})=>{if(!t)return;e=mr(e),t=gr(e,t);s.select(j.store).get(e,"isComplementaryAreaVisible")||s.dispatch(j.store).set(e,"isComplementaryAreaVisible",!0),o({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t})},br=e=>({registry:t})=>{e=mr(e);t.select(j.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(j.store).set(e,"isComplementaryAreaVisible",!1)},yr=(e,t)=>({registry:s})=>{if(!t)return;e=mr(e),t=gr(e,t);const o=s.select(j.store).get(e,"pinnedItems");!0!==o?.[t]&&s.dispatch(j.store).set(e,"pinnedItems",{...o,[t]:!0})},xr=(e,t)=>({registry:s})=>{if(!t)return;e=mr(e),t=gr(e,t);const o=s.select(j.store).get(e,"pinnedItems");s.dispatch(j.store).set(e,"pinnedItems",{...o,[t]:!1})};function vr(e,t){return function({registry:s}){S()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),s.dispatch(j.store).toggle(e,t)}}function wr(e,t,s){return function({registry:o}){S()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),o.dispatch(j.store).set(e,t,!!s)}}function Sr(e,t){return function({registry:s}){S()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),s.dispatch(j.store).setDefaults(e,t)}}function kr(e){return{type:"OPEN_MODAL",name:e}}function Pr(){return{type:"CLOSE_MODAL"}}const Cr=(0,c.createRegistrySelector)((e=>(t,s)=>{s=mr(s);const o=e(j.store).get(s,"isComplementaryAreaVisible");if(void 0!==o)return!1===o?null:t?.complementaryAreas?.[s]})),jr=(0,c.createRegistrySelector)((e=>(t,s)=>{s=mr(s);const o=e(j.store).get(s,"isComplementaryAreaVisible"),n=t?.complementaryAreas?.[s];return o&&void 0===n})),Er=(0,c.createRegistrySelector)((e=>(t,s,o)=>{var n;o=gr(s=mr(s),o);const i=e(j.store).get(s,"pinnedItems");return null===(n=i?.[o])||void 0===n||n})),Tr=(0,c.createRegistrySelector)((e=>(t,s,o)=>(S()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(j.store).get(s,o))));function Br(e,t){return e.activeModal===t}const Ir=(0,c.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:s,area:o}=t;return e[s]?e:{...e,[s]:o}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:s,area:o}=t;return{...e,[s]:o}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),Nr=(0,c.createReduxStore)("core/interface",{reducer:Ir,actions:r,selectors:a});(0,c.register)(Nr);const Ar=window.wp.plugins,Dr=(0,Ar.withPluginContext)(((e,t)=>({icon:t.icon||e.icon,identifier:t.identifier||`${e.name}/${t.name}`})));const Rr=Dr((function({as:e=Do.Button,scope:t,identifier:s,icon:o,selectedIcon:n,name:i,shortcut:r,...a}){const l=e,d=(0,c.useSelect)((e=>e(Nr).getActiveComplementaryArea(t)===s),[s,t]),{enableComplementaryArea:u,disableComplementaryArea:p}=(0,c.useDispatch)(Nr);return(0,P.jsx)(l,{icon:n&&d?n:o,"aria-controls":s.replace("/",":"),"aria-checked":(h=a.role,["checkbox","option","radio","switch","menuitemcheckbox","menuitemradio","treeitem"].includes(h)?d:void 0),onClick:()=>{d?p(t):u(t,s)},shortcut:r,...a});var h})),Mr=({smallScreenTitle:e,children:t,className:s,toggleButtonProps:o})=>{const n=(0,P.jsx)(Rr,{icon:Bn,...o});return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsxs)("div",{className:"components-panel__header interface-complementary-area-header__small",children:[e&&(0,P.jsx)("h2",{className:"interface-complementary-area-header__small-title",children:e}),n]}),(0,P.jsxs)("div",{className:dr("components-panel__header","interface-complementary-area-header",s),tabIndex:-1,children:[t,n]})]})},Or=()=>{};function Lr({name:e,as:t=Do.Button,onClick:s,...o}){return(0,P.jsx)(Do.Fill,{name:e,children:({onClick:e})=>(0,P.jsx)(t,{onClick:s||e?(...t)=>{(s||Or)(...t),(e||Or)(...t)}:void 0,...o})})}Lr.Slot=function({name:e,as:t=Do.ButtonGroup,fillProps:s={},bubblesVirtually:o,...n}){return(0,P.jsx)(Do.Slot,{name:e,bubblesVirtually:o,fillProps:s,children:e=>{if(!u.Children.toArray(e).length)return null;const s=[];u.Children.forEach(e,(({props:{__unstableExplicitMenuItem:e,__unstableTarget:t}})=>{t&&e&&s.push(t)}));const o=u.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&s.includes(e.props.__unstableTarget)?null:e));return(0,P.jsx)(t,{...n,children:o})}})};const Fr=Lr,Vr=({__unstableExplicitMenuItem:e,__unstableTarget:t,...s})=>(0,P.jsx)(Do.MenuItem,{...s});function Ur({scope:e,target:t,__unstableExplicitMenuItem:s,...o}){return(0,P.jsx)(Rr,{as:o=>(0,P.jsx)(Fr,{__unstableExplicitMenuItem:s,__unstableTarget:`${e}/${t}`,as:Vr,name:`${e}/plugin-more-menu`,...o}),role:"menuitemcheckbox",selectedIcon:Ro,name:t,scope:e,...o})}function zr({scope:e,...t}){return(0,P.jsx)(Do.Fill,{name:`PinnedItems/${e}`,...t})}zr.Slot=function({scope:e,className:t,...s}){return(0,P.jsx)(Do.Slot,{name:`PinnedItems/${e}`,...s,children:e=>e?.length>0&&(0,P.jsx)("div",{className:dr(t,"interface-pinned-items"),children:e})})};const Hr=zr,Gr=.3;const $r=280,Wr={open:{width:$r},closed:{width:0},mobileOpen:{width:"100vw"}};function Zr({activeArea:e,isActive:t,scope:s,children:o,className:n,id:i}){const r=(0,p.useReducedMotion)(),a=(0,p.useViewportMatch)("medium","<"),l=(0,p.usePrevious)(e),c=(0,p.usePrevious)(t),[,d]=(0,u.useState)({});(0,u.useEffect)((()=>{d({})}),[t]);const h={type:"tween",duration:r||a||l&&e&&e!==l?0:Gr,ease:[.6,0,.4,1]};return(0,P.jsx)(Do.Fill,{name:`ComplementaryArea/${s}`,children:(0,P.jsx)(Do.__unstableAnimatePresence,{initial:!1,children:(c||t)&&(0,P.jsx)(Do.__unstableMotion.div,{variants:Wr,initial:"closed",animate:a?"mobileOpen":"open",exit:"closed",transition:h,className:"interface-complementary-area__fill",children:(0,P.jsx)("div",{id:i,className:n,style:{width:a?"100vw":$r},children:o})})})})}const Yr=Dr((function({children:e,className:t,closeLabel:s=(0,gs.__)("Close plugin"),identifier:o,header:n,headerClassName:i,icon:r,isPinnable:a=!0,panelClassName:l,scope:d,name:p,smallScreenTitle:h,title:m,toggleShortcut:g,isActiveByDefault:_}){const[f,b]=(0,u.useState)(!1),{isLoading:y,isActive:x,isPinned:v,activeArea:w,isSmall:S,isLarge:k,showIconLabels:C}=(0,c.useSelect)((e=>{const{getActiveComplementaryArea:t,isComplementaryAreaLoading:s,isItemPinned:n}=e(Nr),{get:i}=e(j.store),r=t(d);return{isLoading:s(d),isActive:r===o,isPinned:n(d,o),activeArea:r,isSmall:e(hr.store).isViewportMatch("< medium"),isLarge:e(hr.store).isViewportMatch("large"),showIconLabels:i("core","showIconLabels")}}),[o,d]);!function(e,t,s,o,n){const i=(0,u.useRef)(!1),r=(0,u.useRef)(!1),{enableComplementaryArea:a,disableComplementaryArea:l}=(0,c.useDispatch)(Nr);(0,u.useEffect)((()=>{o&&n&&!i.current?(l(e),r.current=!0):r.current&&!n&&i.current?(r.current=!1,a(e,t)):r.current&&s&&s!==t&&(r.current=!1),n!==i.current&&(i.current=n)}),[o,n,e,t,s,l,a])}(d,o,w,x,S);const{enableComplementaryArea:E,disableComplementaryArea:T,pinItem:B,unpinItem:I}=(0,c.useDispatch)(Nr);if((0,u.useEffect)((()=>{_&&void 0===w&&!S?E(d,o):void 0===w&&S&&T(d,o),b(!0)}),[w,_,d,o,S,E,T]),f)return(0,P.jsxs)(P.Fragment,{children:[a&&(0,P.jsx)(Hr,{scope:d,children:v&&(0,P.jsx)(Rr,{scope:d,identifier:o,isPressed:x&&(!C||k),"aria-expanded":x,"aria-disabled":y,label:m,icon:C?Ro:r,showTooltip:!C,variant:C?"tertiary":void 0,size:"compact",shortcut:g})}),p&&a&&(0,P.jsx)(Ur,{target:p,scope:d,icon:r,children:m}),(0,P.jsxs)(Zr,{activeArea:w,isActive:x,className:dr("interface-complementary-area",t),scope:d,id:o.replace("/",":"),children:[(0,P.jsx)(Mr,{className:i,closeLabel:s,onClose:()=>T(d),smallScreenTitle:h,toggleButtonProps:{label:s,size:"small",shortcut:g,scope:d,identifier:o},children:n||(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)("h2",{className:"interface-complementary-area-header__title",children:m}),a&&(0,P.jsx)(Do.Button,{className:"interface-complementary-area__pin-unpin-item",icon:v?ur:pr,label:v?(0,gs.__)("Unpin from toolbar"):(0,gs.__)("Pin to toolbar"),onClick:()=>(v?I:B)(d,o),isPressed:v,"aria-expanded":v,size:"compact"})]})}),(0,P.jsx)(Do.Panel,{className:l,children:e})]})]})}));Yr.Slot=function({scope:e,...t}){return(0,P.jsx)(Do.Slot,{name:`ComplementaryArea/${e}`,...t})};const Kr=Yr,qr=({isActive:e})=>((0,u.useEffect)((()=>{let e=!1;return document.body.classList.contains("sticky-menu")&&(e=!0,document.body.classList.remove("sticky-menu")),()=>{e&&document.body.classList.add("sticky-menu")}}),[]),(0,u.useEffect)((()=>(e?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode"),()=>{e&&document.body.classList.remove("is-fullscreen-mode")})),[e]),null),Qr=(0,u.forwardRef)((({children:e,className:t,ariaLabel:s,as:o="div",...n},i)=>(0,P.jsx)(o,{ref:i,className:dr("interface-navigable-region",t),"aria-label":s,role:"region",tabIndex:"-1",...n,children:e})));Qr.displayName="NavigableRegion";const Xr=Qr,Jr={type:"tween",duration:.25,ease:[.6,0,.4,1]};const ea={hidden:{opacity:1,marginTop:-60},visible:{opacity:1,marginTop:0},distractionFreeHover:{opacity:1,marginTop:0,transition:{...Jr,delay:.2,delayChildren:.2}},distractionFreeHidden:{opacity:0,marginTop:-60},distractionFreeDisabled:{opacity:0,marginTop:0,transition:{...Jr,delay:.8,delayChildren:.8}}};const ta=(0,u.forwardRef)((function({isDistractionFree:e,footer:t,header:s,editorNotices:o,sidebar:n,secondarySidebar:i,content:r,actions:a,labels:l,className:c},d){const[h,m]=(0,p.useResizeObserver)(),g=(0,p.useViewportMatch)("medium","<"),_={type:"tween",duration:(0,p.useReducedMotion)()?0:.25,ease:[.6,0,.4,1]};!function(e){(0,u.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const f={...{header:(0,gs._x)("Header","header landmark area"),body:(0,gs.__)("Content"),secondarySidebar:(0,gs.__)("Block Library"),sidebar:(0,gs._x)("Settings","settings landmark area"),actions:(0,gs.__)("Publish"),footer:(0,gs.__)("Footer")},...l};return(0,P.jsxs)("div",{ref:d,className:dr(c,"interface-interface-skeleton",!!t&&"has-footer"),children:[(0,P.jsxs)("div",{className:"interface-interface-skeleton__editor",children:[(0,P.jsx)(Do.__unstableAnimatePresence,{initial:!1,children:!!s&&(0,P.jsx)(Xr,{as:Do.__unstableMotion.div,className:"interface-interface-skeleton__header","aria-label":f.header,initial:e&&!g?"distractionFreeHidden":"hidden",whileHover:e&&!g?"distractionFreeHover":"visible",animate:e&&!g?"distractionFreeDisabled":"visible",exit:e&&!g?"distractionFreeHidden":"hidden",variants:ea,transition:_,children:s})}),e&&(0,P.jsx)("div",{className:"interface-interface-skeleton__header",children:o}),(0,P.jsxs)("div",{className:"interface-interface-skeleton__body",children:[(0,P.jsx)(Do.__unstableAnimatePresence,{initial:!1,children:!!i&&(0,P.jsx)(Xr,{className:"interface-interface-skeleton__secondary-sidebar",ariaLabel:f.secondarySidebar,as:Do.__unstableMotion.div,initial:"closed",animate:"open",exit:"closed",variants:{open:{width:m.width},closed:{width:0}},transition:_,children:(0,P.jsxs)(Do.__unstableMotion.div,{style:{position:"absolute",width:g?"100vw":"fit-content",height:"100%",left:0},variants:{open:{x:0},closed:{x:"-100%"}},transition:_,children:[h,i]})})}),(0,P.jsx)(Xr,{className:"interface-interface-skeleton__content",ariaLabel:f.body,children:r}),!!n&&(0,P.jsx)(Xr,{className:"interface-interface-skeleton__sidebar",ariaLabel:f.sidebar,children:n}),!!a&&(0,P.jsx)(Xr,{className:"interface-interface-skeleton__actions",ariaLabel:f.actions,children:a})]})]}),!!t&&(0,P.jsx)(Xr,{className:"interface-interface-skeleton__footer",ariaLabel:f.footer,children:t})]})}));function sa(){const e=(0,c.useSelect)((e=>{const{richEditingEnabled:t,codeEditingEnabled:s}=e(qi).getEditorSettings();return!t||!s}),[]),{getBlockSelectionStart:t}=(0,c.useSelect)(m.store),{getActiveComplementaryArea:s}=(0,c.useSelect)(Nr),{enableComplementaryArea:o,disableComplementaryArea:n}=(0,c.useDispatch)(Nr),{redo:i,undo:r,savePost:a,setIsListViewOpened:l,switchEditorMode:d,toggleDistractionFree:u}=(0,c.useDispatch)(qi),{isEditedPostDirty:p,isPostSavingLocked:h,isListViewOpened:g,getEditorMode:_}=(0,c.useSelect)(qi);return(0,lr.useShortcut)("core/editor/toggle-mode",(()=>{d("visual"===_()?"text":"visual")}),{isDisabled:e}),(0,lr.useShortcut)("core/editor/toggle-distraction-free",(()=>{u()})),(0,lr.useShortcut)("core/editor/undo",(e=>{r(),e.preventDefault()})),(0,lr.useShortcut)("core/editor/redo",(e=>{i(),e.preventDefault()})),(0,lr.useShortcut)("core/editor/save",(e=>{e.preventDefault(),h()||p()&&a()})),(0,lr.useShortcut)("core/editor/toggle-list-view",(e=>{g()||(e.preventDefault(),l(!0))})),(0,lr.useShortcut)("core/editor/toggle-sidebar",(e=>{e.preventDefault();if(["edit-post/document","edit-post/block"].includes(s("core")))n("core");else{const e=t()?"edit-post/block":"edit-post/document";o("core",e)}})),null}class oa extends u.Component{constructor(e){super(e),this.needsAutosave=!(!e.isDirty||!e.isAutosaveable)}componentDidMount(){this.props.disableIntervalChecks||this.setAutosaveTimer()}componentDidUpdate(e){this.props.disableIntervalChecks?this.props.editsReference!==e.editsReference&&this.props.autosave():(this.props.interval!==e.interval&&(clearTimeout(this.timerId),this.setAutosaveTimer()),this.props.isDirty&&(!this.props.isAutosaving||e.isAutosaving)?this.props.editsReference!==e.editsReference&&(this.needsAutosave=!0):this.needsAutosave=!1)}componentWillUnmount(){clearTimeout(this.timerId)}setAutosaveTimer(e=1e3*this.props.interval){this.timerId=setTimeout((()=>{this.autosaveTimerHandler()}),e)}autosaveTimerHandler(){this.props.isAutosaveable?(this.needsAutosave&&(this.needsAutosave=!1,this.props.autosave()),this.setAutosaveTimer()):this.setAutosaveTimer(1e3)}render(){return null}}const na=(0,p.compose)([(0,c.withSelect)(((e,t)=>{const{getReferenceByDistinctEdits:s}=e(d.store),{isEditedPostDirty:o,isEditedPostAutosaveable:n,isAutosavingPost:i,getEditorSettings:r}=e(qi),{interval:a=r().autosaveInterval}=t;return{editsReference:s(),isDirty:o(),isAutosaveable:n(),isAutosaving:i(),interval:a}})),(0,c.withDispatch)(((e,t)=>({autosave(){const{autosave:s=e(qi).autosave}=t;s()}})))])(oa),ia=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M10.8622 8.04053L14.2805 12.0286L10.8622 16.0167L9.72327 15.0405L12.3049 12.0286L9.72327 9.01672L10.8622 8.04053Z"})}),ra=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"m13.1 16-3.4-4 3.4-4 1.1 1-2.6 3 2.6 3-1.1 1z"})}),aa=window.wp.keycodes,la=window.wp.commands,ca=(0,Do.__unstableMotion)(Do.Button);function da(e){const{postType:t,postTypeLabel:s,documentTitle:o,isNotFound:n,isUnsyncedPattern:i,templateTitle:r,onNavigateToPreviousEntityRecord:a}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s,getEditorSettings:o,__experimentalGetTemplateInfo:n}=e(qi),{getEditedEntityRecord:i,getPostType:r,isResolving:a}=e(d.store),l=t(),c=s(),u=i("postType",l,c),p=n(u),h=r(l)?.labels?.singular_name;return{postType:l,postTypeLabel:h,documentTitle:u.title,isNotFound:!u&&!a("getEditedEntityRecord","postType",l,c),isUnsyncedPattern:"unsynced"===u?.wp_pattern_sync_status,templateTitle:p.title,onNavigateToPreviousEntityRecord:o().onNavigateToPreviousEntityRecord}}),[]),{open:l}=(0,c.useDispatch)(la.store),h=(0,p.useReducedMotion)(),g=V.includes(t),_=U.includes(t),f=!!a,b=g?r:o,y=e.title||b,x=e.icon,v=(0,u.useRef)(!1);return(0,u.useEffect)((()=>{v.current=!0}),[]),(0,P.jsxs)("div",{className:dr("editor-document-bar",{"has-back-button":f,"is-global":_&&!i}),children:[(0,P.jsx)(Do.__unstableAnimatePresence,{children:f&&(0,P.jsx)(ca,{className:"editor-document-bar__back",icon:(0,gs.isRTL)()?ia:ra,onClick:e=>{e.stopPropagation(),a()},size:"compact",initial:!!v.current&&{opacity:0,transform:"translateX(15%)"},animate:{opacity:1,transform:"translateX(0%)"},exit:{opacity:0,transform:"translateX(15%)"},transition:h?{duration:0}:void 0,children:(0,gs.__)("Back")})}),n?(0,P.jsx)(Do.__experimentalText,{children:(0,gs.__)("Document not found")}):(0,P.jsxs)(Do.Button,{className:"editor-document-bar__command",onClick:()=>l(),size:"compact",children:[(0,P.jsxs)(Do.__unstableMotion.div,{className:"editor-document-bar__title",initial:!!v.current&&{opacity:0,transform:f?"translateX(15%)":"translateX(-15%)"},animate:{opacity:1,transform:"translateX(0%)"},transition:h?{duration:0}:void 0,children:[x&&(0,P.jsx)(m.BlockIcon,{icon:x}),(0,P.jsxs)(Do.__experimentalText,{size:"body",as:"h1",children:[(0,P.jsx)("span",{className:"editor-document-bar__post-title",children:y?(0,Ao.decodeEntities)(y):(0,gs.__)("No title")}),s&&!e.title&&(0,P.jsx)("span",{className:"editor-document-bar__post-type-label",children:"· "+(0,Ao.decodeEntities)(s)})]})]},f),(0,P.jsx)("span",{className:"editor-document-bar__shortcut",children:aa.displayShortcut.primary("k")})]})]})}const ua=window.wp.richText,pa=({children:e,isValid:t,level:s,href:o,onSelect:n})=>(0,P.jsx)("li",{className:dr("document-outline__item",`is-${s.toLowerCase()}`,{"is-invalid":!t}),children:(0,P.jsxs)("a",{href:o,className:"document-outline__button",onClick:n,children:[(0,P.jsx)("span",{className:"document-outline__emdash","aria-hidden":"true"}),(0,P.jsx)("strong",{className:"document-outline__level",children:s}),(0,P.jsx)("span",{className:"document-outline__item-content",children:e})]})}),ha=(0,P.jsx)("em",{children:(0,gs.__)("(Empty heading)")}),ma=[(0,P.jsx)("br",{},"incorrect-break"),(0,P.jsx)("em",{children:(0,gs.__)("(Incorrect heading level)")},"incorrect-message")],ga=[(0,P.jsx)("br",{},"incorrect-break-h1"),(0,P.jsx)("em",{children:(0,gs.__)("(Your theme may already use a H1 for the post title)")},"incorrect-message-h1")],_a=[(0,P.jsx)("br",{},"incorrect-break-multiple-h1"),(0,P.jsx)("em",{children:(0,gs.__)("(Multiple H1 headings are not recommended)")},"incorrect-message-multiple-h1")];function fa(){return(0,P.jsxs)(Do.SVG,{width:"138",height:"148",viewBox:"0 0 138 148",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,P.jsx)(Do.Rect,{width:"138",height:"148",rx:"4",fill:"#F0F6FC"}),(0,P.jsx)(Do.Line,{x1:"44",y1:"28",x2:"24",y2:"28",stroke:"#DDDDDD"}),(0,P.jsx)(Do.Rect,{x:"48",y:"16",width:"27",height:"23",rx:"4",fill:"#DDDDDD"}),(0,P.jsx)(Do.Path,{d:"M54.7585 32V23.2727H56.6037V26.8736H60.3494V23.2727H62.1903V32H60.3494V28.3949H56.6037V32H54.7585ZM67.4574 23.2727V32H65.6122V25.0241H65.5611L63.5625 26.277V24.6406L65.723 23.2727H67.4574Z",fill:"black"}),(0,P.jsx)(Do.Line,{x1:"55",y1:"59",x2:"24",y2:"59",stroke:"#DDDDDD"}),(0,P.jsx)(Do.Rect,{x:"59",y:"47",width:"29",height:"23",rx:"4",fill:"#DDDDDD"}),(0,P.jsx)(Do.Path,{d:"M65.7585 63V54.2727H67.6037V57.8736H71.3494V54.2727H73.1903V63H71.3494V59.3949H67.6037V63H65.7585ZM74.6605 63V61.6705L77.767 58.794C78.0313 58.5384 78.2528 58.3082 78.4318 58.1037C78.6136 57.8991 78.7514 57.6989 78.8452 57.5028C78.9389 57.304 78.9858 57.0895 78.9858 56.8594C78.9858 56.6037 78.9276 56.3835 78.8111 56.1989C78.6946 56.0114 78.5355 55.8679 78.3338 55.7685C78.1321 55.6662 77.9034 55.6151 77.6477 55.6151C77.3807 55.6151 77.1477 55.669 76.9489 55.777C76.75 55.8849 76.5966 56.0398 76.4886 56.2415C76.3807 56.4432 76.3267 56.6832 76.3267 56.9616H74.5753C74.5753 56.3906 74.7045 55.8949 74.9631 55.4744C75.2216 55.054 75.5838 54.7287 76.0497 54.4986C76.5156 54.2685 77.0526 54.1534 77.6605 54.1534C78.2855 54.1534 78.8295 54.2642 79.2926 54.4858C79.7585 54.7045 80.1207 55.0085 80.3793 55.3977C80.6378 55.7869 80.767 56.233 80.767 56.7358C80.767 57.0653 80.7017 57.3906 80.571 57.7116C80.4432 58.0327 80.2145 58.3892 79.8849 58.7812C79.5554 59.1705 79.0909 59.6378 78.4915 60.1832L77.2173 61.4318V61.4915H80.8821V63H74.6605Z",fill:"black"}),(0,P.jsx)(Do.Line,{x1:"80",y1:"90",x2:"24",y2:"90",stroke:"#DDDDDD"}),(0,P.jsx)(Do.Rect,{x:"84",y:"78",width:"30",height:"23",rx:"4",fill:"#F0B849"}),(0,P.jsx)(Do.Path,{d:"M90.7585 94V85.2727H92.6037V88.8736H96.3494V85.2727H98.1903V94H96.3494V90.3949H92.6037V94H90.7585ZM99.5284 92.4659V91.0128L103.172 85.2727H104.425V87.2841H103.683L101.386 90.919V90.9872H106.564V92.4659H99.5284ZM103.717 94V92.0227L103.751 91.3793V85.2727H105.482V94H103.717Z",fill:"black"}),(0,P.jsx)(Do.Line,{x1:"66",y1:"121",x2:"24",y2:"121",stroke:"#DDDDDD"}),(0,P.jsx)(Do.Rect,{x:"70",y:"109",width:"29",height:"23",rx:"4",fill:"#DDDDDD"}),(0,P.jsx)(Do.Path,{d:"M76.7585 125V116.273H78.6037V119.874H82.3494V116.273H84.1903V125H82.3494V121.395H78.6037V125H76.7585ZM88.8864 125.119C88.25 125.119 87.6832 125.01 87.1861 124.791C86.6918 124.57 86.3011 124.266 86.0142 123.879C85.7301 123.49 85.5838 123.041 85.5753 122.533H87.4332C87.4446 122.746 87.5142 122.933 87.642 123.095C87.7727 123.254 87.946 123.378 88.1619 123.466C88.3778 123.554 88.6207 123.598 88.8906 123.598C89.1719 123.598 89.4205 123.548 89.6364 123.449C89.8523 123.349 90.0213 123.212 90.1435 123.036C90.2656 122.859 90.3267 122.656 90.3267 122.426C90.3267 122.193 90.2614 121.987 90.1307 121.808C90.0028 121.626 89.8182 121.484 89.5767 121.382C89.3381 121.28 89.054 121.229 88.7244 121.229H87.9105V119.874H88.7244C89.0028 119.874 89.2486 119.825 89.4616 119.729C89.6776 119.632 89.8452 119.499 89.9645 119.328C90.0838 119.155 90.1435 118.953 90.1435 118.723C90.1435 118.504 90.0909 118.312 89.9858 118.148C89.8835 117.98 89.7386 117.849 89.5511 117.756C89.3665 117.662 89.1506 117.615 88.9034 117.615C88.6534 117.615 88.4247 117.661 88.2173 117.751C88.0099 117.839 87.8438 117.966 87.7188 118.131C87.5938 118.295 87.527 118.489 87.5185 118.71H85.75C85.7585 118.207 85.902 117.764 86.1804 117.381C86.4588 116.997 86.8338 116.697 87.3054 116.482C87.7798 116.263 88.3153 116.153 88.9119 116.153C89.5142 116.153 90.0412 116.263 90.4929 116.482C90.9446 116.7 91.2955 116.996 91.5455 117.368C91.7983 117.737 91.9233 118.152 91.9205 118.612C91.9233 119.101 91.7713 119.509 91.4645 119.835C91.1605 120.162 90.7642 120.369 90.2756 120.457V120.526C90.9176 120.608 91.4063 120.831 91.7415 121.195C92.0795 121.555 92.2472 122.007 92.2443 122.55C92.2472 123.047 92.1037 123.489 91.8139 123.875C91.527 124.261 91.1307 124.565 90.625 124.787C90.1193 125.009 89.5398 125.119 88.8864 125.119Z",fill:"black"})]})}const ba=(e=[])=>e.flatMap(((e={})=>"core/heading"===e.name?{...e,level:e.attributes.level,isEmpty:ya(e)}:ba(e.innerBlocks))),ya=e=>!e.attributes.content||0===e.attributes.content.trim().length;function xa({onSelect:e,isTitleSupported:t,hasOutlineItemsDisabled:s}){const{selectBlock:o}=(0,c.useDispatch)(m.store),{blocks:n,title:i}=(0,c.useSelect)((e=>{var t;const{getBlocks:s}=e(m.store),{getEditedPostAttribute:o}=e(qi),{getPostType:n}=e(d.store),i=n(o("type"));return{title:o("title"),blocks:s(),isTitleSupported:null!==(t=i?.supports?.title)&&void 0!==t&&t}})),r=ba(n);if(r.length<1)return(0,P.jsxs)("div",{className:"editor-document-outline has-no-headings",children:[(0,P.jsx)(fa,{}),(0,P.jsx)("p",{children:(0,gs.__)("Navigate the structure of your document and address issues like empty or incorrect heading levels.")})]});let a=1;const l=document.querySelector(".editor-post-title__input"),u=t&&i&&l,p=r.reduce(((e,t)=>({...e,[t.level]:(e[t.level]||0)+1})),{})[1]>1;return(0,P.jsx)("div",{className:"document-outline",children:(0,P.jsxs)("ul",{children:[u&&(0,P.jsx)(pa,{level:(0,gs.__)("Title"),isValid:!0,onSelect:e,href:`#${l.id}`,isDisabled:s,children:i}),r.map(((t,n)=>{const i=t.level>a+1,r=!(t.isEmpty||i||!t.level||1===t.level&&(p||u));return a=t.level,(0,P.jsxs)(pa,{level:`H${t.level}`,isValid:r,isDisabled:s,href:`#block-${t.clientId}`,onSelect:()=>{o(t.clientId),e?.()},children:[t.isEmpty?ha:(0,ua.getTextContent)((0,ua.create)({html:t.attributes.content})),i&&ma,1===t.level&&p&&_a,u&&1===t.level&&!p&&ga]},n)}))]})})}function va({children:e}){const t=(0,c.useSelect)((e=>{const{getGlobalBlockCount:t}=e(m.store);return t("core/heading")>0}));return t?null:e}const wa=function(){const{registerShortcut:e}=(0,c.useDispatch)(lr.store);return(0,u.useEffect)((()=>{e({name:"core/editor/toggle-mode",category:"global",description:(0,gs.__)("Switch between visual editor and code editor."),keyCombination:{modifier:"secondary",character:"m"}}),e({name:"core/editor/save",category:"global",description:(0,gs.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/editor/undo",category:"global",description:(0,gs.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/editor/redo",category:"global",description:(0,gs.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,aa.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"core/editor/toggle-list-view",category:"global",description:(0,gs.__)("Open the List View."),keyCombination:{modifier:"access",character:"o"}}),e({name:"core/editor/toggle-distraction-free",category:"global",description:(0,gs.__)("Toggle distraction free mode."),keyCombination:{modifier:"primaryShift",character:"\\"}}),e({name:"core/editor/toggle-sidebar",category:"global",description:(0,gs.__)("Show or hide the Settings sidebar."),keyCombination:{modifier:"primaryShift",character:","}}),e({name:"core/editor/keyboard-shortcuts",category:"main",description:(0,gs.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),e({name:"core/editor/next-region",category:"global",description:(0,gs.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),e({name:"core/editor/previous-region",category:"global",description:(0,gs.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"},{modifier:"ctrlShift",character:"~"}]})}),[e]),(0,P.jsx)(m.BlockEditorKeyboardShortcuts.Register,{})},Sa=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})}),ka=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})});const Pa=(0,u.forwardRef)((function(e,t){const s=(0,aa.isAppleOS)()?aa.displayShortcut.primaryShift("z"):aa.displayShortcut.primary("y"),o=(0,c.useSelect)((e=>e(qi).hasEditorRedo()),[]),{redo:n}=(0,c.useDispatch)(qi);return(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,...e,ref:t,icon:(0,gs.isRTL)()?ka:Sa,label:(0,gs.__)("Redo"),shortcut:s,"aria-disabled":!o,onClick:o?n:void 0,className:"editor-history__redo"})}));const Ca=(0,u.forwardRef)((function(e,t){const s=(0,c.useSelect)((e=>e(qi).hasEditorUndo()),[]),{undo:o}=(0,c.useDispatch)(qi);return(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,...e,ref:t,icon:(0,gs.isRTL)()?Sa:ka,label:(0,gs.__)("Undo"),shortcut:aa.displayShortcut.primary("z"),"aria-disabled":!s,onClick:s?o:void 0,className:"editor-history__undo"})}));function ja(){const[e,t]=(0,u.useState)(!1),s=(0,c.useSelect)((e=>e(m.store).isValidTemplate()),[]),{setTemplateValidity:o,synchronizeTemplate:n}=(0,c.useDispatch)(m.store);return s?null:(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.Notice,{className:"editor-template-validation-notice",isDismissible:!1,status:"warning",actions:[{label:(0,gs.__)("Keep it as is"),onClick:()=>o(!0)},{label:(0,gs.__)("Reset the template"),onClick:()=>t(!0)}],children:(0,gs.__)("The content of your post doesn’t match the template assigned to your post type.")}),(0,P.jsx)(Do.__experimentalConfirmDialog,{isOpen:e,confirmButtonText:(0,gs.__)("Reset"),onConfirm:()=>{t(!1),n()},onCancel:()=>t(!1),size:"medium",children:(0,gs.__)("Resetting the template may result in loss of content, do you want to continue?")})]})}const Ea=function(){const{notices:e}=(0,c.useSelect)((e=>({notices:e(ms.store).getNotices()})),[]),{removeNotice:t}=(0,c.useDispatch)(ms.store),s=e.filter((({isDismissible:e,type:t})=>e&&"default"===t)),o=e.filter((({isDismissible:e,type:t})=>!e&&"default"===t));return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.NoticeList,{notices:o,className:"components-editor-notices__pinned"}),(0,P.jsx)(Do.NoticeList,{notices:s,className:"components-editor-notices__dismissible",onRemove:t,children:(0,P.jsx)(ja,{})})]})},Ta=-3;function Ba(){const e=(0,c.useSelect)((e=>e(ms.store).getNotices()),[]),{removeNotice:t}=(0,c.useDispatch)(ms.store),s=e.filter((({type:e})=>"snackbar"===e)).slice(Ta);return(0,P.jsx)(Do.SnackbarList,{notices:s,className:"components-editor-notices__snackbar",onRemove:t})}function Ia({record:e,checked:t,onChange:s}){const{name:o,kind:n,title:i,key:r}=e,{entityRecordTitle:a,hasPostMetaChanges:l}=(0,c.useSelect)((e=>{if("postType"!==n||"wp_template"!==o)return{entityRecordTitle:i,hasPostMetaChanges:sn(e(qi)).hasPostMetaChanges(o,r)};const t=e(d.store).getEditedEntityRecord(n,o,r);return{entityRecordTitle:e(qi).__experimentalGetTemplateInfo(t).title,hasPostMetaChanges:sn(e(qi)).hasPostMetaChanges(o,r)}}),[o,n,i,r]);return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.PanelRow,{children:(0,P.jsx)(Do.CheckboxControl,{__nextHasNoMarginBottom:!0,label:(0,Ao.decodeEntities)(a)||(0,gs.__)("Untitled"),checked:t,onChange:s})}),l&&(0,P.jsx)("ul",{className:"entities-saved-states__changes",children:(0,P.jsx)("li",{children:(0,gs.__)("Post Meta.")})})]})}const{getGlobalStylesChanges:Na,GlobalStylesContext:Aa}=sn(m.privateApis);function Da({record:e}){const{user:t}=(0,u.useContext)(Aa),s=(0,c.useSelect)((t=>t(d.store).getEntityRecord(e.kind,e.name,e.key)),[e.kind,e.name,e.key]),o=Na(t,s,{maxResults:10});return o.length?(0,P.jsx)("ul",{className:"entities-saved-states__changes",children:o.map((e=>(0,P.jsx)("li",{children:e},e)))}):null}function Ra({record:e,count:t}){if("globalStyles"===e?.name)return null;const s=function(e,t){switch(e){case"site":return 1===t?(0,gs.__)("This change will affect your whole site."):(0,gs.__)("These changes will affect your whole site.");case"wp_template":return(0,gs.__)("This change will affect pages and posts that use this template.");case"page":case"post":return(0,gs.__)("The following has been modified.")}}(e?.name,t);return s?(0,P.jsx)(Do.PanelRow,{children:s}):null}function Ma({list:e,unselectedEntities:t,setUnselectedEntities:s}){const o=e.length,n=e[0];let i=(0,c.useSelect)((e=>e(d.store).getEntityConfig(n.kind,n.name)),[n.kind,n.name]).label;return"wp_template_part"===n?.name&&(i=1===o?(0,gs.__)("Template Part"):(0,gs.__)("Template Parts")),(0,P.jsxs)(Do.PanelBody,{title:i,initialOpen:!0,children:[(0,P.jsx)(Ra,{record:n,count:o}),e.map((e=>(0,P.jsx)(Ia,{record:e,checked:!t.some((t=>t.kind===e.kind&&t.name===e.name&&t.key===e.key&&t.property===e.property)),onChange:t=>s(e,t)},e.key||e.property))),"globalStyles"===n?.name&&(0,P.jsx)(Da,{record:n})]})}const Oa=()=>{const{editedEntities:e,siteEdits:t,siteEntityConfig:s}=(0,c.useSelect)((e=>{const{__experimentalGetDirtyEntityRecords:t,getEntityRecordEdits:s,getEntityConfig:o}=e(d.store);return{editedEntities:t(),siteEdits:s("root","site"),siteEntityConfig:o("root","site")}}),[]),o=(0,u.useMemo)((()=>{var o;const n=e.filter((e=>!("root"===e.kind&&"site"===e.name))),i=null!==(o=s?.meta?.labels)&&void 0!==o?o:{},r=[];for(const e in t)r.push({kind:"root",name:"site",title:i[e]||e,property:e});return[...n,...r]}),[e,t,s]),[n,i]=(0,u.useState)([]);return{dirtyEntityRecords:o,isDirty:o.length-n.length>0,setUnselectedEntities:({kind:e,name:t,key:s,property:o},r)=>{i(r?n.filter((n=>n.kind!==e||n.name!==t||n.key!==s||n.property!==o)):[...n,{kind:e,name:t,key:s,property:o}])},unselectedEntities:n}};function La(e){return e}function Fa({close:e,renderDialog:t}){const s=Oa();return(0,P.jsx)(Va,{close:e,renderDialog:t,...s})}function Va({additionalPrompt:e,close:t,onSave:s=La,saveEnabled:o,saveLabel:n=(0,gs.__)("Save"),renderDialog:i,dirtyEntityRecords:r,isDirty:a,setUnselectedEntities:l,unselectedEntities:d}){const h=(0,u.useRef)(),{saveDirtyEntities:m}=sn((0,c.useDispatch)(qi)),g=r.reduce(((e,t)=>{const{name:s}=t;return e[s]||(e[s]=[]),e[s].push(t),e}),{}),{site:_,wp_template:f,wp_template_part:b,...y}=g,x=[_,f,b,...Object.values(y)].filter(Array.isArray),v=null!=o?o:a,w=(0,u.useCallback)((()=>t()),[t]),[S,k]=(0,p.__experimentalUseDialog)({onClose:()=>w()}),C=(0,p.useInstanceId)(Va,"label"),j=(0,p.useInstanceId)(Va,"description");return(0,P.jsxs)("div",{ref:S,...k,className:"entities-saved-states__panel",role:i?"dialog":void 0,"aria-labelledby":i?C:void 0,"aria-describedby":i?j:void 0,children:[(0,P.jsxs)(Do.Flex,{className:"entities-saved-states__panel-header",gap:2,children:[(0,P.jsx)(Do.FlexItem,{isBlock:!0,as:Do.Button,variant:"secondary",size:"compact",onClick:w,children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.FlexItem,{isBlock:!0,as:Do.Button,ref:h,variant:"primary",size:"compact",disabled:!v,accessibleWhenDisabled:!0,onClick:()=>m({onSave:s,dirtyEntityRecords:r,entitiesToSkip:d,close:t}),className:"editor-entities-saved-states__save-button",children:n})]}),(0,P.jsxs)("div",{className:"entities-saved-states__text-prompt",children:[(0,P.jsxs)("div",{className:"entities-saved-states__text-prompt--header-wrapper",id:i?C:void 0,children:[(0,P.jsx)("strong",{className:"entities-saved-states__text-prompt--header",children:(0,gs.__)("Are you ready to save?")}),e]}),(0,P.jsx)("p",{id:i?j:void 0,children:a?(0,u.createInterpolateElement)((0,gs.sprintf)((0,gs._n)("There is <strong>%d site change</strong> waiting to be saved.","There are <strong>%d site changes</strong> waiting to be saved.",x.length),x.length),{strong:(0,P.jsx)("strong",{})}):(0,gs.__)("Select the items you want to save.")})]}),x.map((e=>(0,P.jsx)(Ma,{list:e,unselectedEntities:d,setUnselectedEntities:l},e[0].name)))]})}function Ua(){try{return(0,c.select)(qi).getEditedPostContent()}catch(e){}}function za({text:e,children:t}){const s=(0,p.useCopyToClipboard)(e);return(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"secondary",ref:s,children:t})}class Ha extends u.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){(0,h.doAction)("editor.ErrorBoundary.errorLogged",e)}static getDerivedStateFromError(e){return{error:e}}render(){const{error:e}=this.state;if(!e)return this.props.children;const t=[(0,P.jsx)(za,{text:Ua,children:(0,gs.__)("Copy Post Text")},"copy-post"),(0,P.jsx)(za,{text:e.stack,children:(0,gs.__)("Copy Error")},"copy-error")];return(0,P.jsx)(m.Warning,{className:"editor-error-boundary",actions:t,children:(0,gs.__)("The editor has encountered an unexpected error.")})}}const Ga=Ha,$a=window.requestIdleCallback?window.requestIdleCallback:window.requestAnimationFrame;let Wa;function Za(){const{postId:e,isEditedPostNew:t,hasRemoteAutosave:s}=(0,c.useSelect)((e=>({postId:e(qi).getCurrentPostId(),isEditedPostNew:e(qi).isEditedPostNew(),hasRemoteAutosave:!!e(qi).getEditorSettings().autosave})),[]),{getEditedPostAttribute:o}=(0,c.useSelect)(qi),{createWarningNotice:n,removeNotice:i}=(0,c.useDispatch)(ms.store),{editPost:r,resetEditorBlocks:a}=(0,c.useDispatch)(qi);(0,u.useEffect)((()=>{let l=function(e,t){return window.sessionStorage.getItem(_s(e,t))}(e,t);if(!l)return;try{l=JSON.parse(l)}catch{return}const{post_title:c,content:d,excerpt:u}=l,p={title:c,content:d,excerpt:u};if(!Object.keys(p).some((e=>p[e]!==o(e))))return void fs(e,t);if(s)return;const h="wpEditorAutosaveRestore";n((0,gs.__)("The backup of this post in your browser is different from the version below."),{id:h,actions:[{label:(0,gs.__)("Restore the backup"),onClick(){const{content:e,...t}=p;r(t),a((0,y.parse)(p.content)),i(h)}}]})}),[t,e])}const Ya=(0,p.ifCondition)((()=>{if(void 0!==Wa)return Wa;try{window.sessionStorage.setItem("__wpEditorTestSessionStorage",""),window.sessionStorage.removeItem("__wpEditorTestSessionStorage"),Wa=!0}catch{Wa=!1}return Wa}))((function(){const{autosave:e}=(0,c.useDispatch)(qi),t=(0,u.useCallback)((()=>{$a((()=>e({local:!0})))}),[]);Za(),function(){const{postId:e,isEditedPostNew:t,isDirty:s,isAutosaving:o,didError:n}=(0,c.useSelect)((e=>({postId:e(qi).getCurrentPostId(),isEditedPostNew:e(qi).isEditedPostNew(),isDirty:e(qi).isEditedPostDirty(),isAutosaving:e(qi).isAutosavingPost(),didError:e(qi).didPostSaveRequestFail()})),[]),i=(0,u.useRef)(s),r=(0,u.useRef)(o);(0,u.useEffect)((()=>{!n&&(r.current&&!o||i.current&&!s)&&fs(e,t),i.current=s,r.current=o}),[s,o,n]);const a=(0,p.usePrevious)(t),l=(0,p.usePrevious)(e);(0,u.useEffect)((()=>{l===e&&a&&!t&&fs(e,!0)}),[t,e])}();const s=(0,c.useSelect)((e=>e(qi).getEditorSettings().localAutosaveInterval),[]);return(0,P.jsx)(na,{interval:s,autosave:t})}));const Ka=function({children:e}){const t=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(qi),{getPostType:s}=e(d.store),o=s(t("type"));return!!o?.supports?.["page-attributes"]}),[]);return t?e:null};const qa=function({children:e,supportKeys:t}){const s=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(qi),{getPostType:s}=e(d.store);return s(t("type"))}),[]);let o=!!s;return s&&(o=(Array.isArray(t)?t:[t]).some((e=>!!s.supports[e]))),o?e:null};function Qa(){const e=(0,c.useSelect)((e=>{var t;return null!==(t=e(qi).getEditedPostAttribute("menu_order"))&&void 0!==t?t:0}),[]),{editPost:t}=(0,c.useDispatch)(qi),[s,o]=(0,u.useState)(null),n=null!=s?s:e;return(0,P.jsx)(Do.Flex,{children:(0,P.jsx)(Do.FlexBlock,{children:(0,P.jsx)(Do.__experimentalNumberControl,{__next40pxDefaultSize:!0,label:(0,gs.__)("Order"),help:(0,gs.__)("Set the page order."),value:n,onChange:e=>{o(e);const s=Number(e);Number.isInteger(s)&&""!==e.trim?.()&&t({menu_order:s})},hideLabelFromVision:!0,onBlur:()=>{o(null)}})})})}function Xa(){return(0,P.jsx)(qa,{supportKeys:"page-attributes",children:(0,P.jsx)(Qa,{})})}var Ja=s(9681),el=s.n(Ja);const tl=(0,u.forwardRef)((({className:e,label:t,children:s},o)=>(0,P.jsxs)(Do.__experimentalHStack,{className:dr("editor-post-panel__row",e),ref:o,children:[t&&(0,P.jsx)("div",{className:"editor-post-panel__row-label",children:t}),(0,P.jsx)("div",{className:"editor-post-panel__row-control",children:s})]})));function sl(e){const t=e.map((e=>({children:[],parent:null,...e})));if(t.some((({parent:e})=>null===e)))return t;const s=t.reduce(((e,t)=>{const{parent:s}=t;return e[s]||(e[s]=[]),e[s].push(t),e}),{}),o=e=>e.map((e=>{const t=s[e.id];return{...e,children:t&&t.length?o(t):[]}}));return o(s[0]||[])}const ol=e=>(0,Ao.decodeEntities)(e),nl=e=>({...e,name:ol(e.name)}),il=e=>(null!=e?e:[]).map(nl);function rl(e){return e?.title?.rendered?(0,Ao.decodeEntities)(e.title.rendered):`#${e.id} (${(0,gs.__)("no title")})`}const al=(e,t)=>{const s=el()(e||"").toLowerCase(),o=el()(t||"").toLowerCase();return s===o?0:s.startsWith(o)?s.length:1/0};function ll(){const{editPost:e}=(0,c.useDispatch)(qi),[t,s]=(0,u.useState)(!1),{isHierarchical:o,parentPostId:n,parentPostTitle:i,pageItems:r}=(0,c.useSelect)((e=>{var s;const{getPostType:o,getEntityRecords:n,getEntityRecord:i}=e(d.store),{getCurrentPostId:r,getEditedPostAttribute:a}=e(qi),l=a("type"),c=a("parent"),u=o(l),p=r(),h=null!==(s=u?.hierarchical)&&void 0!==s&&s,m={per_page:100,exclude:p,parent_exclude:p,orderby:"menu_order",order:"asc",_fields:"id,title,parent"};t&&(m.search=t);const g=c?i("postType",l,c):null;return{isHierarchical:h,parentPostId:c,parentPostTitle:g?rl(g):"",pageItems:h?n("postType",l,m):null}}),[t]),a=(0,u.useMemo)((()=>{const e=(s,o=0)=>{const n=s.map((t=>[{value:t.id,label:"— ".repeat(o)+(0,Ao.decodeEntities)(t.name),rawName:t.name},...e(t.children||[],o+1)])).sort((([e],[s])=>al(e.rawName,t)>=al(s.rawName,t)?1:-1));return n.flat()};if(!r)return[];let s=r.map((e=>({id:e.id,parent:e.parent,name:rl(e)})));t||(s=sl(s));const o=e(s),a=o.find((e=>e.value===n));return i&&!a&&o.unshift({value:n,label:i}),o}),[r,t,i,n]);if(!o)return null;return(0,P.jsx)(Do.ComboboxControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,className:"editor-page-attributes__parent",label:(0,gs.__)("Parent"),help:(0,gs.__)("Choose a parent page."),value:n,options:a,onFilterValueChange:(0,p.debounce)((e=>{s(e)}),300),onChange:t=>{e({parent:t})},hideLabelFromVision:!0})}function cl({isOpen:e,onClick:t}){const s=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(qi),s=t("parent");if(!s)return null;const{getEntityRecord:o}=e(d.store);return o("postType",t("type"),s)}),[]),o=(0,u.useMemo)((()=>s?rl(s):(0,gs.__)("None")),[s]);return(0,P.jsx)(Do.Button,{size:"compact",className:"editor-post-parent__panel-toggle",variant:"tertiary","aria-expanded":e,"aria-label":(0,gs.sprintf)((0,gs.__)("Change parent: %s"),o),onClick:t,children:o})}function dl(){const e=(0,c.useSelect)((e=>e(d.store).getEntityRecord("root","__unstableBase")?.home),[]),[t,s]=(0,u.useState)(null),o=(0,u.useMemo)((()=>({anchor:t,placement:"left-start",offset:36,shift:!0})),[t]);return(0,P.jsx)(tl,{label:(0,gs.__)("Parent"),ref:s,children:(0,P.jsx)(Do.Dropdown,{popoverProps:o,className:"editor-post-parent__panel-dropdown",contentClassName:"editor-post-parent__panel-dialog",focusOnMount:!0,renderToggle:({isOpen:e,onToggle:t})=>(0,P.jsx)(cl,{isOpen:e,onClick:t}),renderContent:({onClose:t})=>(0,P.jsxs)("div",{className:"editor-post-parent",children:[(0,P.jsx)(m.__experimentalInspectorPopoverHeader,{title:(0,gs.__)("Parent"),onClose:t}),(0,P.jsxs)("div",{children:[(0,u.createInterpolateElement)((0,gs.sprintf)((0,gs.__)('Child pages inherit characteristics from their parent, such as URL structure. For instance, if "Pricing" is a child of "Services", its URL would be %s<wbr />/services<wbr />/pricing.'),(0,v.filterURLForDisplay)(e).replace(/([/.])/g,"<wbr />$1")),{wbr:(0,P.jsx)("wbr",{})}),(0,P.jsx)("p",{children:(0,u.createInterpolateElement)((0,gs.__)("They also show up as sub-items in the default navigation menu. <a>Learn more.</a>"),{a:(0,P.jsx)(Do.ExternalLink,{href:(0,gs.__)("https://wordpress.org/documentation/article/page-post-settings-sidebar/#page-attributes")})})})]}),(0,P.jsx)(ll,{})]})})})}const ul=ll,pl="page-attributes";function hl(){const{isEnabled:e,postType:t}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t,isEditorPanelEnabled:s}=e(qi),{getPostType:o}=e(d.store);return{isEnabled:s(pl),postType:o(t("type"))}}),[]);return e&&t?(0,P.jsx)(dl,{}):null}function ml(){return(0,P.jsx)(Ka,{children:(0,P.jsx)(hl,{})})}const gl=(0,P.jsx)(k.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,P.jsx)(k.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5 5.5V8H20V5.5H22.5V4H20V1.5H18.5V4H16V5.5H18.5ZM13.9624 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10.0391H18.5V18C18.5 18.2761 18.2761 18.5 18 18.5H10L10 10.4917L16.4589 10.5139L16.4641 9.01389L5.5 8.97618V6C5.5 5.72386 5.72386 5.5 6 5.5H13.9624V4ZM5.5 10.4762V18C5.5 18.2761 5.72386 18.5 6 18.5H8.5L8.5 10.4865L5.5 10.4762Z"})}),_l=(0,gs.__)("Custom Template");function fl({onClose:e}){const{defaultBlockTemplate:t,onNavigateToEntityRecord:s}=(0,c.useSelect)((e=>{const{getEditorSettings:t,getCurrentTemplateId:s}=e(qi);return{defaultBlockTemplate:t().defaultBlockTemplate,onNavigateToEntityRecord:t().onNavigateToEntityRecord,getTemplateId:s}})),{createTemplate:o}=sn((0,c.useDispatch)(qi)),[n,i]=(0,u.useState)(""),[r,a]=(0,u.useState)(!1),l=()=>{i(""),e()};return(0,P.jsx)(Do.Modal,{title:(0,gs.__)("Create custom template"),onRequestClose:l,focusOnMount:"firstContentElement",size:"small",children:(0,P.jsx)("form",{className:"editor-post-template__create-form",onSubmit:async e=>{if(e.preventDefault(),r)return;a(!0);const i=null!=t?t:(0,y.serialize)([(0,y.createBlock)("core/group",{tagName:"header",layout:{inherit:!0}},[(0,y.createBlock)("core/site-title"),(0,y.createBlock)("core/site-tagline")]),(0,y.createBlock)("core/separator"),(0,y.createBlock)("core/group",{tagName:"main"},[(0,y.createBlock)("core/group",{layout:{inherit:!0}},[(0,y.createBlock)("core/post-title")]),(0,y.createBlock)("core/post-content",{layout:{inherit:!0}})])]),c=await o({slug:(0,v.cleanForSlug)(n||_l),content:i,title:n||_l});a(!1),s({postId:c.id,postType:"wp_template"}),l()},children:(0,P.jsxs)(Do.__experimentalVStack,{spacing:"3",children:[(0,P.jsx)(Do.TextControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,gs.__)("Name"),value:n,onChange:i,placeholder:_l,disabled:r,help:(0,gs.__)('Describe the template, e.g. "Post with sidebar". A custom template can be manually applied to any post or page.')}),(0,P.jsxs)(Do.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:l,children:(0,gs.__)("Cancel")}),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",isBusy:r,"aria-disabled":r,children:(0,gs.__)("Create")})]})]})})})}function bl(){return(0,c.useSelect)((e=>{const{getCurrentPostId:t,getCurrentPostType:s}=e(qi);return{postId:t(),postType:s()}}),[])}function yl(){const{postType:e,postId:t}=bl();return(0,c.useSelect)((s=>{const{canUser:o,getEntityRecord:n,getEntityRecords:i}=s(d.store),r=o("read",{kind:"root",name:"site"})?n("root","site"):void 0,a=i("postType","wp_template",{per_page:-1}),l=+t===r?.page_for_posts,c="page"===e&&+t===r?.page_on_front&&a?.some((({slug:e})=>"front-page"===e));return!l&&!c}),[t,e])}function xl(e){return(0,c.useSelect)((t=>t(d.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:e})),[e])}function vl(e){const t=wl(),s=yl(),o=xl(e);return(0,u.useMemo)((()=>s&&o?.filter((e=>e.is_custom&&e.slug!==t&&!!e.content.raw))),[o,t,s])}function wl(){const{postType:e,postId:t}=bl(),s=xl(e),o=(0,c.useSelect)((s=>{const o=s(d.store).getEditedEntityRecord("postType",e,t);return o?.template}),[e,t]);if(o)return s?.find((e=>e.slug===o))?.slug}const Sl={className:"editor-post-template__dropdown",placement:"bottom-start"};function kl({isOpen:e,onClick:t}){const s=(0,c.useSelect)((e=>{const t=e(qi).getEditedPostAttribute("template"),{supportsTemplateMode:s,availableTemplates:o}=e(qi).getEditorSettings();if(!s&&o[t])return o[t];const n=e(d.store).canUser("create",{kind:"postType",name:"wp_template"})&&e(qi).getCurrentTemplateId();return n?.title||n?.slug||o?.[t]}),[]);return(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary","aria-expanded":e,"aria-label":(0,gs.__)("Template options"),onClick:t,children:null!=s?s:(0,gs.__)("Default template")})}function Pl({onClose:e}){var t,s;const o=yl(),{availableTemplates:n,fetchedTemplates:i,selectedTemplateSlug:r,canCreate:a,canEdit:l,currentTemplateId:p,onNavigateToEntityRecord:h,getEditorSettings:g}=(0,c.useSelect)((e=>{const{canUser:t,getEntityRecords:s}=e(d.store),n=e(qi).getEditorSettings(),i=t("create",{kind:"postType",name:"wp_template"}),r=e(qi).getCurrentTemplateId();return{availableTemplates:n.availableTemplates,fetchedTemplates:i?s("postType","wp_template",{post_type:e(qi).getCurrentPostType(),per_page:-1}):void 0,selectedTemplateSlug:e(qi).getEditedPostAttribute("template"),canCreate:o&&i&&n.supportsTemplateMode,canEdit:o&&i&&n.supportsTemplateMode&&!!r,currentTemplateId:r,onNavigateToEntityRecord:n.onNavigateToEntityRecord,getEditorSettings:e(qi).getEditorSettings}}),[o]),_=(0,u.useMemo)((()=>Object.entries({...n,...Object.fromEntries((null!=i?i:[]).map((({slug:e,title:t})=>[e,t.rendered])))}).map((([e,t])=>({value:e,label:t})))),[n,i]),f=null!==(t=_.find((e=>e.value===r)))&&void 0!==t?t:_.find((e=>!e.value)),{editPost:b}=(0,c.useDispatch)(qi),{createSuccessNotice:y}=(0,c.useDispatch)(ms.store),[x,v]=(0,u.useState)(!1);return(0,P.jsxs)("div",{className:"editor-post-template__classic-theme-dropdown",children:[(0,P.jsx)(m.__experimentalInspectorPopoverHeader,{title:(0,gs.__)("Template"),help:(0,gs.__)("Templates define the way content is displayed when viewing your site."),actions:a?[{icon:gl,label:(0,gs.__)("Add template"),onClick:()=>v(!0)}]:[],onClose:e}),o?(0,P.jsx)(Do.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:!0,label:(0,gs.__)("Template"),value:null!==(s=f?.value)&&void 0!==s?s:"",options:_,onChange:e=>b({template:e||""})}):(0,P.jsx)(Do.Notice,{status:"warning",isDismissible:!1,children:(0,gs.__)("The posts page template cannot be changed.")}),l&&h&&(0,P.jsx)("p",{children:(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!1,variant:"link",onClick:()=>{h({postId:p,postType:"wp_template"}),e(),y((0,gs.__)("Editing template. Changes made here affect all posts and pages that use the template."),{type:"snackbar",actions:[{label:(0,gs.__)("Go back"),onClick:()=>g().onNavigateToPreviousEntityRecord()}]})},children:(0,gs.__)("Edit template")})}),x&&(0,P.jsx)(fl,{onClose:()=>v(!1)})]})}const Cl=function(){return(0,P.jsx)(Do.Dropdown,{popoverProps:Sl,focusOnMount:!0,renderToggle:({isOpen:e,onToggle:t})=>(0,P.jsx)(kl,{isOpen:e,onClick:t}),renderContent:({onClose:e})=>(0,P.jsx)(Pl,{onClose:e})})},{PreferenceBaseOption:jl}=(window.wp.warning,sn(j.privateApis)),El=(0,p.compose)((0,c.withSelect)(((e,{panelName:t})=>{const{isEditorPanelEnabled:s,isEditorPanelRemoved:o}=e(qi);return{isRemoved:o(t),isChecked:s(t)}})),(0,p.ifCondition)((({isRemoved:e})=>!e)),(0,c.withDispatch)(((e,{panelName:t})=>({onChange:()=>e(qi).toggleEditorPanelEnabled(t)}))))(jl),{Fill:Tl,Slot:Bl}=(0,Do.createSlotFill)("EnablePluginDocumentSettingPanelOption"),Il=({label:e,panelName:t})=>(0,P.jsx)(Tl,{children:(0,P.jsx)(El,{label:e,panelName:t})});Il.Slot=Bl;const Nl=Il,{Fill:Al,Slot:Dl}=(0,Do.createSlotFill)("PluginDocumentSettingPanel"),Rl=({name:e,className:t,title:s,icon:o,children:n})=>{const{name:i}=(0,Ar.usePluginContext)(),r=`${i}/${e}`,{opened:a,isEnabled:l}=(0,c.useSelect)((e=>{const{isEditorPanelOpened:t,isEditorPanelEnabled:s}=e(qi);return{opened:t(r),isEnabled:s(r)}}),[r]),{toggleEditorPanelOpened:d}=(0,c.useDispatch)(qi);return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Nl,{label:s,panelName:r}),(0,P.jsx)(Al,{children:l&&(0,P.jsx)(Do.PanelBody,{className:t,title:s,icon:o,opened:a,onToggle:()=>d(r),children:n})})]})};Rl.Slot=Dl;const Ml=Rl,Ol=({allowedBlocks:e,icon:t,label:s,onClick:o,small:n,role:i})=>(0,P.jsx)(m.BlockSettingsMenuControls,{children:({selectedBlocks:r,onClose:a})=>((e,t)=>{return!Array.isArray(t)||(s=t,0===e.filter((e=>!s.includes(e))).length);var s})(r,e)?(0,P.jsx)(Do.MenuItem,{onClick:(0,p.compose)(o,a),icon:t,label:n?s:void 0,role:i,children:!n&&s}):null}),Ll=(0,p.compose)((0,Ar.withPluginContext)(((e,t)=>{var s;return{as:null!==(s=t.as)&&void 0!==s?s:Do.MenuItem,icon:t.icon||e.icon,name:"core/plugin-more-menu"}})))(Fr),{Fill:Fl,Slot:Vl}=(0,Do.createSlotFill)("PluginPostPublishPanel"),Ul=({children:e,className:t,title:s,initialOpen:o=!1,icon:n})=>{const{icon:i}=(0,Ar.usePluginContext)();return(0,P.jsx)(Fl,{children:(0,P.jsx)(Do.PanelBody,{className:t,initialOpen:o||!s,title:s,icon:null!=n?n:i,children:e})})};Ul.Slot=Vl;const zl=Ul,{Fill:Hl,Slot:Gl}=(0,Do.createSlotFill)("PluginPostStatusInfo"),$l=({children:e,className:t})=>(0,P.jsx)(Hl,{children:(0,P.jsx)(Do.PanelRow,{className:t,children:e})});$l.Slot=Gl;const Wl=$l,{Fill:Zl,Slot:Yl}=(0,Do.createSlotFill)("PluginPrePublishPanel"),Kl=({children:e,className:t,title:s,initialOpen:o=!1,icon:n})=>{const{icon:i}=(0,Ar.usePluginContext)();return(0,P.jsx)(Zl,{children:(0,P.jsx)(Do.PanelBody,{className:t,initialOpen:o||!s,title:s,icon:null!=n?n:i,children:e})})};Kl.Slot=Yl;const ql=Kl,Ql=(0,p.compose)((0,Ar.withPluginContext)(((e,t)=>{var s;return{as:null!==(s=t.as)&&void 0!==s?s:Do.MenuItem,icon:t.icon||e.icon,name:"core/plugin-preview-menu"}})))(Fr);function Xl({className:e,...t}){const{postTitle:s}=(0,c.useSelect)((e=>({postTitle:e(qi).getEditedPostAttribute("title")})),[]);return(0,P.jsx)(Kr,{panelClassName:e,className:"editor-sidebar",smallScreenTitle:s||(0,gs.__)("(no title)"),scope:"core",...t})}function Jl(e){return(0,P.jsx)(Ur,{__unstableExplicitMenuItem:!0,scope:"core",...e})}function ec({onClick:e}){const[t,s]=(0,u.useState)(!1),{postType:o,postId:n}=bl(),i=vl(o),{editEntityRecord:r}=(0,c.useDispatch)(d.store);if(!i?.length)return null;return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.MenuItem,{onClick:()=>s(!0),children:(0,gs.__)("Swap template")}),t&&(0,P.jsx)(Do.Modal,{title:(0,gs.__)("Choose a template"),onRequestClose:()=>s(!1),overlayClassName:"editor-post-template__swap-template-modal",isFullScreen:!0,children:(0,P.jsx)("div",{className:"editor-post-template__swap-template-modal-content",children:(0,P.jsx)(tc,{postType:o,onSelect:async t=>{r("postType",o,n,{template:t.name},{undoIgnore:!0}),s(!1),e()}})})})]})}function tc({postType:e,onSelect:t}){const s=vl(e),o=(0,u.useMemo)((()=>s.map((e=>({name:e.slug,blocks:(0,y.parse)(e.content.raw),title:(0,Ao.decodeEntities)(e.title.rendered),id:e.id})))),[s]),n=(0,p.useAsyncList)(o);return(0,P.jsx)(m.__experimentalBlockPatternsList,{label:(0,gs.__)("Templates"),blockPatterns:o,shownPatterns:n,onClickPattern:t})}function sc({onClick:e}){const t=wl(),s=yl(),{postType:o,postId:n}=bl(),{editEntityRecord:i}=(0,c.useDispatch)(d.store);return t&&s?(0,P.jsx)(Do.MenuItem,{onClick:()=>{i("postType",o,n,{template:""},{undoIgnore:!0}),e()},children:(0,gs.__)("Use default template")}):null}function oc({onClick:e}){const{canCreateTemplates:t}=(0,c.useSelect)((e=>{const{canUser:t}=e(d.store);return{canCreateTemplates:t("create",{kind:"postType",name:"wp_template"})}}),[]),[s,o]=(0,u.useState)(!1),n=yl();return t&&n?(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.MenuItem,{onClick:()=>{o(!0)},children:(0,gs.__)("Create new template")}),s&&(0,P.jsx)(fl,{onClose:()=>{o(!1),e()}})]}):null}const nc={className:"editor-post-template__dropdown",placement:"bottom-start"};function ic({id:e}){const{isTemplateHidden:t,onNavigateToEntityRecord:s,getEditorSettings:o,hasGoBack:n}=(0,c.useSelect)((e=>{const{getRenderingMode:t,getEditorSettings:s}=sn(e(qi)),o=s();return{isTemplateHidden:"post-only"===t(),onNavigateToEntityRecord:o.onNavigateToEntityRecord,getEditorSettings:s,hasGoBack:o.hasOwnProperty("onNavigateToPreviousEntityRecord")}}),[]),{get:i}=(0,c.useSelect)(j.store),{editedRecord:r,hasResolved:a}=(0,d.useEntityRecord)("postType","wp_template",e),{createSuccessNotice:l}=(0,c.useDispatch)(ms.store),{setRenderingMode:u}=(0,c.useDispatch)(qi),p=(0,c.useSelect)((e=>!!e(d.store).canUser("create",{kind:"postType",name:"wp_template"})),[]);if(!a)return null;const h=n?[{label:(0,gs.__)("Go back"),onClick:()=>o().onNavigateToPreviousEntityRecord()}]:void 0;return(0,P.jsx)(Do.DropdownMenu,{popoverProps:nc,focusOnMount:!0,toggleProps:{size:"compact",variant:"tertiary",tooltipPosition:"middle left"},label:(0,gs.__)("Template options"),text:(0,Ao.decodeEntities)(r.title),icon:null,children:({onClose:e})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsxs)(Do.MenuGroup,{children:[p&&(0,P.jsx)(Do.MenuItem,{onClick:()=>{s({postId:r.id,postType:"wp_template"}),e(),i("core/edit-site","welcomeGuideTemplate")||l((0,gs.__)("Editing template. Changes made here affect all posts and pages that use the template."),{type:"snackbar",actions:h})},children:(0,gs.__)("Edit template")}),(0,P.jsx)(ec,{onClick:e}),(0,P.jsx)(sc,{onClick:e}),p&&(0,P.jsx)(oc,{onClick:e})]}),(0,P.jsx)(Do.MenuGroup,{children:(0,P.jsx)(Do.MenuItem,{icon:t?void 0:Ro,isSelected:!t,role:"menuitemcheckbox",onClick:()=>{u(t?"template-locked":"post-only")},children:(0,gs.__)("Show template")})})]})})}function rc(){const{templateId:e,isBlockTheme:t}=(0,c.useSelect)((e=>{const{getCurrentTemplateId:t,getEditorSettings:s}=e(qi);return{templateId:t(),isBlockTheme:s().__unstableIsBlockBasedTheme}}),[]),s=(0,c.useSelect)((e=>{var t;const s=e(qi).getCurrentPostType(),o=e(d.store).getPostType(s);if(!o?.viewable)return!1;const n=e(qi).getEditorSettings();if(!!n.availableTemplates&&Object.keys(n.availableTemplates).length>0)return!0;if(!n.supportsTemplateMode)return!1;return null!==(t=e(d.store).canUser("create",{kind:"postType",name:"wp_template"}))&&void 0!==t&&t}),[]),o=(0,c.useSelect)((e=>{var t;return null!==(t=e(d.store).canUser("read",{kind:"postType",name:"wp_template"}))&&void 0!==t&&t}),[]);return t&&o||!s?t&&e?(0,P.jsx)(tl,{label:(0,gs.__)("Template"),children:(0,P.jsx)(ic,{id:e})}):null:(0,P.jsx)(tl,{label:(0,gs.__)("Template"),children:(0,P.jsx)(Cl,{})})}const ac={_fields:"id,name",context:"view"},lc={who:"authors",per_page:50,...ac};function cc(e){const{authorId:t,authors:s,postAuthor:o}=(0,c.useSelect)((t=>{const{getUser:s,getUsers:o}=t(d.store),{getEditedPostAttribute:n}=t(qi),i=n("author"),r={...lc};return e&&(r.search=e),{authorId:i,authors:o(r),postAuthor:s(i,ac)}}),[e]);return{authorId:t,authorOptions:(0,u.useMemo)((()=>{const e=(null!=s?s:[]).map((e=>({value:e.id,label:(0,Ao.decodeEntities)(e.name)}))),t=e.findIndex((({value:e})=>o?.id===e));let n=[];return t<0&&o?n=[{value:o.id,label:(0,Ao.decodeEntities)(o.name)}]:t<0&&!o&&(n=[{value:0,label:(0,gs.__)("(No author)")}]),[...n,...e]}),[s,o]),postAuthor:o}}function dc(){const[e,t]=(0,u.useState)(),{editPost:s}=(0,c.useDispatch)(qi),{authorId:o,authorOptions:n}=cc(e);return(0,P.jsx)(Do.ComboboxControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,gs.__)("Author"),options:n,value:o,onFilterValueChange:(0,p.debounce)((e=>{t(e)}),300),onChange:e=>{e&&s({author:e})},allowReset:!1,hideLabelFromVision:!0})}function uc(){const{editPost:e}=(0,c.useDispatch)(qi),{authorId:t,authorOptions:s}=cc();return(0,P.jsx)(Do.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,className:"post-author-selector",label:(0,gs.__)("Author"),options:s,onChange:t=>{const s=Number(t);e({author:s})},value:t,hideLabelFromVision:!0})}const pc=function(){return(0,c.useSelect)((e=>{const t=e(d.store).getUsers(lc);return t?.length>=25}),[])?(0,P.jsx)(dc,{}):(0,P.jsx)(uc,{})};function hc({children:e}){const{hasAssignAuthorAction:t,hasAuthors: <AUTHORS>
/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
function Rp(e){return"[object Object]"===Object.prototype.toString.call(e)}function Mp(e){var t,s;return!1!==Rp(e)&&(void 0===(t=e.constructor)||!1!==Rp(s=t.prototype)&&!1!==s.hasOwnProperty("isPrototypeOf"))}const{GlobalStylesContext:Op,cleanEmptyObject:Lp}=sn(m.privateApis);function Fp(e,t){return Dp()(e,t,{isMergeableObject:Mp,customMerge:e=>{if("backgroundImage"===e)return(e,t)=>t}})}function Vp(){const[e,t,s]=function(){const{globalStylesId:e,isReady:t,settings:s,styles:o,_links:n}=(0,c.useSelect)((e=>{const{getEntityRecord:t,getEditedEntityRecord:s,hasFinishedResolution:o,canUser:n}=e(d.store),i=e(d.store).__experimentalGetCurrentGlobalStylesId();let r;const a=i?n("update",{kind:"root",name:"globalStyles",id:i}):null;i&&"boolean"==typeof a&&(r=a?s("root","globalStyles",i):t("root","globalStyles",i,{context:"view"}));let l=!1;return o("__experimentalGetCurrentGlobalStylesId")&&(l=!i||(a?o("getEditedEntityRecord",["root","globalStyles",i]):o("getEntityRecord",["root","globalStyles",i,{context:"view"}]))),{globalStylesId:i,isReady:l,settings:r?.settings,styles:r?.styles,_links:r?._links}}),[]),{getEditedEntityRecord:i}=(0,c.useSelect)(d.store),{editEntityRecord:r}=(0,c.useDispatch)(d.store);return[t,(0,u.useMemo)((()=>({settings:null!=s?s:{},styles:null!=o?o:{},_links:null!=n?n:{}})),[s,o,n]),(0,u.useCallback)(((t,s={})=>{var o,n,a;const l=i("root","globalStyles",e),c={styles:null!==(o=l?.styles)&&void 0!==o?o:{},settings:null!==(n=l?.settings)&&void 0!==n?n:{},_links:null!==(a=l?._links)&&void 0!==a?a:{}},d="function"==typeof t?t(c):t;r("root","globalStyles",e,{styles:Lp(d.styles)||{},settings:Lp(d.settings)||{},_links:Lp(d._links)||{}},s)}),[e,r,i])]}(),[o,n]=function(){const e=(0,c.useSelect)((e=>e(d.store).__experimentalGetCurrentThemeBaseGlobalStyles()),[]);return[!!e,e]}(),i=(0,u.useMemo)((()=>n&&t?Fp(n,t):{}),[t,n]);return(0,u.useMemo)((()=>({isReady:e&&o,user:t,base:n,merged:i,setUserConfig:s})),[i,t,n,s,e,o])}const Up={};function zp(e){const{RECEIVE_INTERMEDIATE_RESULTS:t}=sn(d.privateApis),{getEntityRecords:s}=e(d.store);return s("postType","wp_block",{per_page:-1,[t]:!0})}const Hp=["__experimentalBlockDirectory","__experimentalDiscussionSettings","__experimentalFeatures","__experimentalGlobalStylesBaseStyles","alignWide","blockInspectorTabs","allowedMimeTypes","bodyPlaceholder","canLockBlocks","canUpdateBlockBindings","capabilities","clearBlockSelection","codeEditingEnabled","colors","disableCustomColors","disableCustomFontSizes","disableCustomSpacingSizes","disableCustomGradients","disableLayoutStyles","enableCustomLineHeight","enableCustomSpacing","enableCustomUnits","enableOpenverseMediaCategory","fontSizes","gradients","generateAnchors","onNavigateToEntityRecord","imageDefaultSize","imageDimensions","imageEditing","imageSizes","isRTL","locale","maxWidth","postContentAttributes","postsPerPage","readOnly","styles","titlePlaceholder","supportsLayout","widgetTypesToHideFromLegacyWidgetBlock","__unstableHasCustomAppender","__unstableIsPreviewMode","__unstableResolvedAssets","__unstableIsBlockBasedTheme"],{globalStylesDataKey:Gp,globalStylesLinksDataKey:$p,selectBlockPatternsKey:Wp,reusableBlocksSelectKey:Zp,sectionRootClientIdKey:Yp}=sn(m.privateApis);const Kp=function(e,t,s,o){var n,i,r,a;const l=(0,p.useViewportMatch)("medium"),{allowRightClickOverrides:h,blockTypes:g,focusMode:_,hasFixedToolbar:f,isDistractionFree:b,keepCaretInsideBlock:x,hasUploadPermissions:v,hiddenBlockTypes:w,canUseUnfilteredHTML:S,userCanCreatePages:k,pageOnFront:P,pageForPosts:C,userPatternCategories:E,restBlockPatternCategories:T,sectionRootClientId:B}=(0,c.useSelect)((e=>{var n;const{canUser:i,getRawEntityRecord:r,getEntityRecord:a,getUserPatternCategories:c,getBlockPatternCategories:u}=e(d.store),{get:p}=e(j.store),{getBlockTypes:h}=e(y.store),{getBlocksByName:g,getBlockAttributes:_}=e(m.store),f=i("read",{kind:"root",name:"site"})?a("root","site"):void 0;return{allowRightClickOverrides:p("core","allowRightClickOverrides"),blockTypes:h(),canUseUnfilteredHTML:r("postType",t,s)?._links?.hasOwnProperty("wp:action-unfiltered-html"),focusMode:p("core","focusMode"),hasFixedToolbar:p("core","fixedToolbar")||!l,hiddenBlockTypes:p("core","hiddenBlockTypes"),isDistractionFree:p("core","distractionFree"),keepCaretInsideBlock:p("core","keepCaretInsideBlock"),hasUploadPermissions:null===(n=i("create",{kind:"root",name:"media"}))||void 0===n||n,userCanCreatePages:i("create",{kind:"postType",name:"page"}),pageOnFront:f?.page_on_front,pageForPosts:f?.page_for_posts,userPatternCategories:c(),restBlockPatternCategories:u(),sectionRootClientId:"template-locked"===o?null!==(x=g("core/post-content")?.[0])&&void 0!==x?x:"":null!==(b=g("core/group").find((e=>"main"===_(e)?.tagName)))&&void 0!==b?b:""};var b,x}),[t,s,l,o]),{merged:I}=Vp(),N=null!==(n=I.styles)&&void 0!==n?n:Up,A=null!==(i=I._links)&&void 0!==i?i:Up,D=null!==(r=e.__experimentalAdditionalBlockPatterns)&&void 0!==r?r:e.__experimentalBlockPatterns,R=null!==(a=e.__experimentalAdditionalBlockPatternCategories)&&void 0!==a?a:e.__experimentalBlockPatternCategories,M=(0,u.useMemo)((()=>[...D||[]].filter((({postTypes:e})=>!e||Array.isArray(e)&&e.includes(t)))),[D,t]),O=(0,u.useMemo)((()=>[...R||[],...T||[]].filter(((e,t,s)=>t===s.findIndex((t=>e.name===t.name))))),[R,T]),{undo:L,setIsInserterOpened:F}=(0,c.useDispatch)(qi),{saveEntityRecord:V}=(0,c.useDispatch)(d.store),U=(0,u.useCallback)((e=>k?V("postType","page",e):Promise.reject({message:(0,gs.__)("You do not have permission to create Pages.")})),[V,k]),z=(0,u.useMemo)((()=>{if(w&&w.length>0){return(!0===e.allowedBlockTypes?g.map((({name:e})=>e)):e.allowedBlockTypes||[]).filter((e=>!w.includes(e)))}return e.allowedBlockTypes}),[e.allowedBlockTypes,w,g]),H=!1===e.focusMode;return(0,u.useMemo)((()=>{const s={...Object.fromEntries(Object.entries(e).filter((([e])=>Hp.includes(e)))),[Gp]:N,[$p]:A,allowedBlockTypes:z,allowRightClickOverrides:h,focusMode:_&&!H,hasFixedToolbar:f,isDistractionFree:b,keepCaretInsideBlock:x,mediaUpload:v?Np:void 0,__experimentalBlockPatterns:M,[Wp]:e=>{const{hasFinishedResolution:s,getBlockPatternsForPostType:o}=sn(e(d.store)),n=o(t);return s("getBlockPatterns")?n:void 0},[Zp]:zp,__experimentalBlockPatternCategories:O,__experimentalUserPatternCategories:E,__experimentalFetchLinkSuggestions:(t,s)=>(0,d.__experimentalFetchLinkSuggestions)(t,s,e),inserterMediaCategories:Bp,__experimentalFetchRichUrlData:d.__experimentalFetchUrlData,__experimentalCanUserUseUnfilteredHTML:S,__experimentalUndo:L,outlineMode:!b&&"wp_template"===t,__experimentalCreatePageEntity:U,__experimentalUserCanCreatePages:k,pageOnFront:P,pageForPosts:C,__experimentalPreferPatternsOnRoot:"wp_template"===t,templateLock:"wp_navigation"===t?"insert":e.templateLock,template:"wp_navigation"===t?[["core/navigation",{},[]]]:e.template,__experimentalSetIsInserterOpened:F,[Yp]:B};return s}),[z,h,_,H,f,b,x,e,v,E,M,O,S,L,U,k,P,C,t,F,B,N,A])},qp=["core/post-title","core/post-featured-image","core/post-content"];function Qp(){const e=(0,u.useMemo)((()=>[...(0,h.applyFilters)("editor.postContentBlockTypes",qp),"core/template-part"]),[]),t=(0,c.useSelect)((t=>{const{getPostBlocksByName:s}=sn(t(qi));return s(e)}),[e]),s=(0,c.useSelect)((e=>{const{getBlocksByName:t,getBlockOrder:s}=e(m.store);return t("core/template-part").flatMap((e=>s(e)))}),[]),o=(0,c.useRegistry)();return(0,u.useEffect)((()=>{const{setBlockEditingMode:e,unsetBlockEditingMode:n}=o.dispatch(m.store);return o.batch((()=>{e("","disabled");for(const s of t)e(s,"contentOnly");for(const t of s)e(t,"disabled")})),()=>{o.batch((()=>{n("");for(const e of t)n(e);for(const e of s)n(e)}))}}),[t,s,o]),null}function Xp(){const e=(0,c.useSelect)((e=>e(m.store).getBlockOrder()?.[0]),[]),{setBlockEditingMode:t,unsetBlockEditingMode:s}=(0,c.useDispatch)(m.store);(0,u.useEffect)((()=>{if(e)return t(e,"contentOnly"),()=>{s(e)}}),[e,s,t])}const Jp=["wp_block","wp_template","wp_template_part"];const eh=(0,P.jsxs)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,P.jsx)(k.Path,{d:"m16 15.5h-8v-1.5h8zm-7.5-2.5h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2zm-9-3h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2z"}),(0,P.jsx)(k.Path,{d:"m18.5 6.5h-13a.5.5 0 0 0 -.5.5v9.5a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9.5a.5.5 0 0 0 -.5-.5zm-13-1.5h13a2 2 0 0 1 2 2v9.5a2 2 0 0 1 -2 2h-13a2 2 0 0 1 -2-2v-9.5a2 2 0 0 1 2-2z"})]}),th=(0,P.jsx)(k.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,P.jsx)(k.Path,{d:"M3 6h11v1.5H3V6Zm3.5 5.5h11V13h-11v-1.5ZM21 17H10v1.5h11V17Z"})}),sh=(0,P.jsx)(k.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,P.jsx)(k.Path,{d:"M20.8 10.7l-4.3-4.3-1.1 1.1 4.3 4.3c.******* 0 .4l-4.3 4.3 1.1 1.1 4.3-4.3c.7-.8.7-1.9 0-2.6zM4.2 11.8l4.3-4.3-1-1-4.3 4.3c-.7.7-.7 1.8 0 2.5l4.3 4.3 1.1-1.1-4.3-4.3c-.2-.1-.2-.3-.1-.4z"})}),oh=(0,P.jsx)(k.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM8.5 18.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h2.5v13zm10-.5c0 .3-.2.5-.5.5h-8v-13h8c.3 0 .5.2.5.5v12z"})}),nh=(0,P.jsx)(k.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4 14.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h8v13zm4.5-.5c0 .3-.2.5-.5.5h-2.5v-13H18c.3 0 .5.2.5.5v12z"})}),ih=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),rh=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M11.1 15.8H20v-1.5h-8.9v1.5zm0-8.6v1.5H20V7.2h-8.9zM6 13c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})}),ah=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"})}),{RenamePatternModal:lh}=sn(cn.privateApis),ch="editor/pattern-rename";function dh(){const{record:e,postType:t}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(qi),{getEditedEntityRecord:o}=e(d.store),n=t();return{record:o("postType",n,s()),postType:n}}),[]),{closeModal:s}=(0,c.useDispatch)(Nr);return(0,c.useSelect)((e=>e(Nr).isModalActive(ch)))&&t===O?(0,P.jsx)(lh,{onClose:s,pattern:e}):null}const{DuplicatePatternModal:uh}=sn(cn.privateApis),ph="editor/pattern-duplicate";function hh(){const{record:e,postType:t}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(qi),{getEditedEntityRecord:o}=e(d.store),n=t();return{record:o("postType",n,s()),postType:n}}),[]),{closeModal:s}=(0,c.useDispatch)(Nr);return(0,c.useSelect)((e=>e(Nr).isModalActive(ph)))&&t===O?(0,P.jsx)(uh,{onClose:s,onSuccess:()=>s(),pattern:e}):null}function mh(){const{editorMode:e,isListViewOpen:t,showBlockBreadcrumbs:s,isDistractionFree:o,isTopToolbar:n,isFocusMode:i,isPreviewMode:r,isViewable:a,isCodeEditingEnabled:l,isRichEditingEnabled:u,isPublishSidebarEnabled:p}=(0,c.useSelect)((e=>{var t,s;const{get:o}=e(j.store),{isListViewOpened:n,getCurrentPostType:i,getEditorSettings:r}=e(qi),{getSettings:a}=e(m.store),{getPostType:l}=e(d.store);return{editorMode:null!==(t=o("core","editorMode"))&&void 0!==t?t:"visual",isListViewOpen:n(),showBlockBreadcrumbs:o("core","showBlockBreadcrumbs"),isDistractionFree:o("core","distractionFree"),isFocusMode:o("core","focusMode"),isTopToolbar:o("core","fixedToolbar"),isPreviewMode:a().__unstableIsPreviewMode,isViewable:null!==(s=l(i())?.viewable)&&void 0!==s&&s,isCodeEditingEnabled:r().codeEditingEnabled,isRichEditingEnabled:r().richEditingEnabled,isPublishSidebarEnabled:e(qi).isPublishSidebarEnabled()}}),[]),{getActiveComplementaryArea:h}=(0,c.useSelect)(Nr),{toggle:g}=(0,c.useDispatch)(j.store),{createInfoNotice:_}=(0,c.useDispatch)(ms.store),{__unstableSaveForPreview:f,setIsListViewOpened:b,switchEditorMode:y,toggleDistractionFree:x}=(0,c.useDispatch)(qi),{openModal:v,enableComplementaryArea:w,disableComplementaryArea:S}=(0,c.useDispatch)(Nr),{getCurrentPostId:k}=(0,c.useSelect)(qi),P=l&&u;if(r)return{commands:[],isLoading:!1};const C=[];return C.push({name:"core/open-shortcut-help",label:(0,gs.__)("Keyboard shortcuts"),icon:eh,callback:({close:e})=>{e(),v("editor/keyboard-shortcut-help")}}),C.push({name:"core/toggle-distraction-free",label:o?(0,gs.__)("Exit Distraction Free"):(0,gs.__)("Enter Distraction Free"),callback:({close:e})=>{x(),e()}}),C.push({name:"core/open-preferences",label:(0,gs.__)("Editor preferences"),callback:({close:e})=>{e(),v("editor/preferences")}}),C.push({name:"core/toggle-spotlight-mode",label:(0,gs.__)("Toggle spotlight"),callback:({close:e})=>{g("core","focusMode"),e(),_(i?(0,gs.__)("Spotlight off."):(0,gs.__)("Spotlight on."),{id:"core/editor/toggle-spotlight-mode/notice",type:"snackbar",actions:[{label:(0,gs.__)("Undo"),onClick:()=>{g("core","focusMode")}}]})}}),C.push({name:"core/toggle-list-view",label:t?(0,gs.__)("Close List View"):(0,gs.__)("Open List View"),icon:th,callback:({close:e})=>{b(!t),e(),_(t?(0,gs.__)("List View off."):(0,gs.__)("List View on."),{id:"core/editor/toggle-list-view/notice",type:"snackbar"})}}),C.push({name:"core/toggle-top-toolbar",label:(0,gs.__)("Toggle top toolbar"),callback:({close:e})=>{g("core","fixedToolbar"),o&&x(),e(),_(n?(0,gs.__)("Top toolbar off."):(0,gs.__)("Top toolbar on."),{id:"core/editor/toggle-top-toolbar/notice",type:"snackbar",actions:[{label:(0,gs.__)("Undo"),onClick:()=>{g("core","fixedToolbar")}}]})}}),P&&C.push({name:"core/toggle-code-editor",label:"visual"===e?(0,gs.__)("Open code editor"):(0,gs.__)("Exit code editor"),icon:sh,callback:({close:t})=>{y("visual"===e?"text":"visual"),t()}}),C.push({name:"core/toggle-breadcrumbs",label:s?(0,gs.__)("Hide block breadcrumbs"):(0,gs.__)("Show block breadcrumbs"),callback:({close:e})=>{g("core","showBlockBreadcrumbs"),e(),_(s?(0,gs.__)("Breadcrumbs hidden."):(0,gs.__)("Breadcrumbs visible."),{id:"core/editor/toggle-breadcrumbs/notice",type:"snackbar"})}}),C.push({name:"core/open-settings-sidebar",label:(0,gs.__)("Toggle settings sidebar"),icon:(0,gs.isRTL)()?oh:nh,callback:({close:e})=>{const t=h("core");e(),"edit-post/document"===t?S("core"):w("core","edit-post/document")}}),C.push({name:"core/open-block-inspector",label:(0,gs.__)("Toggle block inspector"),icon:ih,callback:({close:e})=>{const t=h("core");e(),"edit-post/block"===t?S("core"):w("core","edit-post/block")}}),C.push({name:"core/toggle-publish-sidebar",label:p?(0,gs.__)("Disable pre-publish checks"):(0,gs.__)("Enable pre-publish checks"),icon:rh,callback:({close:e})=>{e(),g("core","isPublishSidebarEnabled"),_(p?(0,gs.__)("Pre-publish checks disabled."):(0,gs.__)("Pre-publish checks enabled."),{id:"core/editor/publish-sidebar/notice",type:"snackbar"})}}),a&&C.push({name:"core/preview-link",label:(0,gs.__)("Preview in a new tab"),icon:mn,callback:async({close:e})=>{e();const t=k(),s=await f();window.open(s,`wp-preview-${t}`)}}),{commands:C,isLoading:!1}}function gh(){const{postType:e}=(0,c.useSelect)((e=>{const{getCurrentPostType:t}=e(qi);return{postType:t()}}),[]),{openModal:t}=(0,c.useDispatch)(Nr),s=[];return e===O&&(s.push({name:"core/rename-pattern",label:(0,gs.__)("Rename pattern"),icon:ah,callback:({close:e})=>{t(ch),e()}}),s.push({name:"core/duplicate-pattern",label:(0,gs.__)("Duplicate pattern"),icon:Di,callback:({close:e})=>{t(ph),e()}})),{isLoading:!1,commands:s}}const{BlockRemovalWarningModal:_h}=sn(m.privateApis),fh=["core/post-content","core/post-template","core/query"],bh=[{postTypes:["wp_template","wp_template_part"],callback(e){if(e.filter((({name:e})=>fh.includes(e))).length)return(0,gs._n)("Deleting this block will stop your post or page content from displaying on this template. It is not recommended.","Some of the deleted blocks will stop your post or page content from displaying on this template. It is not recommended.",e.length)}},{postTypes:["wp_block"],callback(e){if(e.filter((({attributes:e})=>e?.metadata?.bindings&&Object.values(e.metadata.bindings).some((e=>"core/pattern-overrides"===e.source)))).length)return(0,gs._n)("The deleted block allows instance overrides. Removing it may result in content not displaying where this pattern is used. Are you sure you want to proceed?","Some of the deleted blocks allow instance overrides. Removing them may result in content not displaying where this pattern is used. Are you sure you want to proceed?",e.length)}}];function yh(){const e=(0,c.useSelect)((e=>e(qi).getCurrentPostType()),[]),t=(0,u.useMemo)((()=>bh.filter((t=>t.postTypes.includes(e)))),[e]);return _h&&t?(0,P.jsx)(_h,{rules:t}):null}function xh(){const{blockPatternsWithPostContentBlockType:e,postType:t}=(0,c.useSelect)((e=>{const{getPatternsByBlockTypes:t,getBlocksByName:s}=e(m.store),{getCurrentPostType:o,getRenderingMode:n}=e(qi);return{blockPatternsWithPostContentBlockType:t("core/post-content","post-only"===n()?"":s("core/post-content")?.[0]),postType:o()}}),[]);return(0,u.useMemo)((()=>e?.length?e.filter((e=>"page"===t&&!e.postTypes||Array.isArray(e.postTypes)&&e.postTypes.includes(t))):[]),[t,e])}function vh({blockPatterns:e,onChoosePattern:t}){const s=(0,p.useAsyncList)(e),{editEntityRecord:o}=(0,c.useDispatch)(d.store),{postType:n,postId:i}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(qi);return{postType:t(),postId:s()}}),[]);return(0,P.jsx)(m.__experimentalBlockPatternsList,{blockPatterns:e,shownPatterns:s,onClickPattern:(e,s)=>{o("postType",n,i,{blocks:s,content:({blocks:e=[]})=>(0,y.__unstableSerializeAndClean)(e)}),t()}})}function wh({onClose:e}){const t=xh();return t.length>0?(0,P.jsx)(Do.Modal,{title:(0,gs.__)("Choose a pattern"),isFullScreen:!0,onRequestClose:e,children:(0,P.jsx)("div",{className:"editor-start-page-options__modal-content",children:(0,P.jsx)(vh,{blockPatterns:t,onChoosePattern:e})})}):null}function Sh(){const[e,t]=(0,u.useState)(!1),s=(0,c.useSelect)((e=>{const{isEditedPostDirty:t,isEditedPostEmpty:s,getCurrentPostType:o}=e(qi),n=e(Nr).isModalActive("editor/preferences");return e(j.store).get("core","enableChoosePatternModal")&&!n&&!t()&&s()&&R!==o()}),[]);return!s||e?null:(0,P.jsx)(wh,{onClose:()=>t(!0)})}const kh=[{keyCombination:{modifier:"primary",character:"b"},description:(0,gs.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,gs.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,gs.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,gs.__)("Remove a link.")},{keyCombination:{character:"[["},description:(0,gs.__)("Insert a link to a post or page.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,gs.__)("Underline the selected text.")},{keyCombination:{modifier:"access",character:"d"},description:(0,gs.__)("Strikethrough the selected text.")},{keyCombination:{modifier:"access",character:"x"},description:(0,gs.__)("Make the selected text inline code.")},{keyCombination:{modifier:"access",character:"0"},aliases:[{modifier:"access",character:"7"}],description:(0,gs.__)("Convert the current heading to a paragraph.")},{keyCombination:{modifier:"access",character:"1-6"},description:(0,gs.__)("Convert the current paragraph or heading to a heading of level 1 to 6.")},{keyCombination:{modifier:"primaryShift",character:"SPACE"},description:(0,gs.__)("Add non breaking space.")}];function Ph({keyCombination:e,forceAriaLabel:t}){const s=e.modifier?aa.displayShortcutList[e.modifier](e.character):e.character,o=e.modifier?aa.shortcutAriaLabel[e.modifier](e.character):e.character;return(0,P.jsx)("kbd",{className:"editor-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":t||o,children:(Array.isArray(s)?s:[s]).map(((e,t)=>"+"===e?(0,P.jsx)(u.Fragment,{children:e},t):(0,P.jsx)("kbd",{className:"editor-keyboard-shortcut-help-modal__shortcut-key",children:e},t)))})}const Ch=function({description:e,keyCombination:t,aliases:s=[],ariaLabel:o}){return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)("div",{className:"editor-keyboard-shortcut-help-modal__shortcut-description",children:e}),(0,P.jsxs)("div",{className:"editor-keyboard-shortcut-help-modal__shortcut-term",children:[(0,P.jsx)(Ph,{keyCombination:t,forceAriaLabel:o}),s.map(((e,t)=>(0,P.jsx)(Ph,{keyCombination:e,forceAriaLabel:o},t)))]})]})};const jh=function({name:e}){const{keyCombination:t,description:s,aliases:o}=(0,c.useSelect)((t=>{const{getShortcutKeyCombination:s,getShortcutDescription:o,getShortcutAliases:n}=t(lr.store);return{keyCombination:s(e),aliases:n(e),description:o(e)}}),[e]);return t?(0,P.jsx)(Ch,{keyCombination:t,description:s,aliases:o}):null},Eh="editor/keyboard-shortcut-help",Th=({shortcuts:e})=>(0,P.jsx)("ul",{className:"editor-keyboard-shortcut-help-modal__shortcut-list",role:"list",children:e.map(((e,t)=>(0,P.jsx)("li",{className:"editor-keyboard-shortcut-help-modal__shortcut",children:"string"==typeof e?(0,P.jsx)(jh,{name:e}):(0,P.jsx)(Ch,{...e})},t)))}),Bh=({title:e,shortcuts:t,className:s})=>(0,P.jsxs)("section",{className:dr("editor-keyboard-shortcut-help-modal__section",s),children:[!!e&&(0,P.jsx)("h2",{className:"editor-keyboard-shortcut-help-modal__section-title",children:e}),(0,P.jsx)(Th,{shortcuts:t})]}),Ih=({title:e,categoryName:t,additionalShortcuts:s=[]})=>{const o=(0,c.useSelect)((e=>e(lr.store).getCategoryShortcuts(t)),[t]);return(0,P.jsx)(Bh,{title:e,shortcuts:o.concat(s)})};const Nh=function(){const e=(0,c.useSelect)((e=>e(Nr).isModalActive(Eh)),[]),{openModal:t,closeModal:s}=(0,c.useDispatch)(Nr),o=()=>{e?s():t(Eh)};return(0,lr.useShortcut)("core/editor/keyboard-shortcuts",o),e?(0,P.jsxs)(Do.Modal,{className:"editor-keyboard-shortcut-help-modal",title:(0,gs.__)("Keyboard shortcuts"),closeButtonLabel:(0,gs.__)("Close"),onRequestClose:o,children:[(0,P.jsx)(Bh,{className:"editor-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/editor/keyboard-shortcuts"]}),(0,P.jsx)(Ih,{title:(0,gs.__)("Global shortcuts"),categoryName:"global"}),(0,P.jsx)(Ih,{title:(0,gs.__)("Selection shortcuts"),categoryName:"selection"}),(0,P.jsx)(Ih,{title:(0,gs.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,gs.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,gs.__)("Forward-slash")}]}),(0,P.jsx)(Bh,{title:(0,gs.__)("Text formatting"),shortcuts:kh}),(0,P.jsx)(Ih,{title:(0,gs.__)("List View shortcuts"),categoryName:"list-view"})]}):null};function Ah({clientId:e,onClose:t}){const{entity:s,onNavigateToEntityRecord:o,canEditTemplates:n}=(0,c.useSelect)((t=>{const{getBlockEditingMode:s,getBlockParentsByBlockName:o,getSettings:n,getBlockAttributes:i}=t(m.store);if(!("contentOnly"===s(e)))return{};const r=o(e,"core/block",!0)[0];let a;if(r)a=t(d.store).getEntityRecord("postType","wp_block",i(r).ref);else{const{getCurrentTemplateId:s,getRenderingMode:o}=t(qi),n=s(),{getContentLockingParent:i}=sn(t(m.store));"template-locked"===o()&&!i(e)&&n&&(a=t(d.store).getEntityRecord("postType","wp_template",n))}return{canEditTemplates:t(d.store).canUser("create",{kind:"postType",name:"wp_template"}),entity:a,onNavigateToEntityRecord:n().onNavigateToEntityRecord}}),[e]);if(!s)return(0,P.jsx)(Dh,{clientId:e,onClose:t});const i="wp_block"===s.type;let r=i?(0,gs.__)("Edit the pattern to move, delete, or make further changes to this block."):(0,gs.__)("Edit the template to move, delete, or make further changes to this block.");return n||(r=(0,gs.__)("Only users with permissions to edit the template can move or delete this block")),(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(m.__unstableBlockSettingsMenuFirstItem,{children:(0,P.jsx)(Do.MenuItem,{onClick:()=>{o({postId:s.id,postType:s.type})},disabled:!n,children:i?(0,gs.__)("Edit pattern"):(0,gs.__)("Edit template")})}),(0,P.jsx)(Do.__experimentalText,{variant:"muted",as:"p",className:"editor-content-only-settings-menu__description",children:r})]})}function Dh({clientId:e,onClose:t}){const{contentLockingParent:s}=(0,c.useSelect)((t=>{const{getContentLockingParent:s}=sn(t(m.store));return{contentLockingParent:s(e)}}),[e]),o=(0,m.useBlockDisplayInformation)(s),n=(0,c.useDispatch)(m.store);if(!o?.title)return null;const{modifyContentLockBlock:i}=sn(n);return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(m.__unstableBlockSettingsMenuFirstItem,{children:(0,P.jsx)(Do.MenuItem,{onClick:()=>{i(s),t()},children:(0,gs._x)("Unlock","Unlock content locked blocks")})}),(0,P.jsx)(Do.__experimentalText,{variant:"muted",as:"p",className:"editor-content-only-settings-menu__description",children:(0,gs.__)("Temporarily unlock the parent block to edit, delete or make further changes to this block.")})]})}function Rh(){return(0,P.jsx)(m.BlockSettingsMenuControls,{children:({selectedClientIds:e,onClose:t})=>1===e.length&&(0,P.jsx)(Ah,{clientId:e[0],onClose:t})})}function Mh(e){const{slug:t,patterns:s}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(qi),{getEntityRecord:o,getBlockPatterns:n}=e(d.store),i=s();return{slug:o("postType",t(),i).slug,patterns:n()}}),[]),o=(0,c.useSelect)((e=>e(d.store).getCurrentTheme().stylesheet));return(0,u.useMemo)((()=>[{name:"fallback",blocks:(0,y.parse)(e),title:(0,gs.__)("Fallback content")},...s.filter((e=>Array.isArray(e.templateTypes)&&e.templateTypes.some((e=>t.startsWith(e))))).map((e=>({...e,blocks:(0,y.parse)(e.content).map((e=>function(e){return e.innerBlocks.find((e=>"core/template-part"===e.name))&&(e.innerBlocks=e.innerBlocks.map((e=>("core/template-part"===e.name&&void 0===e.attributes.theme&&(e.attributes.theme=o),e)))),"core/template-part"===e.name&&void 0===e.attributes.theme&&(e.attributes.theme=o),e}(e)))})))]),[e,t,s])}function Oh({fallbackContent:e,onChoosePattern:t,postType:s}){const[,,o]=(0,d.useEntityBlockEditor)("postType",s),n=Mh(e),i=(0,p.useAsyncList)(n);return(0,P.jsx)(m.__experimentalBlockPatternsList,{blockPatterns:n,shownPatterns:i,onClickPattern:(e,s)=>{o(s,{selection:void 0}),t()}})}function Lh({slug:e,isCustom:t,onClose:s,postType:o}){const n=function(e,t=!1){return(0,c.useSelect)((s=>{const{getEntityRecord:o,getDefaultTemplateId:n}=s(d.store),i=n({slug:e,is_custom:t,ignore_empty:!0});return i?o("postType",R,i)?.content?.raw:void 0}),[e,t])}(e,t);return n?(0,P.jsxs)(Do.Modal,{className:"editor-start-template-options__modal",title:(0,gs.__)("Choose a pattern"),closeLabel:(0,gs.__)("Cancel"),focusOnMount:"firstElement",onRequestClose:s,isFullScreen:!0,children:[(0,P.jsx)("div",{className:"editor-start-template-options__modal-content",children:(0,P.jsx)(Oh,{fallbackContent:n,slug:e,isCustom:t,postType:o,onChoosePattern:()=>{s()}})}),(0,P.jsx)(Do.Flex,{className:"editor-start-template-options__modal__actions",justify:"flex-end",expanded:!1,children:(0,P.jsx)(Do.FlexItem,{children:(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:s,children:(0,gs.__)("Skip")})})})]}):null}function Fh(){const[e,t]=(0,u.useState)(!1),{shouldOpenModal:s,slug:o,isCustom:n,postType:i,postId:r}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(qi),o=t(),n=s(),{getEditedEntityRecord:i,hasEditsForEntityRecord:r}=e(d.store),a=i("postType",o,n);return{shouldOpenModal:!r("postType",o,n)&&""===a.content&&R===o,slug:a.slug,isCustom:a.is_custom,postType:o,postId:n}}),[]);return(0,u.useEffect)((()=>{t(!1)}),[i,r]),!s||e?null:(0,P.jsx)(Lh,{slug:o,isCustom:n,postType:i,onClose:()=>t(!0)})}function Vh({clientId:e,onClose:t}){const{getBlocks:s}=(0,c.useSelect)(m.store),{replaceBlocks:o}=(0,c.useDispatch)(m.store);return(0,c.useSelect)((t=>t(m.store).canRemoveBlock(e)),[e])?(0,P.jsx)(Do.MenuItem,{onClick:()=>{o(e,s(e)),t()},children:(0,gs.__)("Detach")}):null}function Uh({clientIds:e,blocks:t}){const[s,o]=(0,u.useState)(!1),{replaceBlocks:n}=(0,c.useDispatch)(m.store),{createSuccessNotice:i}=(0,c.useDispatch)(ms.store),{canCreate:r}=(0,c.useSelect)((e=>({canCreate:e(m.store).canInsertBlockType("core/template-part")})),[]);if(!r)return null;return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.MenuItem,{icon:$,onClick:()=>{o(!0)},"aria-expanded":s,"aria-haspopup":"dialog",children:(0,gs.__)("Create template part")}),s&&(0,P.jsx)(Wo,{closeModal:()=>{o(!1)},blocks:t,onCreate:async t=>{n(e,(0,y.createBlock)("core/template-part",{slug:t.slug,theme:t.theme})),i((0,gs.__)("Template part created."),{type:"snackbar"})}})]})}function zh(){return(0,P.jsx)(m.BlockSettingsMenuControls,{children:({selectedClientIds:e,onClose:t})=>(0,P.jsx)(Hh,{clientIds:e,onClose:t})})}function Hh({clientIds:e,onClose:t}){const{isContentOnly:s,blocks:o}=(0,c.useSelect)((t=>{const{getBlocksByClientId:s,getBlockEditingMode:o}=t(m.store);return{blocks:s(e),isContentOnly:1===e.length&&"contentOnly"===o(e[0])}}),[e]);return s?null:1===o.length&&"core/template-part"===o[0]?.name?(0,P.jsx)(Vh,{clientId:e[0],onClose:t}):(0,P.jsx)(Uh,{clientIds:e,blocks:o})}const{ExperimentalBlockEditorProvider:Gh}=sn(m.privateApis),{PatternsMenuItems:$h}=sn(cn.privateApis),Wh=()=>{},Zh=["wp_block","wp_navigation","wp_template_part"];const Yh=Pp((({post:e,settings:t,recovery:s,initialEdits:o,children:n,BlockEditorProviderComponent:i=Gh,__unstableTemplate:r})=>{const{editorSettings:a,selection:l,isReady:p,mode:g,postTypeEntities:_}=(0,c.useSelect)((t=>{const{getEditorSettings:s,getEditorSelection:o,getRenderingMode:n,__unstableIsEditorReady:i}=t(qi),{getEntitiesConfig:r}=t(d.store);return{editorSettings:s(),isReady:i(),mode:n(),selection:o(),postTypeEntities:"wp_template"===e.type?r("postType"):null}}),[e.type]),f=(0,c.useSelect)((e=>{const{__unstableGetEditorMode:t}=sn(e(m.store));return"zoom-out"===t()})),b=!!r&&"post-only"!==g,x=b?r:e,v=(0,u.useMemo)((()=>{const t={};if("wp_template"===e.type){if("page"===e.slug)t.postType="page";else if("single"===e.slug)t.postType="post";else if("single"===e.slug.split("-")[0]){const s=_?.map((e=>e.name))||[],o=e.slug.match(`^single-(${s.join("|")})(?:-.+)?$`);o&&(t.postType=o[1])}}else Zh.includes(x.type)&&!b||(t.postId=e.id,t.postType=e.type);return{...t,templateSlug:"wp_template"===x.type?x.slug:void 0}}),[b,e.id,e.type,e.slug,x.type,x.slug,_]),{id:w,type:S}=x,k=Kp(a,S,w,g),[C,j,E]=function(e,t,s){const o="post-only"!==s&&t?"template":"post",[n,i,r]=(0,d.useEntityBlockEditor)("postType",e.type,{id:e.id}),[a,l,c]=(0,d.useEntityBlockEditor)("postType",t?.type,{id:t?.id}),p=(0,u.useMemo)((()=>{if("wp_navigation"===e.type)return[(0,y.createBlock)("core/navigation",{ref:e.id,templateLock:!1})]}),[e.type,e.id]),h=(0,u.useMemo)((()=>p||("template"===o?a:n)),[p,o,a,n]);return t&&"template-locked"===s||"wp_navigation"===e.type?[h,Wh,Wh]:[h,"post"===o?i:l,"post"===o?r:c]}(e,r,g),{updatePostLock:T,setupEditor:B,updateEditorSettings:I,setCurrentTemplateId:N,setEditedPost:A,setRenderingMode:D}=sn((0,c.useDispatch)(qi)),{createWarningNotice:R}=(0,c.useDispatch)(ms.store);return(0,u.useLayoutEffect)((()=>{s||(T(t.postLock),B(e,o,t.template),t.autosave&&R((0,gs.__)("There is an autosave of this post that is more recent than the version below."),{id:"autosave-exists",actions:[{label:(0,gs.__)("View the autosave"),url:t.autosave.editLink}]}))}),[]),(0,u.useEffect)((()=>{A(e.type,e.id)}),[e.type,e.id,A]),(0,u.useEffect)((()=>{I(t)}),[t,I]),(0,u.useEffect)((()=>{N(r?.id)}),[r?.id,N]),(0,u.useEffect)((()=>{var e;D(null!==(e=t.defaultRenderingMode)&&void 0!==e?e:"post-only")}),[t.defaultRenderingMode,D]),function(e,t){(0,u.useEffect)((()=>((0,h.addFilter)("blockEditor.__unstableCanInsertBlockType","removeTemplatePartsFromInserter",((s,o)=>!(!Jp.includes(e)&&"core/template-part"===o.name&&"post-only"===t)&&s)),(0,h.addFilter)("blockEditor.__unstableCanInsertBlockType","removePostContentFromInserter",((t,s,o,{getBlockParentsByBlockName:n})=>Jp.includes(e)||"core/post-content"!==s.name?t:n(o,"core/query").length>0)),()=>{(0,h.removeFilter)("blockEditor.__unstableCanInsertBlockType","removeTemplatePartsFromInserter"),(0,h.removeFilter)("blockEditor.__unstableCanInsertBlockType","removePostContentFromInserter")})),[e,t])}(e.type,g),(0,la.useCommandLoader)({name:"core/editor/edit-ui",hook:mh}),(0,la.useCommandLoader)({name:"core/editor/contextual-commands",hook:gh,context:"entity-edit"}),p?(0,P.jsx)(d.EntityProvider,{kind:"root",type:"site",children:(0,P.jsx)(d.EntityProvider,{kind:"postType",type:e.type,id:e.id,children:(0,P.jsx)(m.BlockContextProvider,{value:v,children:(0,P.jsxs)(i,{value:C,onChange:E,onInput:j,selection:l,settings:k,useSubRegistry:!1,children:[n,!t.__unstableIsPreviewMode&&(0,P.jsxs)(P.Fragment,{children:[!f&&(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)($h,{}),(0,P.jsx)(zh,{}),(0,P.jsx)(Rh,{})]}),"template-locked"===g&&(0,P.jsx)(Qp,{}),"wp_navigation"===S&&(0,P.jsx)(Xp,{}),(0,P.jsx)(sa,{}),(0,P.jsx)(Nh,{}),(0,P.jsx)(yh,{}),(0,P.jsx)(Sh,{}),(0,P.jsx)(Fh,{}),(0,P.jsx)(dh,{}),(0,P.jsx)(hh,{})]})]})})})}):null}));const Kh=function(e){return(0,P.jsx)(Yh,{...e,BlockEditorProviderComponent:m.BlockEditorProvider,children:e.children})},qh=window.wp.serverSideRender;var Qh=s.n(qh);function Xh(e,t,s=[]){const o=(0,u.forwardRef)(((s,o)=>(S()("wp.editor."+e,{since:"5.3",alternative:"wp.blockEditor."+e,version:"6.2"}),(0,P.jsx)(t,{ref:o,...s}))));return s.forEach((s=>{o[s]=Xh(e+"."+s,t[s])})),o}function Jh(e,t){return(...s)=>(S()("wp.editor."+e,{since:"5.3",alternative:"wp.blockEditor."+e,version:"6.2"}),t(...s))}const em=Xh("RichText",m.RichText,["Content"]);em.isEmpty=Jh("RichText.isEmpty",m.RichText.isEmpty);const tm=Xh("Autocomplete",m.Autocomplete),sm=Xh("AlignmentToolbar",m.AlignmentToolbar),om=Xh("BlockAlignmentToolbar",m.BlockAlignmentToolbar),nm=Xh("BlockControls",m.BlockControls,["Slot"]),im=Xh("BlockEdit",m.BlockEdit),rm=Xh("BlockEditorKeyboardShortcuts",m.BlockEditorKeyboardShortcuts),am=Xh("BlockFormatControls",m.BlockFormatControls,["Slot"]),lm=Xh("BlockIcon",m.BlockIcon),cm=Xh("BlockInspector",m.BlockInspector),dm=Xh("BlockList",m.BlockList),um=Xh("BlockMover",m.BlockMover),pm=Xh("BlockNavigationDropdown",m.BlockNavigationDropdown),hm=Xh("BlockSelectionClearer",m.BlockSelectionClearer),mm=Xh("BlockSettingsMenu",m.BlockSettingsMenu),gm=Xh("BlockTitle",m.BlockTitle),_m=Xh("BlockToolbar",m.BlockToolbar),fm=Xh("ColorPalette",m.ColorPalette),bm=Xh("ContrastChecker",m.ContrastChecker),ym=Xh("CopyHandler",m.CopyHandler),xm=Xh("DefaultBlockAppender",m.DefaultBlockAppender),vm=Xh("FontSizePicker",m.FontSizePicker),wm=Xh("Inserter",m.Inserter),Sm=Xh("InnerBlocks",m.InnerBlocks,["ButtonBlockAppender","DefaultBlockAppender","Content"]),km=Xh("InspectorAdvancedControls",m.InspectorAdvancedControls,["Slot"]),Pm=Xh("InspectorControls",m.InspectorControls,["Slot"]),Cm=Xh("PanelColorSettings",m.PanelColorSettings),jm=Xh("PlainText",m.PlainText),Em=Xh("RichTextShortcut",m.RichTextShortcut),Tm=Xh("RichTextToolbarButton",m.RichTextToolbarButton),Bm=Xh("__unstableRichTextInputEvent",m.__unstableRichTextInputEvent),Im=Xh("MediaPlaceholder",m.MediaPlaceholder),Nm=Xh("MediaUpload",m.MediaUpload),Am=Xh("MediaUploadCheck",m.MediaUploadCheck),Dm=Xh("MultiSelectScrollIntoView",m.MultiSelectScrollIntoView),Rm=Xh("NavigableToolbar",m.NavigableToolbar),Mm=Xh("ObserveTyping",m.ObserveTyping),Om=Xh("SkipToSelectedBlock",m.SkipToSelectedBlock),Lm=Xh("URLInput",m.URLInput),Fm=Xh("URLInputButton",m.URLInputButton),Vm=Xh("URLPopover",m.URLPopover),Um=Xh("Warning",m.Warning),zm=Xh("WritingFlow",m.WritingFlow),Hm=Jh("createCustomColorsHOC",m.createCustomColorsHOC),Gm=Jh("getColorClassName",m.getColorClassName),$m=Jh("getColorObjectByAttributeValues",m.getColorObjectByAttributeValues),Wm=Jh("getColorObjectByColorValue",m.getColorObjectByColorValue),Zm=Jh("getFontSize",m.getFontSize),Ym=Jh("getFontSizeClass",m.getFontSizeClass),Km=Jh("withColorContext",m.withColorContext),qm=Jh("withColors",m.withColors),Qm=Jh("withFontSizes",m.withFontSizes),Xm=sa,Jm=sa;function eg(e){return S()("wp.editor.cleanForSlug",{since:"12.7",plugin:"Gutenberg",alternative:"wp.url.cleanForSlug"}),(0,v.cleanForSlug)(e)}const{createPrivateSlotFill:tg}=sn(Do.privateApis),sg=tg("EditCanvasContainerSlot"),og="__experimentalMainDashboardButton",{Fill:ng,Slot:ig}=(0,Do.createSlotFill)(og),rg=ng;rg.Slot=()=>{const e=(0,Do.__experimentalUseSlotFills)(og);return(0,P.jsx)(ig,{bubblesVirtually:!0,fillProps:{length:e?e.length:0}})};const ag=rg,lg=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M6.6 6L5.4 7l4.5 5-4.5 5 1.1 1 5.5-6-5.4-6zm6 0l-1.1 1 4.5 5-4.5 5 1.1 1 5.5-6-5.5-6z"})}),cg=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M11.6 7l-1.1-1L5 12l5.5 6 1.1-1L7 12l4.6-5zm6 0l-1.1-1-5.5 6 5.5 6 1.1-1-4.6-5 4.6-5z"})}),{useHasBlockToolbar:dg}=sn(m.privateApis);function ug({isCollapsed:e,onToggle:t}){const{blockSelectionStart:s}=(0,c.useSelect)((e=>({blockSelectionStart:e(m.store).getBlockSelectionStart()})),[]),o=dg(),n=!!s;return(0,u.useEffect)((()=>{s&&t(!1)}),[s,t]),o?(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)("div",{className:dr("editor-collapsible-block-toolbar",{"is-collapsed":e||!n}),children:(0,P.jsx)(m.BlockToolbar,{hideDragHandle:!0})}),(0,P.jsx)(Do.Popover.Slot,{name:"block-toolbar"}),(0,P.jsx)(Do.Button,{className:"editor-collapsible-block-toolbar__toggle",icon:e?lg:cg,onClick:()=>{t(!e)},label:e?(0,gs.__)("Show block tools"):(0,gs.__)("Hide block tools"),size:"compact"})]}):null}const pg=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})});const hg=function({className:e,disableBlockTools:t=!1}){const{setIsInserterOpened:s,setIsListViewOpened:o}=(0,c.useDispatch)(qi),{isDistractionFree:n,isInserterOpened:i,isListViewOpen:r,listViewShortcut:a,inserterSidebarToggleRef:l,listViewToggleRef:d,hasFixedToolbar:h,showIconLabels:g}=(0,c.useSelect)((e=>{const{getSettings:t}=e(m.store),{get:s}=e(j.store),{isListViewOpened:o,getEditorMode:n,getInserterSidebarToggleRef:i,getListViewToggleRef:r}=sn(e(qi)),{getShortcutRepresentation:a}=e(lr.store),{__unstableGetEditorMode:l}=e(m.store);return{isInserterOpened:e(qi).isInserterOpened(),isListViewOpen:o(),listViewShortcut:a("core/editor/toggle-list-view"),inserterSidebarToggleRef:i(),listViewToggleRef:r(),hasFixedToolbar:t().hasFixedToolbar,showIconLabels:s("core","showIconLabels"),isDistractionFree:s("core","distractionFree"),isVisualMode:"visual"===n(),isZoomedOutView:"zoom-out"===l()}}),[]),_=(0,p.useViewportMatch)("medium"),f=(0,p.useViewportMatch)("wide"),b=(0,gs.__)("Document tools"),y=(0,u.useCallback)((()=>o(!r)),[o,r]),x=(0,u.useCallback)((()=>s(!i)),[i,s]),v=(0,gs._x)("Toggle block inserter","Generic label for block inserter button"),w=i?(0,gs.__)("Close"):(0,gs.__)("Add");return(0,P.jsx)(m.NavigableToolbar,{className:dr("editor-document-tools","edit-post-header-toolbar",e),"aria-label":b,variant:"unstyled",children:(0,P.jsxs)("div",{className:"editor-document-tools__left",children:[!n&&(0,P.jsx)(Do.ToolbarItem,{ref:l,as:Do.Button,className:"editor-document-tools__inserter-toggle",variant:"primary",isPressed:i,onMouseDown:e=>{i&&e.preventDefault()},onClick:x,disabled:t,icon:pg,label:g?w:v,showTooltip:!g,"aria-expanded":i}),(f||!g)&&(0,P.jsxs)(P.Fragment,{children:[_&&!h&&(0,P.jsx)(Do.ToolbarItem,{as:m.ToolSelector,showTooltip:!g,variant:g?"tertiary":void 0,disabled:t,size:"compact"}),(0,P.jsx)(Do.ToolbarItem,{as:Ca,showTooltip:!g,variant:g?"tertiary":void 0,size:"compact"}),(0,P.jsx)(Do.ToolbarItem,{as:Pa,showTooltip:!g,variant:g?"tertiary":void 0,size:"compact"}),!n&&(0,P.jsx)(Do.ToolbarItem,{as:Do.Button,className:"editor-document-tools__document-overview-toggle",icon:th,disabled:t,isPressed:r,label:(0,gs.__)("Document Overview"),onClick:y,shortcut:a,showTooltip:!g,variant:g?"tertiary":void 0,"aria-expanded":r,ref:d,size:"compact"})]})]})})},mg=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})});function gg(){const{createNotice:e}=(0,c.useDispatch)(ms.store),{getCurrentPostId:t,getCurrentPostType:s}=(0,c.useSelect)(qi),{getEditedEntityRecord:o}=(0,c.useSelect)(d.store);const n=(0,p.useCopyToClipboard)((function(){const e=o("postType",s(),t());return e?"function"==typeof e.content?e.content(e):e.blocks?(0,y.__unstableSerializeAndClean)(e.blocks):e.content?e.content:void 0:""}),(function(){e("info",(0,gs.__)("All content copied."),{isDismissible:!0,type:"snackbar"})}));return(0,P.jsx)(Do.MenuItem,{ref:n,children:(0,gs.__)("Copy all blocks")})}const _g=[{value:"visual",label:(0,gs.__)("Visual editor")},{value:"text",label:(0,gs.__)("Code editor")}];const fg=function(){const{shortcut:e,isRichEditingEnabled:t,isCodeEditingEnabled:s,mode:o}=(0,c.useSelect)((e=>({shortcut:e(lr.store).getShortcutRepresentation("core/editor/toggle-mode"),isRichEditingEnabled:e(qi).getEditorSettings().richEditingEnabled,isCodeEditingEnabled:e(qi).getEditorSettings().codeEditingEnabled,mode:e(qi).getEditorMode()})),[]),{switchEditorMode:n}=(0,c.useDispatch)(qi);let i=o;t||"visual"!==o||(i="text"),s||"text"!==o||(i="visual");const r=_g.map((o=>(s||"text"!==o.value||(o={...o,disabled:!0}),t||"visual"!==o.value||(o={...o,disabled:!0,info:(0,gs.__)("You can enable the visual editor in your profile settings.")}),o.value===i||o.disabled?o:{...o,shortcut:e})));return(0,P.jsx)(Do.MenuGroup,{label:(0,gs.__)("Editor"),children:(0,P.jsx)(Do.MenuItemsChoice,{choices:r,value:i,onSelect:n})})},{Fill:bg,Slot:yg}=(0,Do.createSlotFill)("ToolsMoreMenuGroup");bg.Slot=({fillProps:e})=>(0,P.jsx)(yg,{fillProps:e});const xg=bg,{Fill:vg,Slot:wg}=(0,Do.createSlotFill)("web"===u.Platform.OS?Symbol("ViewMoreMenuGroup"):"ViewMoreMenuGroup");vg.Slot=({fillProps:e})=>(0,P.jsx)(wg,{fillProps:e});const Sg=vg;function kg(){const{openModal:e}=(0,c.useDispatch)(Nr),{set:t}=(0,c.useDispatch)(j.store),{toggleDistractionFree:s}=(0,c.useDispatch)(qi),o=(0,c.useSelect)((e=>e(j.store).get("core","showIconLabels")),[]),n=()=>{t("core","distractionFree",!1)};return(0,P.jsx)(P.Fragment,{children:(0,P.jsx)(Do.DropdownMenu,{icon:mg,label:(0,gs.__)("Options"),popoverProps:{placement:"bottom-end",className:"more-menu-dropdown__content"},toggleProps:{showTooltip:!o,...o&&{variant:"tertiary"},tooltipPosition:"bottom",size:"compact"},children:({onClose:t})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsxs)(Do.MenuGroup,{label:(0,gs._x)("View","noun"),children:[(0,P.jsx)(j.PreferenceToggleMenuItem,{scope:"core",name:"fixedToolbar",onToggle:n,label:(0,gs.__)("Top toolbar"),info:(0,gs.__)("Access all block and document tools in a single place"),messageActivated:(0,gs.__)("Top toolbar activated"),messageDeactivated:(0,gs.__)("Top toolbar deactivated")}),(0,P.jsx)(j.PreferenceToggleMenuItem,{scope:"core",name:"distractionFree",label:(0,gs.__)("Distraction free"),info:(0,gs.__)("Write with calmness"),handleToggling:!1,onToggle:s,messageActivated:(0,gs.__)("Distraction free mode activated"),messageDeactivated:(0,gs.__)("Distraction free mode deactivated"),shortcut:aa.displayShortcut.primaryShift("\\")}),(0,P.jsx)(j.PreferenceToggleMenuItem,{scope:"core",name:"focusMode",label:(0,gs.__)("Spotlight mode"),info:(0,gs.__)("Focus on one block at a time"),messageActivated:(0,gs.__)("Spotlight mode activated"),messageDeactivated:(0,gs.__)("Spotlight mode deactivated")}),(0,P.jsx)(Sg.Slot,{fillProps:{onClose:t}})]}),(0,P.jsx)(fg,{}),(0,P.jsx)(Fr.Slot,{name:"core/plugin-more-menu",label:(0,gs.__)("Plugins"),as:Do.MenuGroup,fillProps:{onClick:t}}),(0,P.jsxs)(Do.MenuGroup,{label:(0,gs.__)("Tools"),children:[(0,P.jsx)(Do.MenuItem,{onClick:()=>e("editor/keyboard-shortcut-help"),shortcut:aa.displayShortcut.access("h"),children:(0,gs.__)("Keyboard shortcuts")}),(0,P.jsx)(gg,{}),(0,P.jsxs)(Do.MenuItem,{icon:mn,href:(0,gs.__)("https://wordpress.org/documentation/article/wordpress-block-editor/"),target:"_blank",rel:"noopener noreferrer",children:[(0,gs.__)("Help"),(0,P.jsx)(Do.VisuallyHidden,{as:"span",children:(0,gs.__)("(opens in a new tab)")})]}),(0,P.jsx)(xg.Slot,{fillProps:{onClose:t}})]}),(0,P.jsx)(Do.MenuGroup,{children:(0,P.jsx)(Do.MenuItem,{onClick:()=>e("editor/preferences"),children:(0,gs.__)("Preferences")})})]})})})}const Pg=(0,p.compose)((0,c.withSelect)((e=>{var t;return{hasPublishAction:null!==(t=e(qi).getCurrentPost()?._links?.["wp:action-publish"])&&void 0!==t&&t,isBeingScheduled:e(qi).isEditedPostBeingScheduled(),isPending:e(qi).isCurrentPostPending(),isPublished:e(qi).isCurrentPostPublished(),isPublishSidebarEnabled:e(qi).isPublishSidebarEnabled(),isPublishSidebarOpened:e(qi).isPublishSidebarOpened(),isScheduled:e(qi).isCurrentPostScheduled(),postStatus:e(qi).getEditedPostAttribute("status"),postStatusHasChanged:e(qi).getPostEdits()?.status}})),(0,c.withDispatch)((e=>{const{togglePublishSidebar:t}=e(qi);return{togglePublishSidebar:t}})))((function({forceIsDirty:e,hasPublishAction:t,isBeingScheduled:s,isPending:o,isPublished:n,isPublishSidebarEnabled:i,isPublishSidebarOpened:r,isScheduled:a,togglePublishSidebar:l,setEntitiesSavedStatesCallback:c,postStatusHasChanged:d,postStatus:u}){const h="toggle",m="button",g=(0,p.useViewportMatch)("medium","<");let _;return _=n||d&&!["future","publish"].includes(u)||a&&s||o&&!t&&!g?m:g||i?h:m,(0,P.jsx)(rd,{forceIsDirty:e,isOpen:r,isToggle:_===h,onToggle:l,setEntitiesSavedStatesCallback:c})}));function Cg(){const{hasLoaded:e,permalink:t,isPublished:s,label:o,showIconLabels:n}=(0,c.useSelect)((e=>{const t=e(qi).getCurrentPostType(),s=e(d.store).getPostType(t),{get:o}=e(j.store);return{permalink:e(qi).getPermalink(),isPublished:e(qi).isCurrentPostPublished(),label:s?.labels.view_item,hasLoaded:!!s,showIconLabels:o("core","showIconLabels")}}),[]);return s&&t&&e?(0,P.jsx)(Do.Button,{icon:mn,label:o||(0,gs.__)("View post"),href:t,target:"_blank",showTooltip:!n,size:"compact"}):null}const jg=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M20.5 16h-.7V8c0-1.1-.9-2-2-2H6.2c-1.1 0-2 .9-2 2v8h-.7c-.8 0-1.5.7-1.5 1.5h20c0-.8-.7-1.5-1.5-1.5zM5.7 8c0-.3.2-.5.5-.5h11.6c.3 0 .5.2.5.5v7.6H5.7V8z"})}),Eg=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M15 4H9c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm.5 14c0 .3-.2.5-.5.5H9c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h6c.3 0 .5.2.5.5v12zm-4.5-.5h2V16h-2v1.5z"})}),Tg=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(k.Path,{d:"M17 4H7c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm.5 14c0 .3-.2.5-.5.5H7c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h10c.3 0 .5.2.5.5v12zm-7.5-.5h4V16h-4v1.5z"})});function Bg({forceIsAutosaveable:e,disabled:t}){const{deviceType:s,homeUrl:o,isTemplate:n,isViewable:i,showIconLabels:r}=(0,c.useSelect)((e=>{var t;const{getDeviceType:s,getCurrentPostType:o}=e(qi),{getEntityRecord:n,getPostType:i}=e(d.store),{get:r}=e(j.store),a=o();return{deviceType:s(),homeUrl:n("root","__unstableBase")?.home,isTemplate:"wp_template"===a,isViewable:null!==(t=i(a)?.viewable)&&void 0!==t&&t,showIconLabels:r("core","showIconLabels")}}),[]),{setDeviceType:a}=(0,c.useDispatch)(qi),{__unstableSetEditorMode:l}=(0,c.useDispatch)(m.store),{resetZoomLevel:u}=sn((0,c.useDispatch)(m.store)),h=e=>{a(e),l("edit"),u()};if((0,p.useViewportMatch)("medium","<"))return null;const g={className:"editor-preview-dropdown__toggle",iconPosition:"right",size:"compact",showTooltip:!r,disabled:t,accessibleWhenDisabled:t},_={"aria-label":(0,gs.__)("View options")},f={desktop:jg,mobile:Eg,tablet:Tg},b=[{value:"Desktop",label:(0,gs.__)("Desktop"),icon:jg},{value:"Tablet",label:(0,gs.__)("Tablet"),icon:Tg},{value:"Mobile",label:(0,gs.__)("Mobile"),icon:Eg}];return(0,P.jsx)(Do.DropdownMenu,{className:dr("editor-preview-dropdown",`editor-preview-dropdown--${s.toLowerCase()}`),popoverProps:{placement:"bottom-end"},toggleProps:g,menuProps:_,icon:f[s.toLowerCase()],label:(0,gs.__)("View"),disableOpenOnArrowDown:t,children:({onClose:t})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.MenuGroup,{children:(0,P.jsx)(Do.MenuItemsChoice,{choices:b,value:s,onSelect:h})}),n&&(0,P.jsx)(Do.MenuGroup,{children:(0,P.jsxs)(Do.MenuItem,{href:o,target:"_blank",icon:mn,onClick:t,children:[(0,gs.__)("View site"),(0,P.jsx)(Do.VisuallyHidden,{as:"span",children:(0,gs.__)("(opens in a new tab)")})]})}),i&&(0,P.jsx)(Do.MenuGroup,{children:(0,P.jsx)(sd,{className:"editor-preview-dropdown__button-external",role:"menuitem",forceIsAutosaveable:e,"aria-label":(0,gs.__)("Preview in new tab"),textContent:(0,P.jsxs)(P.Fragment,{children:[(0,gs.__)("Preview in new tab"),(0,P.jsx)(Do.Icon,{icon:mn})]}),onPreview:t})}),(0,P.jsx)(Fr.Slot,{name:"core/plugin-preview-menu",as:Do.MenuGroup,fillProps:{onClick:t}})]})})}const Ig=(0,P.jsx)(k.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",children:(0,P.jsx)(k.Path,{fill:"none",d:"M5.75 12.75V18.25H11.25M12.75 5.75H18.25V11.25",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"square"})}),Ng=({disabled:e})=>{const{isZoomOut:t,showIconLabels:s}=(0,c.useSelect)((e=>({isZoomOut:sn(e(m.store)).isZoomOut(),showIconLabels:e(j.store).get("core","showIconLabels")}))),{resetZoomLevel:o,setZoomLevel:n,__unstableSetEditorMode:i}=sn((0,c.useDispatch)(m.store));return(0,P.jsx)(Do.Button,{accessibleWhenDisabled:!0,disabled:e,onClick:()=>{t?o():n(50),i(t?"edit":"zoom-out")},icon:Ig,label:(0,gs.__)("Zoom Out"),isPressed:t,size:"compact",showTooltip:!s})},Ag={distractionFreeDisabled:{y:"-50px"},distractionFreeHover:{y:0},distractionFreeHidden:{y:"-50px"},visible:{y:0},hidden:{y:0}},Dg={distractionFreeDisabled:{x:"-100%"},distractionFreeHover:{x:0},distractionFreeHidden:{x:"-100%"},visible:{x:0},hidden:{x:0}};const Rg=function({customSaveButton:e,forceIsDirty:t,forceDisableBlockTools:s,setEntitiesSavedStatesCallback:o,title:n,isEditorIframed:i}){const r=(0,p.useViewportMatch)("large"),a=(0,p.useViewportMatch)("medium"),l=(0,p.useMediaQuery)("(max-width: 403px)"),{isTextEditor:d,isPublishSidebarOpened:h,showIconLabels:g,hasFixedToolbar:_,hasBlockSelection:f,isNestedEntity:b}=(0,c.useSelect)((e=>{const{get:t}=e(j.store),{getEditorMode:s,getEditorSettings:o,isPublishSidebarOpened:n}=e(qi),{__unstableGetEditorMode:i}=e(m.store);return{isTextEditor:"text"===s(),isPublishSidebarOpened:n(),showIconLabels:t("core","showIconLabels"),hasFixedToolbar:t("core","fixedToolbar"),hasBlockSelection:!!e(m.store).getBlockSelectionStart(),isNestedEntity:!!o().onNavigateToPreviousEntityRecord,isZoomedOutView:"zoom-out"===i()}}),[]),[y,x]=(0,u.useState)(!0),v=(!f||y)&&!l,w=(()=>{const e=(0,Do.__experimentalUseSlotFills)(og);return Boolean(e&&e.length)})();return(0,P.jsxs)("div",{className:"editor-header edit-post-header",children:[w&&(0,P.jsx)(Do.__unstableMotion.div,{className:"editor-header__back-button",variants:Dg,transition:{type:"tween"},children:(0,P.jsx)(ag.Slot,{})}),(0,P.jsxs)(Do.__unstableMotion.div,{variants:Ag,className:"editor-header__toolbar",transition:{type:"tween"},children:[(0,P.jsx)(hg,{disableBlockTools:s||d}),_&&a&&(0,P.jsx)(ug,{isCollapsed:y,onToggle:x})]}),v&&(0,P.jsx)(Do.__unstableMotion.div,{className:"editor-header__center",variants:Ag,transition:{type:"tween"},children:(0,P.jsx)(da,{title:n})}),(0,P.jsxs)(Do.__unstableMotion.div,{variants:Ag,transition:{type:"tween"},className:"editor-header__settings",children:[!e&&!h&&(0,P.jsx)(Mu,{forceIsDirty:t}),(0,P.jsx)(Bg,{forceIsAutosaveable:t,disabled:b}),(0,P.jsx)(sd,{className:"editor-header__post-preview-button",forceIsAutosaveable:t}),(0,P.jsx)(Cg,{}),i&&r&&(0,P.jsx)(Ng,{disabled:s}),(r||!g)&&(0,P.jsx)(Hr.Slot,{scope:"core"}),!e&&(0,P.jsx)(Pg,{forceIsDirty:t,setEntitiesSavedStatesCallback:o}),e,(0,P.jsx)(kg,{})]})]})},{PrivateInserterLibrary:Mg}=sn(m.privateApis);function Og(){const{blockSectionRootClientId:e,inserterSidebarToggleRef:t,insertionPoint:s,showMostUsedBlocks:o,sidebarIsOpened:n}=(0,c.useSelect)((e=>{const{getInserterSidebarToggleRef:t,getInsertionPoint:s,isPublishSidebarOpened:o}=sn(e(qi)),{getBlockRootClientId:n,__unstableGetEditorMode:i,getSectionRootClientId:r}=sn(e(m.store)),{get:a}=e(j.store),{getActiveComplementaryArea:l}=e(Nr);return{inserterSidebarToggleRef:t(),insertionPoint:s(),showMostUsedBlocks:a("core","mostUsedBlocks"),blockSectionRootClientId:(()=>{if("zoom-out"===i()){const e=r();if(e)return e}return n()})(),sidebarIsOpened:!(!l("core")&&!o())}}),[]),{setIsInserterOpened:i}=(0,c.useDispatch)(qi),{disableComplementaryArea:r}=(0,c.useDispatch)(Nr),a=(0,p.useViewportMatch)("medium","<"),l=(0,u.useRef)(),d=(0,u.useCallback)((()=>{i(!1),t.current?.focus()}),[t,i]),h=(0,u.useCallback)((e=>{e.keyCode!==aa.ESCAPE||e.defaultPrevented||(e.preventDefault(),d())}),[d]),g=(0,P.jsx)("div",{className:"editor-inserter-sidebar__content",children:(0,P.jsx)(Mg,{showMostUsedBlocks:o,showInserterHelpPanel:!0,shouldFocusBlock:a,rootClientId:null!=e?e:s.rootClientId,__experimentalInsertionIndex:s.insertionIndex,onSelect:s.onSelect,__experimentalInitialTab:s.tab,__experimentalInitialCategory:s.category,__experimentalFilterValue:s.filterValue,onPatternCategorySelection:n?()=>r("core"):void 0,ref:l,onClose:d})});return(0,P.jsx)("div",{onKeyDown:h,className:"editor-inserter-sidebar",children:g})}function Lg(){return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsxs)("div",{className:"editor-list-view-sidebar__outline",children:[(0,P.jsxs)("div",{children:[(0,P.jsx)(Do.__experimentalText,{children:(0,gs.__)("Characters:")}),(0,P.jsx)(Do.__experimentalText,{children:(0,P.jsx)(vp,{})})]}),(0,P.jsxs)("div",{children:[(0,P.jsx)(Do.__experimentalText,{children:(0,gs.__)("Words:")}),(0,P.jsx)(bp,{})]}),(0,P.jsxs)("div",{children:[(0,P.jsx)(Do.__experimentalText,{children:(0,gs.__)("Time to read:")}),(0,P.jsx)(xp,{})]})]}),(0,P.jsx)(xa,{})]})}const{TabbedSidebar:Fg}=sn(m.privateApis);function Vg(){const{setIsListViewOpened:e}=(0,c.useDispatch)(qi),{getListViewToggleRef:t}=sn((0,c.useSelect)(qi)),s=(0,p.useFocusOnMount)("firstElement"),o=(0,u.useCallback)((()=>{e(!1),t().current?.focus()}),[t,e]),n=(0,u.useCallback)((e=>{e.keyCode!==aa.ESCAPE||e.defaultPrevented||(e.preventDefault(),o())}),[o]),[i,r]=(0,u.useState)(null),[a,l]=(0,u.useState)("list-view"),d=(0,u.useRef)(),h=(0,u.useRef)(),g=(0,u.useRef)(),_=(0,p.useMergeRefs)([s,g,r]);const f=(0,u.useCallback)((()=>{d.current.contains(d.current.ownerDocument.activeElement)?o():function(e){const t=Xu.focus.tabbable.find(h.current)[0];if("list-view"===e){const e=Xu.focus.tabbable.find(g.current)[0];(d.current.contains(e)?e:t).focus()}else t.focus()}(a)}),[o,a]);return(0,lr.useShortcut)("core/editor/toggle-list-view",f),(0,P.jsx)("div",{className:"editor-list-view-sidebar",onKeyDown:n,ref:d,children:(0,P.jsx)(Fg,{tabs:[{name:"list-view",title:(0,gs._x)("List View","Post overview"),panel:(0,P.jsx)("div",{className:"editor-list-view-sidebar__list-view-container",children:(0,P.jsx)("div",{className:"editor-list-view-sidebar__list-view-panel-content",children:(0,P.jsx)(m.__experimentalListView,{dropZoneElement:i})})}),panelRef:_},{name:"outline",title:(0,gs._x)("Outline","Post overview"),panel:(0,P.jsx)("div",{className:"editor-list-view-sidebar__list-view-container",children:(0,P.jsx)(Lg,{})})}],onClose:o,onSelect:e=>l(e),defaultTabId:"list-view",ref:h,closeButtonLabel:(0,gs.__)("Close")})})}const{Fill:Ug,Slot:zg}=(0,Do.createSlotFill)("ActionsPanel");function Hg({setEntitiesSavedStatesCallback:e,closeEntitiesSavedStates:t,isEntitiesSavedStatesOpen:s,forceIsDirtyPublishPanel:o}){const{closePublishSidebar:n,togglePublishSidebar:i}=(0,c.useDispatch)(qi),{publishSidebarOpened:r,isPublishable:a,isDirty:l,hasOtherEntitiesChanges:d}=(0,c.useSelect)((e=>{const{isPublishSidebarOpened:t,isEditedPostPublishable:s,isCurrentPostPublished:o,isEditedPostDirty:n,hasNonPostEntityChanges:i}=e(qi),r=i();return{publishSidebarOpened:t(),isPublishable:!o()&&s(),isDirty:r||n(),hasOtherEntitiesChanges:r}}),[]),p=(0,u.useCallback)((()=>e(!0)),[]);let h;return h=r?(0,P.jsx)(vu,{onClose:n,forceIsDirty:o,PrePublishExtension:ql.Slot,PostPublishExtension:zl.Slot}):a&&!d?(0,P.jsx)("div",{className:"editor-layout__toggle-publish-panel",children:(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"secondary",onClick:i,"aria-expanded":!1,children:(0,gs.__)("Open publish panel")})}):(0,P.jsx)("div",{className:"editor-layout__toggle-entities-saved-states-panel",children:(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"secondary",onClick:p,"aria-expanded":!1,disabled:!l,accessibleWhenDisabled:!0,children:(0,gs.__)("Open save panel")})}),(0,P.jsxs)(P.Fragment,{children:[s&&(0,P.jsx)(Fa,{close:t}),(0,P.jsx)(zg,{bubblesVirtually:!0}),!s&&h]})}function Gg({autoFocus:e=!1}){const{switchEditorMode:t}=(0,c.useDispatch)(qi),{shortcut:s,isRichEditingEnabled:o}=(0,c.useSelect)((e=>{const{getEditorSettings:t}=e(qi),{getShortcutRepresentation:s}=e(lr.store);return{shortcut:s("core/editor/toggle-mode"),isRichEditingEnabled:t().richEditingEnabled}}),[]),{resetZoomLevel:n,__unstableSetEditorMode:i}=sn((0,c.useDispatch)(m.store)),r=(0,u.useRef)();return(0,u.useEffect)((()=>{n(),i("edit"),e||r?.current?.focus()}),[e,n,i]),(0,P.jsxs)("div",{className:"editor-text-editor",children:[o&&(0,P.jsxs)("div",{className:"editor-text-editor__toolbar",children:[(0,P.jsx)("h2",{children:(0,gs.__)("Editing code")}),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>t("visual"),shortcut:s,children:(0,gs.__)("Exit code editor")})]}),(0,P.jsxs)("div",{className:"editor-text-editor__body",children:[(0,P.jsx)(ip,{ref:r}),(0,P.jsx)(Qu,{})]})]})}function $g({contentRef:e}){const{onNavigateToEntityRecord:t,templateId:s}=(0,c.useSelect)((e=>{const{getEditorSettings:t,getCurrentTemplateId:s}=e(qi);return{onNavigateToEntityRecord:t().onNavigateToEntityRecord,templateId:s()}}),[]),o=(0,c.useSelect)((e=>!!e(d.store).canUser("create",{kind:"postType",name:"wp_template"})),[]),[n,i]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{const t=e=>{o&&e.target.classList.contains("is-root-container")&&"core/template-part"!==e.target.dataset?.type&&(e.defaultPrevented||(e.preventDefault(),i(!0)))},s=e.current;return s?.addEventListener("dblclick",t),()=>{s?.removeEventListener("dblclick",t)}}),[e,o]),o?(0,P.jsx)(Do.__experimentalConfirmDialog,{isOpen:n,confirmButtonText:(0,gs.__)("Edit template"),onConfirm:()=>{i(!1),t({postId:s,postType:"wp_template"})},onCancel:()=>i(!1),size:"medium",children:(0,gs.__)("You’ve tried to select a block that is part of a template, which may be used on other posts and pages. Would you like to edit the template?")}):null}const Wg=20;function Zg({direction:e,resizeWidthBy:t}){const s=`resizable-editor__resize-help-${e}`;return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(Do.Tooltip,{text:(0,gs.__)("Drag to resize"),children:(0,P.jsx)(Do.__unstableMotion.button,{className:`editor-resizable-editor__resize-handle is-${e}`,"aria-label":(0,gs.__)("Drag to resize"),"aria-describedby":s,onKeyDown:function(s){const{keyCode:o}=s;"left"===e&&o===aa.LEFT||"right"===e&&o===aa.RIGHT?t(Wg):("left"===e&&o===aa.RIGHT||"right"===e&&o===aa.LEFT)&&t(-Wg)},variants:{active:{opacity:1,scaleY:1.3}},whileFocus:"active",whileHover:"active",whileTap:"active",role:"separator","aria-orientation":"vertical"},"handle")}),(0,P.jsx)(Do.VisuallyHidden,{id:s,children:(0,gs.__)("Use left and right arrow keys to resize the canvas.")})]})}const Yg={position:void 0,userSelect:void 0,cursor:void 0,width:void 0,height:void 0,top:void 0,right:void 0,bottom:void 0,left:void 0};const Kg=function({className:e,enableResizing:t,height:s,children:o}){const[n,i]=(0,u.useState)("100%"),r=(0,u.useRef)(),a=(0,u.useCallback)((e=>{r.current&&i(r.current.offsetWidth+e)}),[]);return(0,P.jsx)(Do.ResizableBox,{className:dr("editor-resizable-editor",e,{"is-resizable":t}),ref:e=>{r.current=e?.resizable},size:{width:t?n:"100%",height:t&&s?s:"100%"},onResizeStop:(e,t,s)=>{i(s.style.width)},minWidth:300,maxWidth:"100%",maxHeight:"100%",enable:{left:t,right:t},showHandle:t,resizeRatio:2,handleComponent:{left:(0,P.jsx)(Zg,{direction:"left",resizeWidthBy:a}),right:(0,P.jsx)(Zg,{direction:"right",resizeWidthBy:a})},handleClasses:void 0,handleStyles:{left:Yg,right:Yg},children:o})},qg=500;function Qg(e,t,s){return Math.min(Math.max(e,t),s)}function Xg(e,t,s){const o=e-Qg(e,s.left,s.right),n=t-Qg(t,s.top,s.bottom);return Math.sqrt(o*o+n*n)}function Jg({isEnabled:e=!0}={}){const{getEnabledClientIdsTree:t,getBlockName:s,getBlockOrder:o}=sn((0,c.useSelect)(m.store)),{selectBlock:n}=(0,c.useDispatch)(m.store);return(0,p.useRefEffect)((i=>{if(!e)return;const r=e=>{(e.target===i||e.target.classList.contains("is-root-container"))&&((e,r)=>{const a=t().flatMap((({clientId:e})=>{const t=s(e);if("core/template-part"===t)return[];if("core/post-content"===t){const t=o(e);if(t.length)return t}return[e]}));let l=1/0,c=null;for(const t of a){const s=i.querySelector(`[data-block="${t}"]`);if(!s)continue;const o=Xg(e,r,s.getBoundingClientRect());o<l&&o<qg&&(l=o,c=t)}c&&n(c)})(e.clientX,e.clientY)};return i.addEventListener("click",r),()=>i.removeEventListener("click",r)}),[e])}const{LayoutStyle:e_,useLayoutClasses:t_,useLayoutStyles:s_,ExperimentalBlockCanvas:o_,useFlashEditableBlocks:n_,useZoomOutModeExit:i_}=sn(m.privateApis),r_=[O,R,L,M];function a_(e){for(let t=0;t<e.length;t++){if("core/post-content"===e[t].name)return e[t].attributes;if(e[t].innerBlocks.length){const s=a_(e[t].innerBlocks);if(s)return s}}}function l_(e){for(let t=0;t<e.length;t++)if("core/post-content"===e[t].name)return!0;return!1}const c_=function({autoFocus:e,styles:t,disableIframe:s=!1,iframeProps:o,contentRef:n,className:i}){const[r,a]=(0,p.useResizeObserver)(),l=(0,p.useViewportMatch)("small","<"),h=(0,p.useViewportMatch)("medium","<"),{renderingMode:g,postContentAttributes:_,editedPostTemplate:f={},wrapperBlockName:b,wrapperUniqueId:x,deviceType:v,isFocusedEntity:w,isDesignPostType:S,postType:k,isPreview:C}=(0,c.useSelect)((e=>{const{getCurrentPostId:t,getCurrentPostType:s,getCurrentTemplateId:o,getEditorSettings:n,getRenderingMode:i,getDeviceType:r}=e(qi),{getPostType:a,getEditedEntityRecord:l}=e(d.store),c=s(),u=i();let p;c===O?p="core/block":"post-only"===u&&(p="core/post-content");const h=n(),m=h.supportsTemplateMode,g=a(c),_=o(),f=_?l("postType",R,_):void 0;return{renderingMode:u,postContentAttributes:h.postContentAttributes,isDesignPostType:r_.includes(c),editedPostTemplate:g?.viewable&&m?f:void 0,wrapperBlockName:p,wrapperUniqueId:t(),deviceType:r(),isFocusedEntity:!!h.onNavigateToPreviousEntityRecord,postType:c,isPreview:h.__unstableIsPreviewMode}}),[]),{isCleanNewPost:j}=(0,c.useSelect)(qi),{hasRootPaddingAwareAlignments:E,themeHasDisabledLayoutStyles:T,themeSupportsLayout:B,isZoomedOut:I}=(0,c.useSelect)((e=>{const{getSettings:t,isZoomOut:s}=sn(e(m.store)),o=t();return{themeHasDisabledLayoutStyles:o.disableLayoutStyles,themeSupportsLayout:o.supportsLayout,hasRootPaddingAwareAlignments:o.__experimentalFeatures?.useRootPaddingAwareAlignments,isZoomedOut:s()}}),[]),N=(0,m.__experimentalUseResizeCanvas)(v),[A]=(0,m.useSettings)("layout"),D=(0,u.useMemo)((()=>"post-only"!==g||S?{type:"default"}:B?{...A,type:"constrained"}:{type:"default"}),[g,B,A,S]),F=(0,u.useMemo)((()=>{if(!f?.content&&!f?.blocks&&_)return _;if(f?.blocks)return a_(f?.blocks);const e="string"==typeof f?.content?f?.content:"";return a_((0,y.parse)(e))||{}}),[f?.content,f?.blocks,_]),V=(0,u.useMemo)((()=>{if(!f?.content&&!f?.blocks)return!1;if(f?.blocks)return l_(f?.blocks);const e="string"==typeof f?.content?f?.content:"";return l_((0,y.parse)(e))||!1}),[f?.content,f?.blocks]),{layout:U={},align:z=""}=F||{},H=t_(F,"core/post-content"),G=dr({"is-layout-flow":!B},B&&H,z&&`align${z}`),$=s_(F,"core/post-content",".block-editor-block-list__layout.is-root-container"),W=(0,u.useMemo)((()=>U&&("constrained"===U?.type||U?.inherit||U?.contentSize||U?.wideSize)?{...A,...U,type:"constrained"}:{...A,...U,type:"default"}),[U?.type,U?.inherit,U?.contentSize,U?.wideSize,A]),Z=_?W:D,Y="default"!==Z?.type||V?Z:D,K=(0,m.__unstableUseTypingObserver)(),q=(0,u.useRef)();(0,u.useEffect)((()=>{e&&j()&&q?.current?.focus()}),[e,j]);const Q=(0,u.useRef)(),X=(0,m.__unstableUseTypewriter)();n=(0,p.useMergeRefs)([Q,n,"post-only"===g?X:null,n_({isEnabled:"template-locked"===g}),Jg({isEnabled:"template-locked"===g}),i_()]);const J=I&&!h?{scale:"default",frameSize:"40px"}:{},ee=k===L,te=[L,M,O].includes(k)&&!C&&!l&&!I,se=!s||["Tablet","Mobile"].includes(v),oe=(0,u.useMemo)((()=>[...null!=t?t:[],{css:`:where(.block-editor-iframe__body){display:flow-root;}.is-root-container{display:flow-root;${te?"min-height:0!important;":""}}`}]),[t,te]);return(0,P.jsx)("div",{className:dr("editor-visual-editor","edit-post-visual-editor",i,{"has-padding":w||te,"is-resizable":te,"is-iframed":se}),children:(0,P.jsx)(Kg,{enableResizing:te,height:a.height&&!ee?a.height:"100%",children:(0,P.jsxs)(o_,{shouldIframe:se,contentRef:n,styles:oe,height:"100%",iframeProps:{...o,...J,style:{...o?.style,...N}},children:[B&&!T&&"post-only"===g&&!S&&(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(e_,{selector:".editor-visual-editor__post-title-wrapper",layout:D}),(0,P.jsx)(e_,{selector:".block-editor-block-list__layout.is-root-container",layout:Y}),z&&(0,P.jsx)(e_,{css:".is-root-container.alignwide { max-width: var(--wp--style--global--wide-size); margin-left: auto; margin-right: auto;}\n\t\t.is-root-container.alignwide:where(.is-layout-flow) > :not(.alignleft):not(.alignright) { max-width: var(--wp--style--global--wide-size);}\n\t\t.is-root-container.alignfull { max-width: none; margin-left: auto; margin-right: auto;}\n\t\t.is-root-container.alignfull:where(.is-layout-flow) > :not(.alignleft):not(.alignright) { max-width: none;}"}),$&&(0,P.jsx)(e_,{layout:W,css:$})]}),"post-only"===g&&!S&&(0,P.jsx)("div",{className:dr("editor-visual-editor__post-title-wrapper","edit-post-visual-editor__post-title-wrapper",{"has-global-padding":E}),contentEditable:!1,ref:K,style:{marginTop:"4rem"},children:(0,P.jsx)(np,{ref:q})}),(0,P.jsxs)(m.RecursionProvider,{blockName:b,uniqueId:x,children:[(0,P.jsx)(m.BlockList,{className:dr("is-"+v.toLowerCase()+"-preview","post-only"!==g||S?"wp-site-blocks":`${G} wp-block-post-content`),layout:Z,dropZoneElement:s?Q.current:Q.current?.parentNode,__unstableDisableDropZone:"template-locked"===g}),"template-locked"===g&&(0,P.jsx)($g,{contentRef:Q})]}),te&&r]})})})},d_={header:(0,gs.__)("Editor top bar"),body:(0,gs.__)("Editor content"),sidebar:(0,gs.__)("Editor settings"),actions:(0,gs.__)("Editor publish"),footer:(0,gs.__)("Editor footer")};function u_({className:e,styles:t,children:s,forceIsDirty:o,contentRef:n,disableIframe:i,autoFocus:r,customSaveButton:a,customSavePanel:l,forceDisableBlockTools:d,title:h,iframeProps:g}){const{mode:_,isRichEditingEnabled:f,isInserterOpened:b,isListViewOpened:y,isDistractionFree:x,isPreviewMode:v,showBlockBreadcrumbs:w,documentLabel:S,isZoomOut:k}=(0,c.useSelect)((e=>{const{get:t}=e(j.store),{getEditorSettings:s,getPostTypeLabel:o}=e(qi),n=s(),i=o(),{isZoomOut:r}=sn(e(m.store));return{mode:e(qi).getEditorMode(),isRichEditingEnabled:n.richEditingEnabled,isInserterOpened:e(qi).isInserterOpened(),isListViewOpened:e(qi).isListViewOpened(),isDistractionFree:t("core","distractionFree"),isPreviewMode:n.__unstableIsPreviewMode,showBlockBreadcrumbs:t("core","showBlockBreadcrumbs"),documentLabel:i||(0,gs._x)("Document","noun, breadcrumb"),isZoomOut:r()}}),[]),C=(0,p.useViewportMatch)("medium"),E=y?(0,gs.__)("Document Overview"):(0,gs.__)("Block Library"),[T,B]=(0,u.useState)(!1),I=(0,u.useCallback)((e=>{"function"==typeof T&&T(e),B(!1)}),[T]);return(0,P.jsx)(ta,{isDistractionFree:x,className:dr("editor-editor-interface",e,{"is-entity-save-view-open":!!T,"is-distraction-free":x&&!v}),labels:{...d_,secondarySidebar:E},header:!v&&(0,P.jsx)(Rg,{forceIsDirty:o,setEntitiesSavedStatesCallback:B,customSaveButton:a,forceDisableBlockTools:d,title:h,isEditorIframed:!i}),editorNotices:(0,P.jsx)(Ea,{}),secondarySidebar:!v&&"visual"===_&&(b&&(0,P.jsx)(Og,{})||y&&(0,P.jsx)(Vg,{})),sidebar:!v&&!x&&(0,P.jsx)(Kr.Slot,{scope:"core"}),content:(0,P.jsxs)(P.Fragment,{children:[!x&&!v&&(0,P.jsx)(Ea,{}),(0,P.jsx)(sg.Slot,{children:([e])=>e||(0,P.jsxs)(P.Fragment,{children:[!v&&("text"===_||!f)&&(0,P.jsx)(Gg,{autoFocus:r}),!v&&!C&&"visual"===_&&(0,P.jsx)(m.BlockToolbar,{hideDragHandle:!0}),(v||f&&"visual"===_)&&(0,P.jsx)(c_,{styles:t,contentRef:n,disableIframe:i,autoFocus:r,iframeProps:g}),s]})})]}),footer:!v&&!x&&C&&w&&f&&!k&&"visual"===_&&(0,P.jsx)(m.BlockBreadcrumb,{rootLabelText:S}),actions:v?void 0:l||(0,P.jsx)(Hg,{closeEntitiesSavedStates:I,isEntitiesSavedStatesOpen:T,setEntitiesSavedStatesCallback:B,forceIsDirtyPublishPanel:o})})}const{OverridesPanel:p_}=sn(cn.privateApis);function h_(){return(0,c.useSelect)((e=>"wp_block"===e(qi).getCurrentPostType()),[])?(0,P.jsx)(p_,{}):null}function m_({postType:e,onActionPerformed:t,context:s}){const{defaultActions:o}=(0,c.useSelect)((t=>{const{getEntityActions:s}=sn(t(qi));return{defaultActions:s("postType",e)}}),[e]),{registerPostTypeActions:n}=sn((0,c.useDispatch)(qi));return(0,u.useEffect)((()=>{n(e)}),[n,e]),(0,u.useMemo)((()=>{const e=o.filter((e=>!e.context||e.context===s));if(t)for(let s=0;s<e.length;++s){if(e[s].callback){const o=e[s].callback;e[s]={...e[s],callback:(n,i)=>{o(n,{...i,onActionPerformed:o=>{i?.onActionPerformed&&i.onActionPerformed(o),t(e[s].id,o)}})}}}if(e[s].RenderModal){const o=e[s].RenderModal;e[s]={...e[s],RenderModal:n=>(0,P.jsx)(o,{...n,onActionPerformed:o=>{n.onActionPerformed&&n.onActionPerformed(o),t(e[s].id,o)}})}}}return e}),[o,t,s])}const{DropdownMenuV2:g_,kebabCase:__}=sn(Do.privateApis);function f_({postType:e,postId:t,onActionPerformed:s}){const[o,n]=(0,u.useState)(!1),{item:i,permissions:r}=(0,c.useSelect)((s=>{const{getEditedEntityRecord:o,getEntityRecordPermissions:n}=sn(s(d.store));return{item:o("postType",e,t),permissions:n("postType",e,t)}}),[t,e]),a=(0,u.useMemo)((()=>({...i,permissions:r})),[i,r]),l=m_({postType:e,onActionPerformed:s}),p=(0,u.useMemo)((()=>l.filter((e=>!e.isEligible||e.isEligible(a)))),[l,a]);return(0,P.jsx)(g_,{open:o,trigger:(0,P.jsx)(Do.Button,{size:"small",icon:mg,label:(0,gs.__)("Actions"),disabled:!p.length,accessibleWhenDisabled:!0,className:"editor-all-actions-button",onClick:()=>n(!o)}),onOpenChange:n,placement:"bottom-end",children:(0,P.jsx)(x_,{actions:p,item:a,onClose:()=>{n(!1)}})})}function b_({action:e,onClick:t,items:s}){const o="string"==typeof e.label?e.label:e.label(s);return(0,P.jsx)(g_.Item,{onClick:t,hideOnClick:!e.RenderModal,children:(0,P.jsx)(g_.ItemLabel,{children:o})})}function y_({action:e,item:t,ActionTrigger:s,onClose:o}){const[n,i]=(0,u.useState)(!1),r={action:e,onClick:()=>i(!0),items:[t]},{RenderModal:a,hideModalHeader:l}=e;return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(s,{...r}),n&&(0,P.jsx)(Do.Modal,{title:e.modalHeader||e.label,__experimentalHideHeader:!!l,onRequestClose:()=>{i(!1)},overlayClassName:`editor-action-modal editor-action-modal__${__(e.id)}`,focusOnMount:"firstContentElement",size:"small",children:(0,P.jsx)(a,{items:[t],closeModal:()=>{i(!1),o()}})})]})}function x_({actions:e,item:t,onClose:s}){return(0,P.jsx)(g_.Group,{children:e.map((e=>e.RenderModal?(0,P.jsx)(y_,{action:e,item:t,ActionTrigger:b_,onClose:s},e.id):(0,P.jsx)(b_,{action:e,onClick:()=>e.callback([t]),items:[t]},e.id)))})}function v_({postType:e,postId:t,onActionPerformed:s}){const{isFrontPage:o,isPostsPage:n,title:i,icon:r,isSync:a}=(0,c.useSelect)((s=>{const{__experimentalGetTemplateInfo:o}=s(qi),{canUser:n,getEditedEntityRecord:i}=s(d.store),r=n("read",{kind:"root",name:"site"})?i("root","site"):void 0,a=i("postType",e,t),l=[R,M].includes(e)&&o(a);let c=!1;if(U.includes(e))if(O===e){c="unsynced"!==("unsynced"===a?.meta?.wp_pattern_sync_status?"unsynced":a?.wp_pattern_sync_status)}else c=!0;return{title:l?.title||a?.title,icon:sn(s(qi)).getPostIcon(e,{area:a?.area}),isSync:c,isFrontPage:r?.page_on_front===t,isPostsPage:r?.page_for_posts===t}}),[t,e]);return(0,P.jsx)("div",{className:"editor-post-card-panel",children:(0,P.jsxs)(Do.__experimentalHStack,{spacing:2,className:"editor-post-card-panel__header",align:"flex-start",children:[(0,P.jsx)(Do.Icon,{className:dr("editor-post-card-panel__icon",{"is-sync":a}),icon:r}),(0,P.jsxs)(Do.__experimentalText,{numberOfLines:2,truncate:!0,className:"editor-post-card-panel__title",weight:500,as:"h2",lineHeight:"20px",children:[i?(0,Ao.decodeEntities)(i):(0,gs.__)("No title"),o&&(0,P.jsx)("span",{className:"editor-post-card-panel__title-badge",children:(0,gs.__)("Homepage")}),n&&(0,P.jsx)("span",{className:"editor-post-card-panel__title-badge",children:(0,gs.__)("Posts Page")})]}),(0,P.jsx)(f_,{postType:e,postId:t,onActionPerformed:s})]})})}const w_=189;function S_(){const{postContent:e}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t,getCurrentPostType:s,getCurrentPostId:o}=e(qi),{canUser:n}=e(d.store),{getEntityRecord:i}=e(d.store),r=n("read",{kind:"root",name:"site"})?i("root","site"):void 0,a=s();return{postContent:!(+o()===r?.page_for_posts)&&![R,M].includes(a)&&t("content")}}),[]),t=(0,gs._x)("words","Word count type. Do not translate!"),s=(0,u.useMemo)((()=>e?(0,fp.count)(e,t):0),[e,t]);if(!s)return null;const o=Math.round(s/w_),n=(0,gs.sprintf)((0,gs._n)("%s word","%s words",s),s.toLocaleString()),i=o<=1?(0,gs.__)("1 minute"):(0,gs.sprintf)((0,gs._n)("%s minute","%s minutes",o),o.toLocaleString());return(0,P.jsx)("div",{className:"editor-post-content-information",children:(0,P.jsx)(Do.__experimentalText,{children:(0,gs.sprintf)((0,gs.__)("%1$s, %2$s read time."),n,i)})})}const k_=function(){const{postFormat:e}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(qi),s=t("format");return{postFormat:null!=s?s:"standard"}}),[]),t=Wc.find((t=>t.id===e)),[s,o]=(0,u.useState)(null),n=(0,u.useMemo)((()=>({anchor:s,placement:"left-start",offset:36,shift:!0})),[s]);return(0,P.jsx)($c,{children:(0,P.jsx)(tl,{label:(0,gs.__)("Format"),ref:o,children:(0,P.jsx)(Do.Dropdown,{popoverProps:n,contentClassName:"editor-post-format__dialog",focusOnMount:!0,renderToggle:({isOpen:e,onToggle:s})=>(0,P.jsx)(Do.Button,{size:"compact",variant:"tertiary","aria-expanded":e,"aria-label":(0,gs.sprintf)((0,gs.__)("Change format: %s"),t?.caption),onClick:s,children:t?.caption}),renderContent:({onClose:e})=>(0,P.jsxs)("div",{className:"editor-post-format__dialog-content",children:[(0,P.jsx)(m.__experimentalInspectorPopoverHeader,{title:(0,gs.__)("Format"),onClose:e}),(0,P.jsx)(Zc,{})]})})})})};function P_(){const e=(0,c.useSelect)((e=>e(qi).getEditedPostAttribute("modified")),[]),t=e&&(0,gs.sprintf)((0,gs.__)("Last edited %s."),(0,x.humanTimeDiff)(e));return t?(0,P.jsx)("div",{className:"editor-post-last-edited-panel",children:(0,P.jsx)(Do.__experimentalText,{children:t})}):null}const C_=function({className:e,children:t}){return(0,P.jsx)(Do.__experimentalVStack,{className:dr("editor-post-panel__section",e),children:t})},j_={};function E_(){const{editEntityRecord:e}=(0,c.useDispatch)(d.store),{postsPageTitle:t,postsPageId:s,isTemplate:o,postSlug:n}=(0,c.useSelect)((e=>{const{getEntityRecord:t,getEditedEntityRecord:s,canUser:o}=e(d.store),n=o("read",{kind:"root",name:"site"})?t("root","site"):void 0,i=n?.page_for_posts?s("postType","page",n?.page_for_posts):j_,{getEditedPostAttribute:r,getCurrentPostType:a}=e(qi);return{postsPageId:i?.id,postsPageTitle:i?.title,isTemplate:a()===R,postSlug:r("slug")}}),[]),[i,r]=(0,u.useState)(null),a=(0,u.useMemo)((()=>({anchor:i,placement:"left-start",offset:36,shift:!0})),[i]);if(!o||!["home","index"].includes(n)||!s)return null;const l=t=>{e("postType","page",s,{title:t})},h=(0,Ao.decodeEntities)(t);return(0,P.jsx)(tl,{label:(0,gs.__)("Blog title"),ref:r,children:(0,P.jsx)(Do.Dropdown,{popoverProps:a,contentClassName:"editor-blog-title-dropdown__content",focusOnMount:!0,renderToggle:({isOpen:e,onToggle:t})=>(0,P.jsx)(Do.Button,{size:"compact",variant:"tertiary","aria-expanded":e,"aria-label":(0,gs.sprintf)((0,gs.__)("Change blog title: %s"),h),onClick:t,children:h}),renderContent:({onClose:e})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(m.__experimentalInspectorPopoverHeader,{title:(0,gs.__)("Blog title"),onClose:e}),(0,P.jsx)(Do.__experimentalInputControl,{placeholder:(0,gs.__)("No title"),size:"__unstable-large",value:t,onChange:(0,p.debounce)(l,300),label:(0,gs.__)("Blog title"),help:(0,gs.__)("Set the Posts Page title. Appears in search results, and when the page is shared on social media."),hideLabelFromVision:!0})]})})})}function T_(){const{editEntityRecord:e}=(0,c.useDispatch)(d.store),{postsPerPage:t,isTemplate:s,postSlug:o}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t,getCurrentPostType:s}=e(qi),{getEditedEntityRecord:o,canUser:n}=e(d.store),i=n("read",{kind:"root",name:"site"})?o("root","site"):void 0;return{isTemplate:s()===R,postSlug:t("slug"),postsPerPage:i?.posts_per_page||1}}),[]),[n,i]=(0,u.useState)(null),r=(0,u.useMemo)((()=>({anchor:n,placement:"left-start",offset:36,shift:!0})),[n]);if(!s||!["home","index"].includes(o))return null;const a=t=>{e("root","site",void 0,{posts_per_page:t})};return(0,P.jsx)(tl,{label:(0,gs.__)("Posts per page"),ref:i,children:(0,P.jsx)(Do.Dropdown,{popoverProps:r,contentClassName:"editor-posts-per-page-dropdown__content",focusOnMount:!0,renderToggle:({isOpen:e,onToggle:s})=>(0,P.jsx)(Do.Button,{size:"compact",variant:"tertiary","aria-expanded":e,"aria-label":(0,gs.__)("Change posts per page"),onClick:s,children:t}),renderContent:({onClose:e})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(m.__experimentalInspectorPopoverHeader,{title:(0,gs.__)("Posts per page"),onClose:e}),(0,P.jsx)(Do.__experimentalNumberControl,{placeholder:0,value:t,size:"__unstable-large",spinControls:"custom",step:"1",min:"1",onChange:a,label:(0,gs.__)("Posts per page"),help:(0,gs.__)("Set the default number of posts to display on blog pages, including categories and tags. Some templates may override this setting."),hideLabelFromVision:!0})]})})})}const B_=[{label:(0,gs._x)("Open",'Adjective: e.g. "Comments are open"'),value:"open",description:(0,gs.__)("Visitors can add new comments and replies.")},{label:(0,gs.__)("Closed"),value:"",description:[(0,gs.__)("Visitors cannot add new comments or replies."),(0,gs.__)("Existing comments remain visible.")].join(" ")}];function I_(){const{editEntityRecord:e}=(0,c.useDispatch)(d.store),{allowCommentsOnNewPosts:t,isTemplate:s,postSlug:o}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t,getCurrentPostType:s}=e(qi),{getEditedEntityRecord:o,canUser:n}=e(d.store),i=n("read",{kind:"root",name:"site"})?o("root","site"):void 0;return{isTemplate:s()===R,postSlug:t("slug"),allowCommentsOnNewPosts:i?.default_comment_status||""}}),[]),[n,i]=(0,u.useState)(null),r=(0,u.useMemo)((()=>({anchor:n,placement:"left-start",offset:36,shift:!0})),[n]);if(!s||!["home","index"].includes(o))return null;const a=t=>{e("root","site",void 0,{default_comment_status:t?"open":null})};return(0,P.jsx)(tl,{label:(0,gs.__)("Discussion"),ref:i,children:(0,P.jsx)(Do.Dropdown,{popoverProps:r,contentClassName:"editor-site-discussion-dropdown__content",focusOnMount:!0,renderToggle:({isOpen:e,onToggle:s})=>(0,P.jsx)(Do.Button,{size:"compact",variant:"tertiary","aria-expanded":e,"aria-label":(0,gs.__)("Change discussion settings"),onClick:s,children:t?(0,gs.__)("Comments open"):(0,gs.__)("Comments closed")}),renderContent:({onClose:e})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(m.__experimentalInspectorPopoverHeader,{title:(0,gs.__)("Discussion"),onClose:e}),(0,P.jsxs)(Do.__experimentalVStack,{spacing:3,children:[(0,P.jsx)(Do.__experimentalText,{children:(0,gs.__)("Changes will apply to new posts only. Individual posts may override these settings.")}),(0,P.jsx)(Do.RadioControl,{className:"editor-site-discussion__options",hideLabelFromVision:!0,label:(0,gs.__)("Comment status"),options:B_,onChange:a,selected:t})]})]})})})}function N_({onActionPerformed:e}){const{isRemovedPostStatusPanel:t,postType:s,postId:o}=(0,c.useSelect)((e=>{const{isEditorPanelRemoved:t,getCurrentPostType:s,getCurrentPostId:o}=e(qi);return{isRemovedPostStatusPanel:t("post-status"),postType:s(),postId:o()}}),[]);return(0,P.jsx)(C_,{className:"editor-post-summary",children:(0,P.jsx)(Wl.Slot,{children:n=>(0,P.jsx)(P.Fragment,{children:(0,P.jsxs)(Do.__experimentalVStack,{spacing:4,children:[(0,P.jsx)(v_,{postType:s,postId:o,onActionPerformed:e}),(0,P.jsx)(Gc,{withPanelBody:!1}),(0,P.jsx)(Nc,{}),(0,P.jsxs)(Do.__experimentalVStack,{spacing:1,children:[(0,P.jsx)(S_,{}),(0,P.jsx)(P_,{})]}),!t&&(0,P.jsxs)(Do.__experimentalVStack,{spacing:4,children:[(0,P.jsxs)(Do.__experimentalVStack,{spacing:1,children:[(0,P.jsx)(Ru,{}),(0,P.jsx)(Fu,{}),(0,P.jsx)(hp,{}),(0,P.jsx)(gc,{}),(0,P.jsx)(rc,{}),(0,P.jsx)(wc,{}),(0,P.jsx)(qc,{}),(0,P.jsx)(ml,{}),(0,P.jsx)(Gu,{}),(0,P.jsx)(E_,{}),(0,P.jsx)(T_,{}),(0,P.jsx)(I_,{}),(0,P.jsx)(k_,{}),n]}),(0,P.jsx)(ap,{onActionPerformed:e})]})]})})})})}const{EXCLUDED_PATTERN_SOURCES:A_,PATTERN_TYPES:D_}=sn(cn.privateApis);function R_(e,t){return e.innerBlocks=e.innerBlocks.map((e=>R_(e,t))),"core/template-part"===e.name&&void 0===e.attributes.theme&&(e.attributes.theme=t),e}function M_(e,t){return e.filter(((e,s,o)=>((e,t,s)=>t===s.findIndex((t=>e.name===t.name)))(e,s,o)&&(e=>!A_.includes(e.source))(e)&&(e=>e.templateTypes?.includes(t.slug)||e.blockTypes?.includes("core/template-part/"+t.area))(e)))}function O_(e,t){return e.map((e=>({...e,keywords:e.keywords||[],type:D_.theme,blocks:(0,y.parse)(e.content,{__unstableSkipMigrationLogs:!0}).map((e=>R_(e,t)))})))}function L_({availableTemplates:e,onSelect:t}){const s=(0,p.useAsyncList)(e);return e&&0!==e?.length?(0,P.jsx)(m.__experimentalBlockPatternsList,{label:(0,gs.__)("Templates"),blockPatterns:e,shownPatterns:s,onClickPattern:t,showTitlesAsTooltip:!0}):null}function F_(){const{record:e,postType:t,postId:s}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(qi),{getEditedEntityRecord:o}=e(d.store),n=t(),i=s();return{postType:n,postId:i,record:o("postType",n,i)}}),[]),{editEntityRecord:o}=(0,c.useDispatch)(d.store),n=function(e){const{blockPatterns:t,restBlockPatterns:s,currentThemeStylesheet:o}=(0,c.useSelect)((e=>{var t;const{getEditorSettings:s}=e(qi),o=s();return{blockPatterns:null!==(t=o.__experimentalAdditionalBlockPatterns)&&void 0!==t?t:o.__experimentalBlockPatterns,restBlockPatterns:e(d.store).getBlockPatterns(),currentThemeStylesheet:e(d.store).getCurrentTheme().stylesheet}}),[]);return(0,u.useMemo)((()=>O_(M_([...t||[],...s||[]],e),e)),[t,s,e,o])}(e);return n?.length?(0,P.jsx)(Do.PanelBody,{title:(0,gs.__)("Design"),initialOpen:e.type===M,children:(0,P.jsx)(L_,{availableTemplates:n,onSelect:async e=>{await o("postType",t,s,{blocks:e.blocks,content:(0,y.serialize)(e.blocks)})}})}):null}function V_(){const{postType:e}=(0,c.useSelect)((e=>{const{getCurrentPostType:t}=e(qi);return{postType:t()}}),[]);return[M,R].includes(e)?(0,P.jsx)(F_,{}):null}const U_={document:"edit-post/document",block:"edit-post/block"},{Tabs:z_}=sn(Do.privateApis),H_=(0,u.forwardRef)(((e,t)=>{const{documentLabel:s}=(0,c.useSelect)((e=>{const{getPostTypeLabel:t}=e(qi);return{documentLabel:t()||(0,gs._x)("Document","noun, sidebar")}}),[]);return(0,P.jsxs)(z_.TabList,{ref:t,children:[(0,P.jsx)(z_.Tab,{tabId:U_.document,"data-tab-id":U_.document,children:s}),(0,P.jsx)(z_.Tab,{tabId:U_.block,"data-tab-id":U_.block,children:(0,gs.__)("Block")})]})})),{BlockQuickNavigation:G_}=sn(m.privateApis),$_=["core/post-title","core/post-featured-image","core/post-content"];function W_(){const e=(0,u.useMemo)((()=>(0,h.applyFilters)("editor.postContentBlockTypes",$_)),[]),{clientIds:t,postType:s,renderingMode:o}=(0,c.useSelect)((t=>{const{getCurrentPostType:s,getPostBlocksByName:o,getRenderingMode:n}=sn(t(qi)),i=s();return{postType:i,clientIds:o(R===i?"core/template-part":e),renderingMode:n()}}),[e]),{enableComplementaryArea:n}=(0,c.useDispatch)(Nr);return"post-only"===o&&s!==R||0===t.length?null:(0,P.jsx)(Do.PanelBody,{title:(0,gs.__)("Content"),children:(0,P.jsx)(G_,{clientIds:t,onSelect:()=>{n("core","edit-post/document")}})})}const{BlockQuickNavigation:Z_}=sn(m.privateApis);function Y_(){const e=(0,c.useSelect)((e=>{const{getBlockTypes:t}=e(y.store);return t()}),[]),t=(0,u.useMemo)((()=>e.filter((e=>"theme"===e.category)).map((({name:e})=>e))),[e]),s=(0,c.useSelect)((e=>{const{getBlocksByName:s}=e(m.store);return s(t)}),[t]);return 0===s.length?null:(0,P.jsx)(Do.PanelBody,{title:(0,gs.__)("Content"),children:(0,P.jsx)(Z_,{clientIds:s})})}function K_(){const e=(0,c.useSelect)((e=>{const{getCurrentPostType:t}=e(qi);return t()}),[]);return e!==M?null:(0,P.jsx)(Y_,{})}const q_=function(){const{hasBlockSelection:e}=(0,c.useSelect)((e=>({hasBlockSelection:!!e(m.store).getBlockSelectionStart()})),[]),{getActiveComplementaryArea:t}=(0,c.useSelect)(Nr),{enableComplementaryArea:s}=(0,c.useDispatch)(Nr),{get:o}=(0,c.useSelect)(j.store);(0,u.useEffect)((()=>{const n=t("core"),i=["edit-post/document","edit-post/block"].includes(n),r=o("core","distractionFree");i&&!r&&s("core",e?"edit-post/block":"edit-post/document")}),[e,t,s,o])},{Tabs:Q_}=sn(Do.privateApis),X_=u.Platform.select({web:!0,native:!1}),J_=({tabName:e,keyboardShortcut:t,onActionPerformed:s,extraPanels:o})=>{const n=(0,u.useRef)(null),i=(0,u.useContext)(Q_.Context);return(0,u.useEffect)((()=>{const t=Array.from(n.current?.querySelectorAll('[role="tab"]')||[]),s=t.find((t=>t.getAttribute("data-tab-id")===e)),o=s?.ownerDocument.activeElement;t.some((e=>o&&o.id===e.id))&&s&&s.id!==o?.id&&s?.focus()}),[e]),(0,P.jsx)(Xl,{identifier:e,header:(0,P.jsx)(Q_.Context.Provider,{value:i,children:(0,P.jsx)(H_,{ref:n})}),closeLabel:(0,gs.__)("Close Settings"),className:"editor-sidebar__panel",headerClassName:"editor-sidebar__panel-tabs",title:(0,gs._x)("Settings","sidebar button label"),toggleShortcut:t,icon:(0,gs.isRTL)()?oh:nh,isActiveByDefault:X_,children:(0,P.jsxs)(Q_.Context.Provider,{value:i,children:[(0,P.jsxs)(Q_.TabPanel,{tabId:U_.document,focusable:!1,children:[(0,P.jsx)(N_,{onActionPerformed:s}),(0,P.jsx)(Ml.Slot,{}),(0,P.jsx)(W_,{}),(0,P.jsx)(K_,{}),(0,P.jsx)(V_,{}),(0,P.jsx)(Ku,{}),(0,P.jsx)(h_,{}),o]}),(0,P.jsx)(Q_.TabPanel,{tabId:U_.block,focusable:!1,children:(0,P.jsx)(m.BlockInspector,{})})]})})},ef=({extraPanels:e,onActionPerformed:t})=>{q_();const{tabName:s,keyboardShortcut:o,showSummary:n}=(0,c.useSelect)((e=>{const t=e(lr.store).getShortcutRepresentation("core/editor/toggle-sidebar"),s=e(Nr).getActiveComplementaryArea("core");let o=s;return[U_.block,U_.document].includes(s)||(o=e(m.store).getBlockSelectionStart()?U_.block:U_.document),{tabName:o,keyboardShortcut:t,showSummary:![R,M,L].includes(e(qi).getCurrentPostType())}}),[]),{enableComplementaryArea:i}=(0,c.useDispatch)(Nr),r=(0,u.useCallback)((e=>{e&&i("core",e)}),[i]);return(0,P.jsx)(Q_,{selectedTabId:s,onSelect:r,selectOnMove:!1,children:(0,P.jsx)(J_,{tabName:s,keyboardShortcut:o,showSummary:n,onActionPerformed:t,extraPanels:e})})};const tf=function({postType:e,postId:t,templateId:s,settings:o,children:n,initialEdits:i,onActionPerformed:r,extraContent:a,extraSidebarPanels:l,...u}){const{post:p,template:h,hasLoadedPost:m}=(0,c.useSelect)((o=>{const{getEntityRecord:n,hasFinishedResolution:i}=o(d.store);return{post:n("postType",e,t),template:s?n("postType",R,s):void 0,hasLoadedPost:i("getEntityRecord",["postType",e,t])}}),[e,t,s]);return(0,P.jsxs)(P.Fragment,{children:[m&&!p&&(0,P.jsx)(Do.Notice,{status:"warning",isDismissible:!1,children:(0,gs.__)("You attempted to edit an item that doesn't exist. Perhaps it was deleted?")}),!!p&&(0,P.jsxs)(Yh,{post:p,__unstableTemplate:h,settings:o,initialEdits:i,useSubRegistry:!1,children:[(0,P.jsx)(u_,{...u,children:a}),n,(0,P.jsx)(ef,{onActionPerformed:r,extraPanels:l})]})]})},{PreferenceBaseOption:sf}=sn(j.privateApis),of=(0,p.compose)((0,c.withSelect)((e=>({isChecked:e(qi).isPublishSidebarEnabled()}))),(0,c.withDispatch)((e=>{const{enablePublishSidebar:t,disablePublishSidebar:s}=e(qi);return{onChange:e=>e?t():s()}})))(sf);const nf=function({blockTypes:e,value:t,onItemChange:s}){return(0,P.jsx)("ul",{className:"editor-block-manager__checklist",children:e.map((e=>(0,P.jsxs)("li",{className:"editor-block-manager__checklist-item",children:[(0,P.jsx)(Do.CheckboxControl,{__nextHasNoMarginBottom:!0,label:e.title,checked:t.includes(e.name),onChange:(...t)=>s(e.name,...t)}),(0,P.jsx)(m.BlockIcon,{icon:e.icon})]},e.name)))})};const rf=function e({title:t,blockTypes:s}){const o=(0,p.useInstanceId)(e),{allowedBlockTypes:n,hiddenBlockTypes:i}=(0,c.useSelect)((e=>{const{getEditorSettings:t}=e(qi),{get:s}=e(j.store);return{allowedBlockTypes:t().allowedBlockTypes,hiddenBlockTypes:s("core","hiddenBlockTypes")}}),[]),r=(0,u.useMemo)((()=>!0===n?s:s.filter((({name:e})=>n?.includes(e)))),[n,s]),{showBlockTypes:a,hideBlockTypes:l}=sn((0,c.useDispatch)(qi)),d=(0,u.useCallback)(((e,t)=>{t?a(e):l(e)}),[a,l]),h=(0,u.useCallback)((e=>{const t=s.map((({name:e})=>e));e?a(t):l(t)}),[s,a,l]);if(!r.length)return null;const m=r.map((({name:e})=>e)).filter((e=>!(null!=i?i:[]).includes(e))),g="editor-block-manager__category-title-"+o,_=m.length===r.length,f=!_&&m.length>0;return(0,P.jsxs)("div",{role:"group","aria-labelledby":g,className:"editor-block-manager__category",children:[(0,P.jsx)(Do.CheckboxControl,{__nextHasNoMarginBottom:!0,checked:_,onChange:h,className:"editor-block-manager__category-title",indeterminate:f,label:(0,P.jsx)("span",{id:g,children:t})}),(0,P.jsx)(nf,{blockTypes:r,value:m,onItemChange:d})]})};function af(){const e=(0,p.useDebounce)(us.speak,500),[t,s]=(0,u.useState)(""),{showBlockTypes:o}=sn((0,c.useDispatch)(qi)),{blockTypes:n,categories:i,hasBlockSupport:r,isMatchingSearchTerm:a,numberOfHiddenBlocks:l}=(0,c.useSelect)((e=>{var t;const s=e(y.store).getBlockTypes(),o=(null!==(t=e(j.store).get("core","hiddenBlockTypes"))&&void 0!==t?t:[]).filter((e=>s.some((t=>t.name===e))));return{blockTypes:s,categories:e(y.store).getCategories(),hasBlockSupport:e(y.store).hasBlockSupport,isMatchingSearchTerm:e(y.store).isMatchingSearchTerm,numberOfHiddenBlocks:Array.isArray(o)&&o.length}}),[]);const d=n.filter((e=>r(e,"inserter",!0)&&(!t||a(e,t))&&(!e.parent||e.parent.includes("core/post-content"))));return(0,u.useEffect)((()=>{if(!t)return;const s=d.length,o=(0,gs.sprintf)((0,gs._n)("%d result found.","%d results found.",s),s);e(o)}),[d?.length,t,e]),(0,P.jsxs)("div",{className:"editor-block-manager__content",children:[!!l&&(0,P.jsxs)("div",{className:"editor-block-manager__disabled-blocks-count",children:[(0,gs.sprintf)((0,gs._n)("%d block is hidden.","%d blocks are hidden.",l),l),(0,P.jsx)(Do.Button,{__next40pxDefaultSize:!0,variant:"link",onClick:()=>function(e){const t=e.map((({name:e})=>e));o(t)}(d),children:(0,gs.__)("Reset")})]}),(0,P.jsx)(Do.SearchControl,{__nextHasNoMarginBottom:!0,label:(0,gs.__)("Search for a block"),placeholder:(0,gs.__)("Search for a block"),value:t,onChange:e=>s(e),className:"editor-block-manager__search"}),(0,P.jsxs)("div",{tabIndex:"0",role:"region","aria-label":(0,gs.__)("Available block types"),className:"editor-block-manager__results",children:[0===d.length&&(0,P.jsx)("p",{className:"editor-block-manager__no-results",children:(0,gs.__)("No blocks found.")}),i.map((e=>(0,P.jsx)(rf,{title:e.title,blockTypes:d.filter((t=>t.category===e.slug))},e.slug))),(0,P.jsx)(rf,{title:(0,gs.__)("Uncategorized"),blockTypes:d.filter((({category:e})=>!e))})]})]})}const{PreferencesModal:lf,PreferencesModalTabs:cf,PreferencesModalSection:df,PreferenceToggleControl:uf}=sn(j.privateApis);function pf({extraSections:e={}}){const t=(0,p.useViewportMatch)("medium"),s=(0,c.useSelect)((e=>{const{getEditorSettings:s}=e(qi),{get:o}=e(j.store),n=s().richEditingEnabled;return!o("core","distractionFree")&&t&&n}),[t]),{setIsListViewOpened:o,setIsInserterOpened:n}=(0,c.useDispatch)(qi),{set:i}=(0,c.useDispatch)(j.store),r=!!xh().length,a=(0,u.useMemo)((()=>[{name:"general",tabLabel:(0,gs.__)("General"),content:(0,P.jsxs)(P.Fragment,{children:[(0,P.jsxs)(df,{title:(0,gs.__)("Interface"),children:[(0,P.jsx)(uf,{scope:"core",featureName:"showListViewByDefault",help:(0,gs.__)("Opens the List View sidebar by default."),label:(0,gs.__)("Always open List View")}),s&&(0,P.jsx)(uf,{scope:"core",featureName:"showBlockBreadcrumbs",help:(0,gs.__)("Display the block hierarchy trail at the bottom of the editor."),label:(0,gs.__)("Show block breadcrumbs")}),(0,P.jsx)(uf,{scope:"core",featureName:"allowRightClickOverrides",help:(0,gs.__)("Allows contextual List View menus via right-click, overriding browser defaults."),label:(0,gs.__)("Allow right-click contextual menus")}),r&&(0,P.jsx)(uf,{scope:"core",featureName:"enableChoosePatternModal",help:(0,gs.__)("Shows starter patterns when creating a new page."),label:(0,gs.__)("Show starter patterns")})]}),(0,P.jsxs)(df,{title:(0,gs.__)("Document settings"),description:(0,gs.__)("Select what settings are shown in the document panel."),children:[(0,P.jsx)(Nl.Slot,{}),(0,P.jsx)(Wu,{taxonomyWrapper:(e,t)=>(0,P.jsx)(El,{label:t.labels.menu_name,panelName:`taxonomy-panel-${t.slug}`})}),(0,P.jsx)(Rc,{children:(0,P.jsx)(El,{label:(0,gs.__)("Featured image"),panelName:"featured-image"})}),(0,P.jsx)(kc,{children:(0,P.jsx)(El,{label:(0,gs.__)("Excerpt"),panelName:"post-excerpt"})}),(0,P.jsx)(qa,{supportKeys:["comments","trackbacks"],children:(0,P.jsx)(El,{label:(0,gs.__)("Discussion"),panelName:"discussion-panel"})}),(0,P.jsx)(Ka,{children:(0,P.jsx)(El,{label:(0,gs.__)("Page attributes"),panelName:"page-attributes"})})]}),t&&(0,P.jsx)(df,{title:(0,gs.__)("Publishing"),children:(0,P.jsx)(of,{help:(0,gs.__)("Review settings, such as visibility and tags."),label:(0,gs.__)("Enable pre-publish checks")})}),e?.general]})},{name:"appearance",tabLabel:(0,gs.__)("Appearance"),content:(0,P.jsxs)(df,{title:(0,gs.__)("Appearance"),description:(0,gs.__)("Customize the editor interface to suit your needs."),children:[(0,P.jsx)(uf,{scope:"core",featureName:"fixedToolbar",onToggle:()=>i("core","distractionFree",!1),help:(0,gs.__)("Access all block and document tools in a single place."),label:(0,gs.__)("Top toolbar")}),(0,P.jsx)(uf,{scope:"core",featureName:"distractionFree",onToggle:()=>{i("core","fixedToolbar",!0),n(!1),o(!1)},help:(0,gs.__)("Reduce visual distractions by hiding the toolbar and other elements to focus on writing."),label:(0,gs.__)("Distraction free")}),(0,P.jsx)(uf,{scope:"core",featureName:"focusMode",help:(0,gs.__)("Highlights the current block and fades other content."),label:(0,gs.__)("Spotlight mode")}),e?.appearance]})},{name:"accessibility",tabLabel:(0,gs.__)("Accessibility"),content:(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(df,{title:(0,gs.__)("Navigation"),description:(0,gs.__)("Optimize the editing experience for enhanced control."),children:(0,P.jsx)(uf,{scope:"core",featureName:"keepCaretInsideBlock",help:(0,gs.__)("Keeps the text cursor within the block boundaries, aiding users with screen readers by preventing unintentional cursor movement outside the block."),label:(0,gs.__)("Contain text cursor inside block")})}),(0,P.jsx)(df,{title:(0,gs.__)("Interface"),children:(0,P.jsx)(uf,{scope:"core",featureName:"showIconLabels",label:(0,gs.__)("Show button text labels"),help:(0,gs.__)("Show text instead of icons on buttons across the interface.")})})]})},{name:"blocks",tabLabel:(0,gs.__)("Blocks"),content:(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(df,{title:(0,gs.__)("Inserter"),children:(0,P.jsx)(uf,{scope:"core",featureName:"mostUsedBlocks",help:(0,gs.__)("Adds a category with the most frequently used blocks in the inserter."),label:(0,gs.__)("Show most used blocks")})}),(0,P.jsx)(df,{title:(0,gs.__)("Manage block visibility"),description:(0,gs.__)("Disable blocks that you don't want to appear in the inserter. They can always be toggled back on later."),children:(0,P.jsx)(af,{})})]})},window.__experimentalMediaProcessing&&{name:"media",tabLabel:(0,gs.__)("Media"),content:(0,P.jsx)(P.Fragment,{children:(0,P.jsxs)(df,{title:(0,gs.__)("General"),description:(0,gs.__)("Customize options related to the media upload flow."),children:[(0,P.jsx)(uf,{scope:"core/media",featureName:"optimizeOnUpload",help:(0,gs.__)("Compress media items before uploading to the server."),label:(0,gs.__)("Pre-upload compression")}),(0,P.jsx)(uf,{scope:"core/media",featureName:"requireApproval",help:(0,gs.__)("Require approval step when optimizing existing media."),label:(0,gs.__)("Approval step")})]})})}].filter(Boolean)),[s,e,n,o,i,t,r]);return(0,P.jsx)(cf,{sections:a})}const hf="content",mf={name:"core/pattern-overrides",getValues({select:e,clientId:t,context:s,bindings:o}){const n=s["pattern/overrides"],{getBlockAttributes:i}=e(m.store),r=i(t),a={};for(const e of Object.keys(o)){const t=n?.[r?.metadata?.name]?.[e];void 0!==t?a[e]=""===t?void 0:t:a[e]=r[e]}return a},setValues({select:e,dispatch:t,clientId:s,bindings:o}){const{getBlockAttributes:n,getBlockParentsByBlockName:i,getBlocks:r}=e(m.store),a=n(s),l=a?.metadata?.name;if(!l)return;const[c]=i(s,"core/block",!0),d=Object.entries(o).reduce(((e,[t,{newValue:s}])=>(e[t]=s,e)),{});if(!c){const e=s=>{for(const o of s)o.attributes?.metadata?.name===l&&t(m.store).updateBlockAttributes(o.clientId,d),e(o.innerBlocks)};return void e(r())}const u=n(c)?.[hf];t(m.store).updateBlockAttributes(c,{[hf]:{...u,[l]:{...u?.[l],...Object.entries(d).reduce(((e,[t,s])=>(e[t]=void 0===s?"":s,e)),{})}}})},canUserEditValue:()=>!0};function gf(e,t){const{getEditedEntityRecord:s}=e(d.store),{getRegisteredPostMeta:o}=sn(e(d.store));let n;t?.postType&&t?.postId&&(n=s("postType",t?.postType,t?.postId).meta);const i=o(t?.postType),r={};return Object.entries(i||{}).forEach((([e,t])=>{var s;"footnotes"!==e&&"_"!==e.charAt(0)&&(r[e]={label:t.title||e,value:null!==(s=n?.[e])&&void 0!==s?s:t.default||void 0,type:t.type})})),Object.keys(r||{}).length?r:null}const _f={name:"core/post-meta",getValues({select:e,context:t,bindings:s}){const o=gf(e,t),n={};for(const[e,t]of Object.entries(s)){var i;const s=t.args.key,{value:r,label:a}=o?.[s]||{};n[e]=null!==(i=null!=r?r:a)&&void 0!==i?i:s}return n},setValues({dispatch:e,context:t,bindings:s}){const o={};Object.values(s).forEach((({args:e,newValue:t})=>{o[e.key]=t})),e(d.store).editEntityRecord("postType",t?.postType,t?.postId,{meta:o})},canUserEditValue({select:e,context:t,args:s}){if(t?.query||t?.queryId)return!1;if("wp_template"===(t?.postType||e(qi).getCurrentPostType()))return!1;const o=gf(e,t)?.[s.key]?.value;if(void 0===o)return!1;if(e(qi).getEditorSettings().enableCustomFields)return!1;return!!e(d.store).canUser("update",{kind:"postType",name:t?.postType,id:t?.postId})},getFieldsList:({select:e,context:t})=>gf(e,t)};const{store:ff,...bf}=l,yf={};function xf(e,t,s){const{registerEntityAction:o}=sn((0,c.dispatch)(qi))}function vf(e,t,s){const{unregisterEntityAction:o}=sn((0,c.dispatch)(qi))}tn(yf,{CreateTemplatePartModal:Wo,BackButton:ag,EntitiesSavedStatesExtensible:Va,Editor:tf,EditorContentSlotFill:sg,GlobalStylesProvider:function({children:e}){const t=Vp();return t.isReady?(0,P.jsx)(Op.Provider,{value:t,children:e}):null},mergeBaseAndUserConfigs:Fp,PluginPostExcerpt:Ec,PostCardPanel:v_,PreferencesModal:function({extraSections:e={}}){const t=(0,c.useSelect)((e=>e(Nr).isModalActive("editor/preferences")),[]),{closeModal:s}=(0,c.useDispatch)(Nr);return t?(0,P.jsx)(lf,{closeModal:s,children:(0,P.jsx)(pf,{extraSections:e})}):null},usePostActions:m_,ToolsMoreMenuGroup:xg,ViewMoreMenuGroup:Sg,ResizableEditor:Kg,registerCoreBlockBindingsSources:function(){(0,y.registerBlockBindingsSource)(mf),(0,y.registerBlockBindingsSource)(_f)},interfaceStore:ff,...bf})})(),(window.wp=window.wp||{}).editor=o})();