<?php
/**
 * Plugin Name: Petting Zoo Importer
 * Description: Custom importer for petting zoo data from JSON files
 * Version: 1.0.0
 * Author: Custom Development
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PettingZooImporter {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_simulate_import', array($this, 'simulate_import'));
        add_action('wp_ajax_process_import', array($this, 'process_import'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=petting_zoo',
            'Import Petting Zoos',
            'Import Data',
            'manage_options',
            'petting-zoo-importer',
            array($this, 'admin_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'petting_zoo_page_petting-zoo-importer') {
            return;
        }
        
        wp_enqueue_script('petting-zoo-importer-js', 
            plugin_dir_url(__FILE__) . 'assets/importer.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('petting-zoo-importer-js', 'importerAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('petting_zoo_importer_nonce')
        ));
        
        wp_enqueue_style('petting-zoo-importer-css',
            plugin_dir_url(__FILE__) . 'assets/importer.css',
            array(),
            '1.0.0'
        );
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>Petting Zoo Data Importer</h1>
            <p>Import petting zoo data from JSON files. Use "Simulate" to test the import without making changes, then "Import" to actually import the data.</p>
            
            <div class="importer-container">
                <div class="file-selection">
                    <h2>Select JSON File</h2>
                    <input type="file" id="json-file" accept=".json" />
                    <p class="description">Select a JSON file containing petting zoo data.</p>
                </div>
                
                <div class="import-actions">
                    <button id="simulate-btn" class="button button-secondary" disabled>
                        <span class="dashicons dashicons-search"></span>
                        Simulate Import
                    </button>
                    <button id="import-btn" class="button button-primary" disabled>
                        <span class="dashicons dashicons-download"></span>
                        Import Data
                    </button>
                </div>
                
                <div class="import-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">Processing...</div>
                </div>
                
                <div class="import-log">
                    <h3>Import Log</h3>
                    <div id="log-content">
                        <p>No import activity yet.</p>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function simulate_import() {
        check_ajax_referer('petting_zoo_importer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $json_data = json_decode(stripslashes($_POST['json_data']), true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error('Invalid JSON data: ' . json_last_error_msg());
        }
        
        $log = array();
        $errors = array();
        $warnings = array();
        
        // Validate JSON structure
        if (!isset($json_data['petting_zoos']) || !is_array($json_data['petting_zoos'])) {
            wp_send_json_error('JSON must contain a "petting_zoos" array');
        }
        
        $log[] = 'Starting simulation for ' . count($json_data['petting_zoos']) . ' petting zoos...';
        
        foreach ($json_data['petting_zoos'] as $index => $zoo_data) {
            $zoo_log = $this->validate_zoo_data($zoo_data, $index + 1);
            $log = array_merge($log, $zoo_log['messages']);
            
            if (!empty($zoo_log['errors'])) {
                $errors = array_merge($errors, $zoo_log['errors']);
            }
            
            if (!empty($zoo_log['warnings'])) {
                $warnings = array_merge($warnings, $zoo_log['warnings']);
            }
        }
        
        $log[] = 'Simulation completed.';
        $log[] = 'Total errors: ' . count($errors);
        $log[] = 'Total warnings: ' . count($warnings);
        
        wp_send_json_success(array(
            'log' => $log,
            'errors' => $errors,
            'warnings' => $warnings,
            'can_import' => empty($errors)
        ));
    }
    
    public function process_import() {
        check_ajax_referer('petting_zoo_importer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $json_data = json_decode(stripslashes($_POST['json_data']), true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error('Invalid JSON data: ' . json_last_error_msg());
        }
        
        $log = array();
        $imported = 0;
        $updated = 0;
        $errors = array();
        
        $log[] = 'Starting import for ' . count($json_data['petting_zoos']) . ' petting zoos...';
        
        foreach ($json_data['petting_zoos'] as $index => $zoo_data) {
            $result = $this->import_zoo($zoo_data, $index + 1);
            $log = array_merge($log, $result['messages']);
            
            if ($result['success']) {
                if ($result['action'] === 'created') {
                    $imported++;
                } else {
                    $updated++;
                }
            } else {
                $errors = array_merge($errors, $result['errors']);
            }
        }
        
        $log[] = 'Import completed.';
        $log[] = 'Created: ' . $imported . ' petting zoos';
        $log[] = 'Updated: ' . $updated . ' petting zoos';
        $log[] = 'Errors: ' . count($errors);
        
        wp_send_json_success(array(
            'log' => $log,
            'imported' => $imported,
            'updated' => $updated,
            'errors' => $errors
        ));
    }
    
    private function validate_zoo_data($zoo_data, $index) {
        $messages = array();
        $errors = array();
        $warnings = array();
        
        $messages[] = "Validating zoo #{$index}...";
        
        // Required fields
        $required_fields = array('name', 'description');
        foreach ($required_fields as $field) {
            if (empty($zoo_data[$field])) {
                $errors[] = "Zoo #{$index}: Missing required field '{$field}'";
            }
        }
        
        // Validate name uniqueness
        if (!empty($zoo_data['name'])) {
            $existing = get_page_by_title($zoo_data['name'], OBJECT, 'petting_zoo');
            if ($existing) {
                $warnings[] = "Zoo #{$index}: '{$zoo_data['name']}' already exists (will be updated)";
            }
        }
        
        // Validate coordinates
        if (isset($zoo_data['latitude']) && isset($zoo_data['longitude'])) {
            if (!is_numeric($zoo_data['latitude']) || !is_numeric($zoo_data['longitude'])) {
                $errors[] = "Zoo #{$index}: Invalid coordinates";
            }
        }
        
        // Validate taxonomies
        $taxonomies = array('location', 'animal_type', 'zoo_type', 'features', 'event_type');
        foreach ($taxonomies as $taxonomy) {
            if (isset($zoo_data[$taxonomy]) && !is_array($zoo_data[$taxonomy])) {
                $errors[] = "Zoo #{$index}: '{$taxonomy}' must be an array";
            }
        }
        
        if (empty($errors)) {
            $messages[] = "Zoo #{$index}: Validation passed";
        }
        
        return array(
            'messages' => $messages,
            'errors' => $errors,
            'warnings' => $warnings
        );
    }
    
    private function import_zoo($zoo_data, $index) {
        $messages = array();
        $errors = array();
        
        try {
            $messages[] = "Importing zoo #{$index}: {$zoo_data['name']}...";
            
            // Check if zoo already exists
            $existing = get_page_by_title($zoo_data['name'], OBJECT, 'petting_zoo');
            $action = 'created';
            
            if ($existing) {
                // Delete existing zoo data
                wp_delete_post($existing->ID, true);
                $messages[] = "Deleted existing zoo data for '{$zoo_data['name']}'";
                $action = 'updated';
            }
            
            // Create new post
            $post_data = array(
                'post_title' => sanitize_text_field($zoo_data['name']),
                'post_content' => wp_kses_post($zoo_data['description']),
                'post_status' => 'publish',
                'post_type' => 'petting_zoo',
                'post_excerpt' => isset($zoo_data['excerpt']) ? sanitize_text_field($zoo_data['excerpt']) : ''
            );
            
            $post_id = wp_insert_post($post_data);
            
            if (is_wp_error($post_id)) {
                $errors[] = "Failed to create post: " . $post_id->get_error_message();
                return array('success' => false, 'messages' => $messages, 'errors' => $errors);
            }
            
            // Save meta fields
            $meta_fields = array('address', 'phone', 'website', 'hours', 'admission', 'latitude', 'longitude');
            foreach ($meta_fields as $field) {
                if (isset($zoo_data[$field])) {
                    update_post_meta($post_id, '_petting_zoo_' . $field, sanitize_text_field($zoo_data[$field]));
                }
            }
            
            // Handle taxonomies
            $taxonomies = array('location', 'animal_type', 'zoo_type', 'features', 'event_type');
            foreach ($taxonomies as $taxonomy) {
                if (isset($zoo_data[$taxonomy]) && is_array($zoo_data[$taxonomy])) {
                    $term_ids = array();
                    
                    foreach ($zoo_data[$taxonomy] as $term_name) {
                        $term = get_term_by('name', $term_name, $taxonomy);
                        
                        if (!$term) {
                            // Create term if it doesn't exist
                            $new_term = wp_insert_term($term_name, $taxonomy);
                            if (!is_wp_error($new_term)) {
                                $term_ids[] = $new_term['term_id'];
                                $messages[] = "Created new {$taxonomy} term: {$term_name}";
                            }
                        } else {
                            $term_ids[] = $term->term_id;
                        }
                    }
                    
                    if (!empty($term_ids)) {
                        wp_set_post_terms($post_id, $term_ids, $taxonomy);
                    }
                }
            }
            
            // Handle featured image if provided
            if (isset($zoo_data['featured_image']) && !empty($zoo_data['featured_image'])) {
                $this->handle_featured_image($post_id, $zoo_data['featured_image']);
            }
            
            $messages[] = "Successfully {$action} zoo: {$zoo_data['name']} (ID: {$post_id})";
            
            return array(
                'success' => true,
                'action' => $action,
                'messages' => $messages,
                'errors' => $errors,
                'post_id' => $post_id
            );
            
        } catch (Exception $e) {
            $errors[] = "Exception during import: " . $e->getMessage();
            return array('success' => false, 'messages' => $messages, 'errors' => $errors);
        }
    }
    
    private function handle_featured_image($post_id, $image_url) {
        // This is a simplified version - you might want to enhance this
        // to handle local files or validate image URLs
        if (filter_var($image_url, FILTER_VALIDATE_URL)) {
            update_post_meta($post_id, '_featured_image_url', $image_url);
        }
    }
}

// Initialize the plugin
new PettingZooImporter();
