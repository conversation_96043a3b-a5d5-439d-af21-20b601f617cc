import*as e from"@wordpress/interactivity";var t={317:e=>{e.exports=import("@wordpress/a11y")}},o={};function i(e){var n=o[e];if(void 0!==n)return n.exports;var a=o[e]={exports:{}};return t[e](a,a.exports,i),a.exports}i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};(()=>{i.d(n,{o:()=>P,w:()=>S});const t=(e=>{var t={};return i.d(t,e),t})({getConfig:()=>e.getConfig,privateApis:()=>e.privateApis,store:()=>e.store});var o;const{directivePrefix:a,getRegionRootFragment:r,initialVdom:s,toVdom:d,render:l,parseServerData:c,populateServerData:g,batch:p}=(0,t.privateApis)("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."),u=null!==(o=(0,t.getConfig)("core/router").navigationMode)&&void 0!==o?o:"regionBased",w=new Map,h=(new Map,e=>{const t=new URL(e,window.location.href);return t.pathname+t.search}),f=async(e,{vdom:t}={})=>{const o={body:void 0};if("regionBased"===u){const i=`data-${a}-router-region`;e.querySelectorAll(`[${i}]`).forEach((e=>{const n=e.getAttribute(i);o[n]=t?.has(e)?t.get(e):d(e)}))}const i=e.querySelector("title")?.innerText,n=c(e);return{regions:o,head:undefined,title:i,initialData:n}},v=e=>{p((()=>{if("regionBased"===u){g(e.initialData);const t=`data-${a}-router-region`;document.querySelectorAll(`[${t}]`).forEach((o=>{const i=o.getAttribute(t),n=r(o);l(e.regions[i],n)}))}e.title&&(document.title=e.title)}))},m=e=>(window.location.assign(e),new Promise((()=>{})));window.addEventListener("popstate",(async()=>{const e=h(window.location.href),t=w.has(e)&&await w.get(e);t?(v(t),S.url=window.location.href):window.location.reload()})),w.set(h(window.location.href),Promise.resolve(f(document,{vdom:s})));let y="",x=!1;const b={loading:"Loading page, please wait.",loaded:"Page Loaded."},{state:S,actions:P}=(0,t.store)("core/router",{state:{url:window.location.href,navigation:{hasStarted:!1,hasFinished:!1}},actions:{*navigate(e,o={}){const{clientNavigationDisabled:i}=(0,t.getConfig)();i&&(yield m(e));const n=h(e),{navigation:a}=S,{loadingAnimation:r=!0,screenReaderAnnouncement:s=!0,timeout:d=1e4}=o;y=e,P.prefetch(n,o);const l=new Promise((e=>setTimeout(e,d))),c=setTimeout((()=>{y===e&&(r&&(a.hasStarted=!0,a.hasFinished=!1),s&&A("loading"))}),400),g=yield Promise.race([w.get(n),l]);if(clearTimeout(c),y===e)if(g&&!g.initialData?.config?.["core/router"]?.clientNavigationDisabled){yield v(g),window.history[o.replace?"replaceState":"pushState"]({},"",e),S.url=e,r&&(a.hasStarted=!1,a.hasFinished=!0),s&&A("loaded");const{hash:t}=new URL(e,window.location.href);t&&document.querySelector(t)?.scrollIntoView()}else yield m(e)},prefetch(e,o={}){const{clientNavigationDisabled:i}=(0,t.getConfig)();if(i)return;const n=h(e);!o.force&&w.has(n)||w.set(n,(async(e,{html:t})=>{try{if(!t){const o=await window.fetch(e);if(200!==o.status)return!1;t=await o.text()}const o=(new window.DOMParser).parseFromString(t,"text/html");return f(o)}catch(e){return!1}})(n,{html:o.html}))}}});function A(e){if(!x){x=!0;const e=document.getElementById("wp-script-module-data-@wordpress/interactivity-router")?.textContent;if(e)try{const t=JSON.parse(e);"string"==typeof t?.i18n?.loading&&(b.loading=t.i18n.loading),"string"==typeof t?.i18n?.loaded&&(b.loaded=t.i18n.loaded)}catch{}else S.navigation.texts?.loading&&(b.loading=S.navigation.texts.loading),S.navigation.texts?.loaded&&(b.loaded=S.navigation.texts.loaded)}const t=b[e];Promise.resolve().then(i.bind(i,317)).then((({speak:e})=>e(t)),(()=>{}))}})();var a=n.o,r=n.w;export{a as actions,r as state};