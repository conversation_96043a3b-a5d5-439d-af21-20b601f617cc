<?php
/**
 * Single Petting Zoo Template
 */

get_header(); ?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                
                <!-- Hero Section with Featured Image -->
                <div class="zoo-hero">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="zoo-featured-image">
                            <?php the_post_thumbnail('full'); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="container">
                        <div class="zoo-hero-content">
                            <h1 class="zoo-title"><?php the_title(); ?></h1>
                            
                            <?php
                            $address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                            if ($address) : ?>
                                <div class="zoo-address">
                                    <span class="address-icon">📍</span>
                                    <?php echo esc_html($address); ?>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Quick Info Bar -->
                            <div class="zoo-quick-info">
                                <?php
                                $phone = get_post_meta(get_the_ID(), '_petting_zoo_phone', true);
                                $website = get_post_meta(get_the_ID(), '_petting_zoo_website', true);
                                
                                if ($phone) : ?>
                                    <a href="tel:<?php echo esc_attr($phone); ?>" class="quick-info-item">
                                        <span class="icon">📞</span>
                                        <?php echo esc_html($phone); ?>
                                    </a>
                                <?php endif;
                                
                                if ($website) : ?>
                                    <a href="<?php echo esc_url($website); ?>" target="_blank" class="quick-info-item">
                                        <span class="icon">🌐</span>
                                        Visit Website
                                    </a>
                                <?php endif; ?>
                                
                                <button class="quick-info-item directions-btn" onclick="getDirections()">
                                    <span class="icon">🗺️</span>
                                    Get Directions
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container">
                    <div class="zoo-content-wrapper">
                        
                        <!-- Main Content -->
                        <div class="zoo-main-content">
                            
                            <!-- Description -->
                            <section class="zoo-description">
                                <h2>About <?php the_title(); ?></h2>
                                <div class="content">
                                    <?php the_content(); ?>
                                </div>
                            </section>

                            <!-- Animals Section -->
                            <?php
                            $animals = get_the_terms(get_the_ID(), 'animal_type');
                            if ($animals && !is_wp_error($animals)) : ?>
                                <section class="zoo-animals">
                                    <h2>Animals You Can Meet</h2>
                                    <div class="animal-tags">
                                        <?php foreach ($animals as $animal) : ?>
                                            <a href="<?php echo get_term_link($animal); ?>" class="animal-tag">
                                                <?php echo esc_html($animal->name); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </section>
                            <?php endif; ?>

                            <!-- Features Section -->
                            <?php
                            $features = get_the_terms(get_the_ID(), 'features');
                            if ($features && !is_wp_error($features)) : ?>
                                <section class="zoo-features">
                                    <h2>Features & Amenities</h2>
                                    <div class="features-grid">
                                        <?php foreach ($features as $feature) : ?>
                                            <div class="feature-item">
                                                <span class="feature-icon">✅</span>
                                                <?php echo esc_html($feature->name); ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </section>
                            <?php endif; ?>

                            <!-- Events Section -->
                            <?php
                            $events = get_the_terms(get_the_ID(), 'event_type');
                            if ($events && !is_wp_error($events)) : ?>
                                <section class="zoo-events">
                                    <h2>Special Events & Services</h2>
                                    <div class="events-list">
                                        <?php foreach ($events as $event) : ?>
                                            <div class="event-item">
                                                <span class="event-icon">🎉</span>
                                                <strong><?php echo esc_html($event->name); ?></strong>
                                                <?php if ($event->description) : ?>
                                                    <p><?php echo esc_html($event->description); ?></p>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </section>
                            <?php endif; ?>

                        </div>

                        <!-- Sidebar -->
                        <div class="zoo-sidebar">
                            
                            <!-- Hours & Admission -->
                            <div class="sidebar-widget">
                                <h3>Visit Information</h3>
                                
                                <?php
                                $hours = get_post_meta(get_the_ID(), '_petting_zoo_hours', true);
                                if ($hours) : ?>
                                    <div class="info-section">
                                        <h4>Hours</h4>
                                        <div class="hours-info">
                                            <?php echo nl2br(esc_html($hours)); ?>
                                        </div>
                                    </div>
                                <?php endif;
                                
                                $admission = get_post_meta(get_the_ID(), '_petting_zoo_admission', true);
                                if ($admission) : ?>
                                    <div class="info-section">
                                        <h4>Admission</h4>
                                        <div class="admission-info">
                                            <?php echo nl2br(esc_html($admission)); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Location Info -->
                            <?php
                            $locations = get_the_terms(get_the_ID(), 'location');
                            if ($locations && !is_wp_error($locations)) : ?>
                                <div class="sidebar-widget">
                                    <h3>Location</h3>
                                    <?php foreach ($locations as $location) : ?>
                                        <a href="<?php echo get_term_link($location); ?>" class="location-link">
                                            📍 More petting zoos in <?php echo esc_html($location->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Zoo Type -->
                            <?php
                            $zoo_types = get_the_terms(get_the_ID(), 'zoo_type');
                            if ($zoo_types && !is_wp_error($zoo_types)) : ?>
                                <div class="sidebar-widget">
                                    <h3>Zoo Type</h3>
                                    <?php foreach ($zoo_types as $type) : ?>
                                        <a href="<?php echo get_term_link($type); ?>" class="zoo-type-link">
                                            🏛️ <?php echo esc_html($type->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Map -->
                            <?php
                            $latitude = get_post_meta(get_the_ID(), '_petting_zoo_latitude', true);
                            $longitude = get_post_meta(get_the_ID(), '_petting_zoo_longitude', true);
                            
                            if ($latitude && $longitude) : ?>
                                <div class="sidebar-widget">
                                    <h3>Map</h3>
                                    <div class="zoo-map" id="zoo-map" 
                                         data-lat="<?php echo esc_attr($latitude); ?>" 
                                         data-lng="<?php echo esc_attr($longitude); ?>"
                                         data-title="<?php echo esc_attr(get_the_title()); ?>">
                                        <!-- Map will be loaded here via JavaScript -->
                                        <div class="map-placeholder">
                                            <p>Interactive map loading...</p>
                                            <a href="https://maps.google.com/?q=<?php echo esc_attr($latitude); ?>,<?php echo esc_attr($longitude); ?>" 
                                               target="_blank" class="btn">
                                                View on Google Maps
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>

                <!-- Related Petting Zoos -->
                <section class="related-zoos section">
                    <div class="container">
                        <h2>Other Petting Zoos You Might Like</h2>
                        
                        <?php
                        // Get related zoos based on location or animal types
                        $related_args = array(
                            'post_type' => 'petting_zoo',
                            'posts_per_page' => 3,
                            'post__not_in' => array(get_the_ID()),
                            'meta_query' => array(
                                'relation' => 'OR',
                            )
                        );
                        
                        // Add location-based relation
                        if ($locations) {
                            $location_ids = wp_list_pluck($locations, 'term_id');
                            $related_args['tax_query'] = array(
                                array(
                                    'taxonomy' => 'location',
                                    'field' => 'term_id',
                                    'terms' => $location_ids,
                                )
                            );
                        }
                        
                        $related_query = new WP_Query($related_args);
                        
                        if ($related_query->have_posts()) : ?>
                            <div class="zoo-grid">
                                <?php while ($related_query->have_posts()) : $related_query->the_post(); ?>
                                    <div class="zoo-card">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('medium'); ?>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <div class="zoo-card-content">
                                            <h3><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                            
                                            <?php
                                            $related_address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                                            if ($related_address) : ?>
                                                <div class="location"><?php echo esc_html($related_address); ?></div>
                                            <?php endif; ?>
                                            
                                            <div class="excerpt">
                                                <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                            </div>
                                            
                                            <a href="<?php the_permalink(); ?>" class="btn">Learn More</a>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php endif;
                        wp_reset_postdata(); ?>
                    </div>
                </section>

            </article>

        <?php endwhile; ?>

    </main>
</div>

<script>
function getDirections() {
    <?php if ($latitude && $longitude) : ?>
        const lat = <?php echo esc_js($latitude); ?>;
        const lng = <?php echo esc_js($longitude); ?>;
        const url = `https://maps.google.com/maps?daddr=${lat},${lng}`;
        window.open(url, '_blank');
    <?php else : ?>
        <?php if ($address) : ?>
            const address = "<?php echo esc_js($address); ?>";
            const url = `https://maps.google.com/maps?daddr=${encodeURIComponent(address)}`;
            window.open(url, '_blank');
        <?php endif; ?>
    <?php endif; ?>
}
</script>

<?php get_footer(); ?>
